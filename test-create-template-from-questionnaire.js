/**
 * Standalone test script for createTemplateFromQuestionnaire function
 * This script can be run independently to test the functionality
 * 
 * Usage: node test-create-template-from-questionnaire.js
 */

import dotenv from 'dotenv';
import { templateService } from './app/services/index.js';
import { questionaireService } from './app/services/index.js';

// Load environment variables
dotenv.config();

async function testCreateTemplateFromQuestionnaire() {
  console.log('🧪 Testing createTemplateFromQuestionnaire function...\n');

  try {
    // Step 1: Get list of available questionnaires
    console.log('📋 Fetching available questionnaires...');
    const questionnaires = await questionaireService.getAllQuestionaire(1, 5, null, 1);
    
    if (!questionnaires.questionaire || questionnaires.questionaire.length === 0) {
      console.log('❌ No questionnaires found. Please create a questionnaire first.');
      return;
    }

    console.log(`✅ Found ${questionnaires.questionaire.length} questionnaires:`);
    questionnaires.questionaire.forEach((qtn, index) => {
      console.log(`   ${index + 1}. ${qtn.qtn_name} (ID: ${qtn.id})`);
    });

    // Step 2: Use the first questionnaire for testing
    const testQuestionnaire = questionnaires.questionaire[0];
    console.log(`\n🎯 Using questionnaire: "${testQuestionnaire.qtn_name}" (ID: ${testQuestionnaire.id})`);

    // Step 3: Get detailed questionnaire data
    console.log('\n📖 Fetching detailed questionnaire data...');
    const questionnaireData = await questionaireService.getQuestionaire(testQuestionnaire.id);
    
    if (!questionnaireData || questionnaireData.length === 0) {
      console.log('❌ Failed to fetch questionnaire details.');
      return;
    }

    console.log(`✅ Questionnaire details loaded:`);
    console.log(`   - Name: ${questionnaireData.name}`);
    console.log(`   - Description: ${questionnaireData.description}`);
    console.log(`   - Groups: ${questionnaireData.groups?.length || 0}`);
    console.log(`   - Price: ${questionnaireData.price}`);

    // Step 4: Create template from questionnaire
    const templateName = `Template from ${questionnaireData.name} - ${new Date().toISOString().slice(0, 19)}`;
    const templateDesc = `Auto-generated template from questionnaire: ${questionnaireData.description}`;
    const testEmail = '<EMAIL>'; // You may need to change this to a valid admin email

    console.log(`\n🏗️  Creating template...`);
    console.log(`   - Template Name: ${templateName}`);
    console.log(`   - Template Description: ${templateDesc}`);
    console.log(`   - Admin Email: ${testEmail}`);

    const newTemplateId = await templateService.createTemplateFromQuestionnaire(
      testQuestionnaire.id,
      templateName,
      templateDesc,
      testEmail
    );

    console.log(`\n🎉 SUCCESS! Template created successfully!`);
    console.log(`   - New Template ID: ${newTemplateId}`);

    // Step 5: Verify the created template
    console.log('\n🔍 Verifying created template...');
    const createdTemplate = await templateService.getTemplate(newTemplateId);
    
    if (createdTemplate && createdTemplate.length > 0) {
      console.log(`✅ Template verification successful:`);
      console.log(`   - Template ID: ${createdTemplate.id}`);
      console.log(`   - Template Name: ${createdTemplate.name}`);
      console.log(`   - Groups: ${createdTemplate.groups?.length || 0}`);
      console.log(`   - Questions: ${createdTemplate.groups?.reduce((total, group) => total + (group.questions?.length || 0), 0) || 0}`);
      
      // Check if IDs are different (regenerated)
      const originalGroupIds = questionnaireData.groups?.map(g => g.id) || [];
      const newGroupIds = createdTemplate.groups?.map(g => g.id) || [];
      
      const idsRegenerated = originalGroupIds.length > 0 && newGroupIds.length > 0 && 
                            !originalGroupIds.some(id => newGroupIds.includes(id));
      
      console.log(`   - IDs Regenerated: ${idsRegenerated ? '✅ YES' : '❌ NO'}`);
      
      if (idsRegenerated) {
        console.log(`   - Original Group IDs: ${originalGroupIds.slice(0, 3).join(', ')}${originalGroupIds.length > 3 ? '...' : ''}`);
        console.log(`   - New Group IDs: ${newGroupIds.slice(0, 3).join(', ')}${newGroupIds.length > 3 ? '...' : ''}`);
      }
    } else {
      console.log('❌ Template verification failed - could not retrieve created template');
    }

    console.log('\n✨ Test completed successfully!');

  } catch (error) {
    console.error('\n❌ Test failed with error:');
    console.error(`   Error: ${error.message}`);
    console.error(`   Stack: ${error.stack}`);
  }
}

// Run the test
if (import.meta.url === `file://${process.argv[1]}`) {
  testCreateTemplateFromQuestionnaire()
    .then(() => {
      console.log('\n🏁 Test script finished.');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Test script failed:', error);
      process.exit(1);
    });
}

export { testCreateTemplateFromQuestionnaire };
