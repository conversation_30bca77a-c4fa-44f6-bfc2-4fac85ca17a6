import { jest } from '@jest/globals';

describe('Email Uniqueness Validation', () => {
  let mockRunQuery;
  let mockPool;

  beforeAll(async () => {
    // Mock database pool and queries
    mockRunQuery = jest.fn();
    
    mockPool = {
      getConnection: jest.fn().mockResolvedValue({
        beginTransaction: jest.fn().mockResolvedValue(),
        commit: jest.fn().mockResolvedValue(),
        rollback: jest.fn().mockResolvedValue(),
        destroy: jest.fn().mockResolvedValue()
      })
    };

    // Mock the database connection
    jest.unstable_mockModule('../app/config/db.js', () => ({
      default: mockPool
    }));

    jest.unstable_mockModule('../app/utils/index.js', () => ({
      runQuery: mockRunQuery,
      passwordHashing: jest.fn().mockResolvedValue('hashed_password')
    }));
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('addUser Email Uniqueness', () => {
    test('should prevent adding user with duplicate email in same law firm', async () => {
      // Mock email check to return existing user
      mockRunQuery.mockResolvedValueOnce([{ COUNT: 1 }]); // Email exists

      const userService = await import('../app/services/user.service.js');

      try {
        await userService.addUser(
          'Individual',
          'Test Org',
          '<EMAIL>',
          'password123',
          '4', // Client role
          1,
          123, // lf_id
          'Mr',
          'John',
          null,
          'Doe',
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          1, // user_id
          null
        );

        // Should not reach here
        expect(true).toBe(false);
      } catch (error) {
        expect(error.message).toContain('A user with this email already exists in this law firm');
        console.log('✅ Duplicate email in same law firm correctly rejected');
      }

      // Verify email check was called
      expect(mockRunQuery).toHaveBeenCalledWith(
        expect.any(Object),
        "SELECT COUNT(*) COUNT FROM users WHERE email = ? AND lf_id = ?",
        ['<EMAIL>', 123]
      );
    });

    test('should allow adding user with same email in different law firm', async () => {
      // Mock email check to return no existing user in this law firm
      mockRunQuery
        .mockResolvedValueOnce([{ COUNT: 0 }]) // Email check - no duplicate
        .mockResolvedValueOnce({ insertId: 456 }) // addUser result
        .mockResolvedValueOnce({ insertId: 789 }); // addUserInfo result

      const userService = await import('../app/services/user.service.js');

      try {
        const result = await userService.addUser(
          'Individual',
          'Test Org',
          '<EMAIL>',
          'password123',
          '4', // Client role
          1,
          124, // Different lf_id
          'Mr',
          'John',
          null,
          'Doe',
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          1, // user_id
          null
        );

        expect(result).toBeDefined();
        console.log('✅ Same email in different law firm correctly allowed');
      } catch (error) {
        console.log('❌ Should have allowed same email in different law firm');
        throw error;
      }

      // Verify email check was called with correct law firm ID
      expect(mockRunQuery).toHaveBeenCalledWith(
        expect.any(Object),
        "SELECT COUNT(*) COUNT FROM users WHERE email = ? AND lf_id = ?",
        ['<EMAIL>', 124]
      );
    });

    test('should allow adding user with unique email in same law firm', async () => {
      // Mock email check to return no existing user
      mockRunQuery
        .mockResolvedValueOnce([{ COUNT: 0 }]) // Email check - no duplicate
        .mockResolvedValueOnce({ insertId: 456 }) // addUser result
        .mockResolvedValueOnce({ insertId: 789 }); // addUserInfo result

      const userService = await import('../app/services/user.service.js');

      try {
        const result = await userService.addUser(
          'Individual',
          'Test Org',
          '<EMAIL>',
          'password123',
          '4', // Client role
          1,
          123, // Same lf_id as first test
          'Mr',
          'Jane',
          null,
          'Smith',
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          1, // user_id
          null
        );

        expect(result).toBeDefined();
        console.log('✅ Unique email in same law firm correctly allowed');
      } catch (error) {
        console.log('❌ Should have allowed unique email');
        throw error;
      }
    });
  });

  describe('saveClientFromPMS Email Uniqueness', () => {
    test('should prevent adding PMS client with duplicate email in same law firm', async () => {
      // Mock email check to return existing user
      mockRunQuery.mockResolvedValueOnce([{ COUNT: 1 }]); // Email exists

      const userService = await import('../app/services/user.service.js');

      try {
        await userService.saveClientFromPMS(
          'Individual',
          'Test Org',
          '<EMAIL>',
          'password123',
          123, // lf_id
          'Mr',
          'John',
          null,
          'Doe',
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          'PMS123'
        );

        // Should not reach here
        expect(true).toBe(false);
      } catch (error) {
        expect(error.message).toContain('A user with this email already exists in this law firm');
        console.log('✅ Duplicate email for PMS client correctly rejected');
      }
    });
  });

  describe('Email Validation Logic', () => {
    test('should verify email uniqueness check query format', () => {
      const expectedQuery = "SELECT COUNT(*) COUNT FROM users WHERE email = ? AND lf_id = ?";
      const testEmail = "<EMAIL>";
      const testLfId = 123;
      
      // This would be the actual query parameters
      const queryParams = [testEmail, testLfId];
      
      expect(queryParams).toEqual([testEmail, testLfId]);
      console.log('✅ Email uniqueness query format is correct');
      console.log(`   Query: ${expectedQuery}`);
      console.log(`   Params: [${queryParams.join(', ')}]`);
    });

    test('should handle case-insensitive email comparison', () => {
      // The email should be converted to lowercase before checking
      const emails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      ];
      
      emails.forEach(email => {
        const lowerEmail = email.toLowerCase();
        expect(lowerEmail).toBe('<EMAIL>');
      });
      
      console.log('✅ Email case normalization works correctly');
    });
  });
});
