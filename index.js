import logger from "#logger";
import app from "#app";
import pool from "./app/config/db.js";
import http from "http";
import { Server } from "socket.io";
import jwt from "jsonwebtoken";

const containerId = process.env.HOSTNAME;

const server = http.createServer(app);
const io = new Server(server, { cors: { origin: "*" } });
const notificationsNamespace = io.of("/public/noti/notification");
const connectedUsers = {};
notificationsNamespace
  .use((socket, next) => {
    const token = socket.handshake.headers.authorization; // get token
    if (!token) return next(new Error("Authentication error"));
    //cut Bearer
    // const token_ = token.includes("Bearer ") ? token.split(" ")[1] : token;
    jwt.verify(token, process.env.JWT_SECRET, (err, decoded) => {
      if (err) {
        return next(new Error("Authentication error"));
      }
      const user_id = decoded.user_id;
      connectedUsers[user_id] = socket;
      socket.userId = user_id;
    });
    next();
  })
  .on("connect", (socket) => {
    console.log(`${socket.id}`);
    // console.log(Object.keys(connectedUsers).length);
    socket.emit("notification", "connected");
    socket.on("disconnect", () => {
      delete connectedUsers[socket.userId];
      console.log(`${socket.id} disconnected`);
    });
  });

const PORT = process.env.PORT || 5000;
if (process.env.NODE_ENV !== "test") {
  server.listen(PORT, () => {
    logger.info(`Server is listening on port ${PORT}`);
  });
}

process.on("SIGINT", function () {
  pool.end();
  logger.info(`Terminated (SIGINT): ${containerId}`);
  process.exit(0);
});

process.on("SIGTERM", () => {
  pool.end();
  logger.info(`Terminated (SIGTERM): ${containerId}`);
  process.exit(0);
});

export { connectedUsers, server, io, notificationsNamespace };
