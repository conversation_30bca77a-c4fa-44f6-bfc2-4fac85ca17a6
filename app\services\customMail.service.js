import { runQuery } from "#utils";
import pool from "../config/db.js";
import ErrorHandler from "#middlewares/ErrorClass.js";
import { s3Client } from "../config/s3.js";
import { S3SyncClient } from "s3-sync-client";
import * as fs from "fs";

export const editTemplateMail = async (
  logo_name,
  title,
  header,
  body,
  footer,
  logo,
  lf_id,
  mail_type = 1,
  remind_time
) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    await runQuery(
      con,
      "INSERT INTO template_email(title,header,body,footer,logo,logo_name,last_modified,lf_id, mail_type, remind_time) VALUES(?,?,?,?,?,?,NOW(),?, ?, ?) ON DUPLICATE KEY UPDATE title = ?, header = ?, body = ?, footer = ?, logo = ?, logo_name = ?, last_modified = NOW(), remind_time = ?",
      [
        title,
        header,
        body,
        footer,
        logo,
        logo_name,
        lf_id,
        mail_type,
        remind_time,
        title,
        header,
        body,
        footer,
        logo,
        logo_name,
        remind_time,
      ]
    );
    await con.commit();
    return 1;
  } catch (error) {
    await con.rollback();
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const getTemplateMail = async (lf_id, mail_type) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    const lf = await runQuery(
      con,
      "SELECT path FROM law_firm WHERE lf_id = ?",
      [lf_id]
    );
    if (!lf.length) {
      throw ErrorHandler.badRequestError("Lawfirm not found");
    }
    let data = await runQuery(
      con,
      "SELECT title, header, body, footer, logo, logo_name, last_modified, remind_time FROM template_email WHERE lf_id = ? and mail_type = ?",
      [lf_id, mail_type]
    );
    await con.commit();
    return {
      title: data[0]?.title,
      header: data[0]?.header,
      body: data[0]?.body,
      footer: data[0]?.footer,
      logo: data[0]?.logo,
      logo_name: data[0]?.logo_name ?? lf[0]?.path,
      last_modified: data[0]?.last_modified,
      remind_time: data[0]?.remind_time,
    };
  } catch (error) {
    await con.rollback();
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const uploadLogo = async (lf_id, file) => {
  try {
    const buffer = Buffer.from(file, "base64");
    const { sync } = new S3SyncClient({ client: s3Client });
    let path = `static/images/${lf_id}-logo.jpg`;
    fs.writeFileSync(path, buffer);
    await sync("static", process.env.S3_PATH);
    return path;
  } catch (error) {
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  }
};

export const getStatement = async (lf_id) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    let data = await runQuery(
      con,
      "SELECT lf_statement FROM law_firm WHERE lf_id = ?",
      [lf_id]
    );
    await con.commit();
    return data[0]?.lf_statement;
  } catch (error) {
    await con.rollback();
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const editStatement = async (lf_id, lf_statement) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    await runQuery(
      con,
      "UPDATE law_firm SET lf_statement = ? WHERE lf_id = ?",
      [lf_statement, lf_id]
    );
    await con.commit();
    return 1;
  } catch (error) {
    await con.rollback();
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};
