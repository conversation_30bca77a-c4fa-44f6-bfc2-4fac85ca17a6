import express from "express";
import { sendTempPassword, getMailType } from "#controllers/mail.controller.js";

const mailRouter = express.Router();

/**
 * @swagger
 * tags:
 *   name: Mail
 *   description: Email management endpoints
 */

/**
 * @swagger
 * /private/mail/sendTempPassword:
 *   post:
 *     summary: Send temporary password to user's email
 *     tags: [Mail]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *               - first_name
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 description: User's email address
 *               password:
 *                 type: string
 *                 description: Temporary password to send
 *               first_name:
 *                 type: string
 *                 description: User's first name for email personalization
 *     responses:
 *       200:
 *         description: Temporary password sent successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Password sent"
 *       400:
 *         description: Error sending temporary password
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Error sending temp password"
 *       500:
 *         description: Unauthorized access
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Unauthorized"
 */
mailRouter.route("/sendTempPassword").post(sendTempPassword);

/**
 * @swagger
 * /private/mail/getMailType:
 *   get:
 *     summary: Get available mail types
 *     tags: [Mail]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Mail types retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Mail type fetched"
 *                 mailType:
 *                   type: array
 *                   items:
 *                     type: object
 */
mailRouter.route("/getMailType").get(getMailType);

export default mailRouter;
