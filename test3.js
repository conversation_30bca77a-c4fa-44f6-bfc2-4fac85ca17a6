import axios from 'axios';
//const clientData = require('../SQL');
import sysSet from "../../appSetting.json";
import fs from 'fs';
import path from 'path';
import OpenAI from 'openai';
const openai = new OpenAI({
    apiKey: "********************************************************************************************************************************************************************",
});

const GetWebPage = async (req, res, next) => {

    let HTML = `<!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Form Page</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 20px;
                position: relative;
            }
            .top-buttons {
                display: flex;
                justify-content: flex-end;
                gap: 10px;
            }
            .top-buttons button {
                padding: 10px;
                border: none;
                border-radius: 5px;
                cursor: pointer;
            }
            .red-btn { background-color: red; color: white; }
            .blue-btn { background-color: blue; color: white; }
            .dark-btn { background-color: darkcyan; color: white; }
            
            .tabs {
                display: flex;
                gap: 10px;
                margin-top: 10px;
            }
            .tab {
                background-color: red;
                color: white;
                padding: 10px;
                border-radius: 5px;
            }
            .form-container {
                border: 1px solid #ccc;
                padding: 20px;
                margin-top: 10px;
            }
            .question {
                margin-bottom: 10px;
                display: flex;
                align-items: center;
            }
            .options label {
                display: block;
            }
            .tooltip-icon {
                margin-left: 10px;
                cursor: pointer;
                color: blue;
            }
            .overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                visibility: hidden;
            }
            .overlay-content {
                background: white;
                padding: 20px;
                border-radius: 5px;
                text-align: center;
                max-width: 80vw;
                max-height: 80vh;
                box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.3);
                overflow-y: auto;
            }
            .overlay button {
                margin-top: 10px;
                padding: 10px;
                border: none;
                cursor: pointer;
                background: red;
                color: white;
                border-radius: 5px;
            }
            .overlay-content p, .overlay-content h1, .overlay-content h2, 
            .overlay-content h3, .overlay-content h4, .overlay-content h5, 
            .overlay-content ul, .overlay-content ol, .overlay-content li {
                font-family: Arial, sans-serif;
                color: #333;
                text-align: left;
                margin: 10px 0;
            }
            .overlay-content ul, .overlay-content ol {
                padding-left: 20px;
            }
            .overlay-content li {
                margin-bottom: 5px;
                line-height: 1.5;
            }
        </style>
        <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
        <script>
            function showTooltip(question, details) {
                $.ajax({
                    url: '/tooltip-info',
                    method: 'GET',
                    data: { question: question, casetype: will },
                    success: function(response) {
                        document.getElementById('overlay-text').innerHTML = response;
                        document.getElementById('overlay').style.visibility = 'visible';
                    },
                    error: function(error) {
                        console.error('Error fetching tooltip info:', error);
                    }
                });
            }
            function closeOverlay() {
                document.getElementById('overlay').style.visibility = 'hidden';
            }
        </script>
    </head>
    <body>
        <div class="top-buttons">
            <button class="blue-btn">Take a Tour</button>
            <button class="red-btn">Call me</button>
            <button class="dark-btn">Save and Exit</button>
            <button class="dark-btn">Save</button>
        </div>
        
        <div class="tabs">
            <div class="tab">Client 1</div>
            <div class="tab">Executors</div>
            <div class="tab">Children</div>
            <div class="tab">Client 2</div>
        </div>
        
        <div class="form-container">
            <p><strong>Please confirm your personal details</strong></p>
            <p><a href="#">Click to answer</a></p>
            
            <div class="question1">
                <p><strong>How would you wish your body to be disposed of?</strong></p>
                <span class="tooltip-icon" onclick="showTooltip('How would you wish your body to be disposed of?', 'Choose between Burial or Cremation')">&#9432;</span>
            </div>
            <div class="options">
                <label><input type="radio" name="disposal" value="burial"> Burial</label>
                <label><input type="radio" name="disposal" value="cremation"> Cremation</label>
            </div>
            
            <div class="question2">
                <p><strong>Do you wish to make any cash gifts?</strong></p>
                <span class="tooltip-icon" onclick="showTooltip('Do you wish to make any cash gifts?', 'Choose Yes or No for cash gifts preference')">&#9432;</span>
            </div>
            <div class="options">
                <label><input type="radio" name="cash_gift" value="yes"> Yes</label>
                <label><input type="radio" name="cash_gift" value="no"> No</label>
            </div>
        </div>
        
        <div id="overlay" class="overlay">
            <div class="overlay-content">
                <p id="overlay-text"></p>
                <button onclick="closeOverlay()">Close</button>
            </div>
        </div>
    </body>
    </html>`
    res.send(HTML)
}

const GetWebPageLPA = async (req, res, next) => {

    let HTML = `<!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>LPA Form</title>
        <script>
            function showSection(sectionId) {
                document.querySelectorAll('.section').forEach(section => {
                    section.style.display = 'none';
                });
                document.getElementById(sectionId).style.display = 'block';
            }
            // this where the detaills this send for the tooltip 
            async function sendQuestion(question) {
                let storedResponse = sessionStorage.getItem(question);
                if (storedResponse) {
                    showOverlay(storedResponse);
                    return;
                }
                showLoading();
                try {
                    //this is the fetches the data for the tooltip
                    let response = await fetch('http://tooltip.legalworkflow.com/tooltip-info', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ question: question, casetype: "LPA", extra: "do not reference any information from a will case type. Use the guidance notes in Form LP12 in www.gov.uk for advice on questions about lasting powers of attorney.  For example, a question about when attorneys can act could use the content under the heading 'Section 5'." })
                    });
                    let data = await response.text();
                    //console.log(data)
                    //console.log(data.status)
                    //console.log("Test - "+data.content)
                    //console.log("Test1 - "+data.body)
                    // this this the method i use to store the data but their are better way to do this in react
                    sessionStorage.setItem(question, data);
                    // push the data to the overlay
                    showOverlay(data);
                } catch (error) {
                    console.error('Error:', error);
                } finally {
                    hideLoading();
                }
            }
            
            function showOverlay(message) {
                let overlay = document.getElementById('overlay');
                let overlayText = document.getElementById('overlay-text');
                overlayText.innerHTML = message;
                overlay.style.display = 'block';
                overlay.style.visibility = 'visible';
            }
            
            function closeOverlay() {
                document.getElementById('overlay').style.display = 'none';
            }

            function showLoading() {
                document.getElementById('loading').style.display = 'block';
            }
            
            function hideLoading() {
                document.getElementById('loading').style.display = 'none';
            }
        </script>
        <style>
            body {
                font-family: Arial, sans-serif;
            }
            .top-buttons {
                display: flex;
                justify-content: flex-end;
                gap: 10px;
                margin-bottom: 10px;
            }
            .top-buttons button {
                padding: 5px 10px;
                border: none;
                border-radius: 5px;
                color: white;
                cursor: pointer;
            }
            .tour { background-color: blue; }
            .call { background-color: red; }
            .save-exit { background-color: darkgreen; }
            .save { background-color: darkslategray; }
            .tabs {
                display: flex;
                margin-bottom: 10px;
            }
            .tab {
                padding: 10px;
                cursor: pointer;
                margin-right: 5px;
                border-radius: 5px;
                color: white;
            }
            .tab.completed { background-color: green; }
            .tab.in-progress { background-color: orange; }
            .tab.not-started { background-color: red; }
            .section {
                display: none;
                padding: 10px;
                border: 1px solid #ccc;
                border-radius: 5px;
            }
            .info-icon {
                display: inline-block;
                width: 20px;
                height: 20px;
                border-radius: 50%;
                background-color: #007BFF;
                color: white;
                text-align: center;
                font-weight: bold;
                cursor: pointer;
                line-height: 20px;
                margin-left: 10px;
            }
            .overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                visibility: hidden;
            }
            .overlay-content {
                background: white;
                padding: 20px;
                border-radius: 5px;
                text-align: center;
                max-width: 80vw;
                max-height: 80vh;
                box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.3);
                overflow-y: auto;
                margin: auto;
                margin-top: 5em;
            }
            .overlay button {
                margin-top: 10px;
                padding: 10px;
                border: none;
                cursor: pointer;
                background: red;
                color: white;
                border-radius: 5px;
            }
            .overlay-content p, .overlay-content h1, .overlay-content h2, 
            .overlay-content h3, .overlay-content h4, .overlay-content h5, 
            .overlay-content ul, .overlay-content ol, .overlay-content li {
                font-family: Arial, sans-serif;
                color: #333;
                text-align: left;
                margin: 10px 0;
            }
            .overlay-content ul, .overlay-content ol {
                padding-left: 20px;
            }
            .overlay-content li {
                margin-bottom: 5px;
                line-height: 1.5;
            }
            .section li {
                display:block;
                padding-top:1em;
            }
            .section input,.section select{
                margin-left: 0.5em;
            }
            #loading {
                display: none;
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0, 0, 0, 0.7);
                color: white;
                padding: 20px;
                border-radius: 5px;
                text-align: center;
            }
        </style>
    </head>
    <body onload="showSection('personal_details');closeOverlay()">
        <h1>Lasting Power of Attorney (LPA) Form</h1>
        <div class="top-buttons">
            <button class="tour">Take a Tour</button>
            <button class="call">Call me</button>
            <button class="save-exit">Save and Exit</button>
            <button class="save">Save</button>
        </div>
        
        <div class="tabs">
            <div class="tab completed" onclick="showSection('personal_details')">Personal Details</div>
            <div class="tab in-progress" onclick="showSection('husband_details')">Your Husband</div>
            <div class="tab not-started" onclick="showSection('lpa_details')">LPA Details</div>
            <div class="tab completed" onclick="showSection('your_attorneys')">Your Attorneys</div>
            <div class="tab completed" onclick="showSection('husband_attorneys')">Husband's Attorneys</div>
            <div class="tab not-started" onclick="showSection('certificate_provider')">Certificate Provider</div>
            <div class="tab not-started" onclick="showSection('husband_certificate_provider')">Husband's Certificate Provider</div>
        </div>
        
              
        <div id="personal_details" class="section">
            <h3>Personal Details</h3>
            <label>Are you making LPAs for just yourself or for you and someone else?</label>
            <span class="info-icon" onclick="sendQuestion('Are you making LPAs for just yourself or for you and someone else?')">i</span>
            <input type="text">
        </div>
        <div id="husband_details" class="section">
            <h3>Your Husband</h3>
            <label>Please provide your husband's contact details</label>
            <span class="info-icon" onclick="sendQuestion('Please provide your husbands contact details')">i</span>
            <input type="text">
        </div>
    
    <div id="lpa_details" class="section">
        
        <h3>LPA Details</h3>
        <ul>
        <li>
        <label>What type of LPAs do you wish to make?</label>
        <span class="info-icon" onclick="sendQuestion('What type of LPAs do you wish to make?')">i</span>
        <input type="text">
        </li>
        <li>
        <label>How are attorneys to make decisions?</label>
        <span class="info-icon" onclick="sendQuestion('How are attorneys to make decisions?')">i</span>
        <input type="text">
        </li>
        <li>
        <label>When do you want attorneys to be able to make decisions?</label>
        <span class="info-icon" onclick="sendQuestion('When do you want attorneys to be able to make decisions?')">i</span>
        <input type="text">
        </li>    
    </div>

    <div id="your_attorneys" class="section">
        
        <h3>Your Attorneys</h3>
        <ul>
        <li>
        <label>Do you wish to appoint your husband as one of your attorneys?</label>
        <span class="info-icon" onclick="sendQuestion('Do you wish to appoint your husband as one of your attorneys?')">i</span>
        <select name="cars" id="cars">
            <option>Yes</option>
            <option>No</option>
        </select>
        </li>
        <li>
        <label>Please add any other attorneys you wish to appoint</label>
        <span class="info-icon" onclick="sendQuestion('Please add any other attorneys you wish to appoint')">i</span>
        <input type="text">
        </li>
        <li>
        <label>When do you want attorneys to be able to make decisions?</label>
        <span class="info-icon" onclick="sendQuestion('When do you want attorneys to be able to make decisions?')">i</span>
        <input type="text">
        </li>    
        <li>
        <label>Please provide details of your replacement attorneys</label>
        <span class="info-icon" onclick="sendQuestion('Please provide details of your replacement attorneys')">i</span>
        <input type="text">
        </li>  
    </div>
    <div id="husband_attorneys" class="section">
        
        <h3>Husband's Attorneys</h3>
        <ul>
        <li>
        <label>Are you to be appointed as attorney for your husband?</label>
        <span class="info-icon" onclick="sendQuestion('Are you to be appointed as attorney for your husband?')">i</span>
        <select name="cars" id="cars">
            <option>Yes</option>
            <option>No</option>
        </select>
        </li>
        <li>
        <label>Who are to be your husband's other attorneys?</label>
        <span class="info-icon" onclick="sendQuestion('Who are to be your husband's other attorneys?')">i</span>
        <input type="text">
        </li>
        <li>
        <label>Does your husband wish to appoint replacement attorneys?</label>
        <span class="info-icon" onclick="sendQuestion('Does your husband wish to appoint replacement attorneys?')">i</span>
        <input type="text">
        </li>    
        <li>
        <label>Who are to be appointed your husband's replacement attorneys?</label>
        <span class="info-icon" onclick="sendQuestion('Who are to be appointed your husband's replacement attorneys?')">i</span>
        <input type="text">
        </li>  
    </div>
    <div id="certificate_provider" class="section">
            <h3>Certificate Provider</h3>
            <label>Who is to be your certificate provider?</label>
            <span class="info-icon" onclick="sendQuestion('Who is to be your certificate provider?')">i</span>
            <input type="text">
        </div>
        <div id="husband_certificate_provider" class="section">
            <h3>Husband's Certificate Provider</h3>
            <label>Who is to be your husband's certificate provider?</label>
            <span class="info-icon" onclick="sendQuestion('Who is to be your husband's certificate provider?')">i</span>
            <input type="text">
        </div>
    <div id="loading">Loading...</div>        
    <div id="overlay" class="overlay">
            <div class="overlay-content">
                <div id="overlay-text"></div>
                <button onclick="closeOverlay()">Close</button>
            </div>
        </div>
    </body>
    </html>
    `
    res.send(HTML)

}

const GetWebPageTA6 = async (req, res, next) => {

    let HTML = `<!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Property Form</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                background: #f9f9f9;
                padding: 20px;
            }
            .container {
                max-width: 90vw;
                background: white;
                padding: 20px;
                border-radius: 5px;
                box-shadow: 0 0 10px rgba(0,0,0,0.1);
                margin: auto;
            }
            .header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 20px;
            }
            .top-buttons button {
                padding: 8px 12px;
                margin-left: 10px;
                border: none;
                color: white;
                border-radius: 3px;
                cursor: pointer;
            }
            .tabs {
                display: flex;
                overflow-x: auto;
                padding-bottom: 10px;
            }
            .tabs button {
                padding: 10px;
                background: #d00;
                color: white;
                border: none;
                cursor: pointer;
                margin-right: 5px;
                border-radius: 3px;
                max-width: 10em;
            }
            .tabs button.active {
                background: #900;
            }
            .tab-content {
                display: none;
            }
            .tab-content.active {
                display: block;
            }
            .tour { background-color: blue; }
            .call { background-color: red; }
            .save-exit { background-color: darkgreen; }
            .save { background-color: darkslategray; }
            .question {
                display: flex;
                align-items: center;
                margin-bottom: 15px;
            }
            .question label {
                flex: 1;
            }
            .eye-icon {
                cursor: pointer;
                margin-right: 10px;
                color: #007BFF;
            }
            .input-field {
                flex: 2;
                padding: 5px;
                border: 1px solid #ccc;
                border-radius: 3px;
            }
            .overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                visibility: hidden;
            }
            .overlay-content {
                background: white;
                padding: 20px;
                border-radius: 5px;
                text-align: center;
                max-width: 80vw;
                max-height: 80vh;
                box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.3);
                overflow-y: auto;
                margin: auto;
                margin-top: 5em;
            }
            .overlay button {
                margin-top: 10px;
                padding: 10px;
                border: none;
                cursor: pointer;
                background: red;
                color: white;
                border-radius: 5px;
            }
            .overlay-content p, .overlay-content h1, .overlay-content h2, 
            .overlay-content h3, .overlay-content h4, .overlay-content h5, 
            .overlay-content ul, .overlay-content ol, .overlay-content li {
                font-family: Arial, sans-serif;
                color: #333;
                text-align: left;
                margin: 10px 0;
            }
            .overlay-content ul, .overlay-content ol {
                padding-left: 20px;
            }
            .overlay-content li {
                margin-bottom: 5px;
                line-height: 1.5;
            }
            .section li {
                display:block;
                padding-top:1em;
            }
            .section input,.section select{
                margin-left: 0.5em;
            }
            #loading {
                display: none;
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0, 0, 0, 0.7);
                color: white;
                padding: 20px;
                border-radius: 5px;
                text-align: center;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h2>Property Form</h2>
                <div class="top-buttons">
                    <button class="tour">Take a Tour</button>
                    <button class="call">Call me</button>
                    <button class="save-exit">Save and Exit</button>
                    <button class="save">Save</button>
                </div>
            </div>
            <div class="tabs" id="tabs-container">
                <button class="tab-button active" data-tab="property">Property and Seller</button>
                <button class="tab-button" data-tab="boundaries">Boundaries</button>
                <button class="tab-button" data-tab="disputes">Disputes</button>
                <button class="tab-button" data-tab="notices">Notices and Proposals</button>
                <button class="tab-button" data-tab="alterations">Alterations, Planning and Building Control</button>
                <button class="tab-button" data-tab="warranties">Guarantees and Warranties</button>
                <button class="tab-button" data-tab="insurance">Insurance</button>
                <button class="tab-button" data-tab="flooding">Flooding</button>
                <button class="tab-button" data-tab="radon">Radon</button>
                <button class="tab-button" data-tab="energy">Energy Efficiency</button>
                <button class="tab-button" data-tab="knotweed">Japanese Knotweed</button>
                <button class="tab-button" data-tab="rights">Rights and Informal Arrangements</button>
                <button class="tab-button" data-tab="services">Services Crossing Land</button>
                <button class="tab-button" data-tab="parking">Parking</button>
                <button class="tab-button" data-tab="charges">Other Charges</button>
                <button class="tab-button" data-tab="occupiers">Occupiers</button>
                <button class="tab-button" data-tab="services_property">Services at the Property</button>
                <button class="tab-button" data-tab="utilities">Utilities</button>
                <button class="tab-button" data-tab="transaction">Transaction Information</button>
            </div>
    
            <div id="property" class="tab-content active">
                <div class="question">
                <label>Property house and street:</label>
                <input type="text" class="input-field" placeholder="Enter house and street">
                </div>
                <div class="question">
                    <label>Property town/city:</label>
                    <input type="text" class="input-field" placeholder="Enter town/city">
                </div>
                <div class="question">
                    <label>Property postcode:</label>
                    <input type="text" class="input-field" placeholder="Enter postcode">
                </div>
                <div class="question">
                    <label>Your name:</label>
                    <input type="text" class="input-field" placeholder="Enter your name">
                </div>
                <div class="question">
                    <label>Sellers solicitors:</label>
                    <input type="text" class="input-field" placeholder="Enter solicitor's name">
                </div>
                <div class="question">
                    <label>Solicitors address line 1:</label>
                    <input type="text" class="input-field" placeholder="Enter solicitor's address">
                </div>
                <div class="question">
                    <label>Solicitor town/city:</label>
                    <input type="text" class="input-field" placeholder="Enter solicitor's town/city">
                </div>
                <div class="question">
                    <label>Solicitor postcode:</label>
                    <input type="text" class="input-field" placeholder="Enter solicitor's postcode">
                </div>
            </div>
            <div id="boundaries" class="tab-content">
                <!-- Additional HTML to insert into the "Boundaries" tab -->
                <div class="question">
                    <label>Do you know who owns or accepts responsibility to maintain or repair any of the boundary features?</label>
                    <span class="eye-icon" onclick="sendQuestion('Do you know who owns or accepts responsibility to maintain or repair any of the boundary features?')">&#128065;</span>
                    <select class="input-field">
                        <option>Yes</option>
                        <option>No</option>
                    </select>
                </div>
                <div class="question">
                    <label>Are the boundaries irregular?</label>
                    <span class="eye-icon" onclick="sendQuestion('Are the boundaries irregular?')">&#128065;</span>
                    <select class="input-field">
                        <option>Yes</option>
                        <option>No</option>
                        <option>Don't know</option>
                    </select>
                </div>
                <div class="question">
                    <label>Are you aware of any boundary feature having been removed in the last 10 years or during your ownership?</label>
                    <span class="eye-icon" onclick="sendQuestion('Are you aware of any boundary feature having been removed in the last 10 years or during your ownership?')">&#128065;</span>
                    <select class="input-field">
                        <option>Yes</option>
                        <option>No</option>
                    </select>
                </div>
                <div class="question">
                    <label>During your ownership, have you bought any adjacent land or property?</label>
                    <span class="eye-icon" onclick="sendQuestion('During your ownership, have you bought any adjacent land or property?')">&#128065;</span>
                    <select class="input-field">
                        <option>Yes</option>
                        <option>No</option>
                    </select>
                </div>
                <div class="question">
                    <label>Does any part of the property or any building on it overhang, or project under, the boundary?</label>
                    <span class="eye-icon" onclick="sendQuestion('Does any part of the property or any building on it overhang, or project under, the boundary?')">&#128065;</span>
                    <select class="input-field">
                        <option>Yes</option>
                        <option>No</option>
                    </select>
                </div>
                <div class="question">
                    <label>Please provide details of the overhanging or projection:</label>
                    <span class="eye-icon" onclick="sendQuestion('Please provide details of the overhanging or projection:')">&#128065;</span>
                    <input type="text" class="input-field" placeholder="Enter details">
                </div>
                <div class="question">
                    <label>Has any notice been received under the Party Wall Act 1996 in respect of any shared or party boundary?</label>
                    <span class="eye-icon" onclick="sendQuestion('Has any notice been received under the Party Wall Act 1996 in respect of any shared or party boundary?')">&#128065;</span>
                    <select class="input-field">
                        <option>Yes</option>
                        <option>No</option>
                    </select>
                </div>
            </div>
            <div id="disputes" class="tab-content">
                <!-- Additional HTML to insert into the "Disputes" tab -->
                <div class="question">
                    <label>Have there been any disputes or complaints regarding the property or a property nearby?</label>
                    <span class="eye-icon" onclick="sendQuestion('Have there been any disputes or complaints regarding the property or a property nearby?')">&#128065;</span>
                    <select class="input-field">
                        <option>Yes</option>
                        <option>No</option>
                    </select>
                </div>
                <div class="question">
                    <label>Are you aware of anything that might lead to a dispute about the property or a property nearby?</label>
                    <span class="eye-icon" onclick="sendQuestion('Are you aware of anything that might lead to a dispute about the property or a property nearby?')">&#128065;</span>
                    <select class="input-field">
                        <option>Yes</option>
                        <option>No</option>
                    </select>
                </div>
            </div>
            <div id="notices" class="tab-content">
            <!-- Additional HTML to insert into the "Notices and Proposals" tab -->
                <div class="question">
                    <label>Have any notices or correspondence been received or sent, or any negotiations or discussions taken place?</label>
                    <span class="eye-icon" onclick="sendQuestion('Have any notices or correspondence been received or sent, or any negotiations or discussions taken place?')">&#128065;</span>
                    <select class="input-field">
                        <option>Yes</option>
                        <option>No</option>
                    </select>
                </div>
                <div class="question">
                    <label>Are you aware of any proposals to develop property or land nearby or of any proposals to make alterations?</label>
                    <span class="eye-icon" onclick="sendQuestion('Are you aware of any proposals to develop property or land nearby or of any proposals to make alterations?')">&#128065;</span>
                    <select class="input-field">
                        <option>Yes</option>
                        <option>No</option>
                    </select>
                </div>
            </div>
            <div id="alterations" class="tab-content">
                <!-- Additional HTML to insert into the "Alterations, Planning and Building Control" tab -->
                <div class="question">
                    <label>Have any building works been done to the whole or any part of the property, including the garden?</label>
                    <span class="eye-icon" onclick="sendQuestion('Have any building works been done to the whole or any part of the property, including the garden?')">&#128065;</span>
                    <select class="input-field">
                        <option>Yes</option>
                        <option>No</option>
                    </select>
                </div>
                <div class="question">
                    <label>Has there been a change of use of the whole or any part of the property, including the garden?</label>
                    <span class="eye-icon" onclick="sendQuestion('Has there been a change of use of the whole or any part of the property, including the garden?')">&#128065;</span>
                    <select class="input-field">
                        <option>Yes</option>
                        <option>No</option>
                    </select>
                </div>
                <div class="question">
                    <label>Have there been replacements or modifications installed in the whole or any part of the property, including the garden?</label>
                    <span class="eye-icon" onclick="sendQuestion('Have there been replacements or modifications installed in the whole or any part of the property, including the garden?')">&#128065;</span>
                    <select class="input-field">
                        <option>Yes</option>
                        <option>No</option>
                    </select>
                </div>
                <div class="question">
                    <label>Has a conservatory been added to all or any part of the property, including the garden?</label>
                    <span class="eye-icon" onclick="sendQuestion('Has a conservatory been added to all or any part of the property, including the garden?')">&#128065;</span>
                    <select class="input-field">
                        <option>Yes</option>
                        <option>No</option>
                    </select>
                </div>
                <div class="question">
                    <label>Was planning permission or building regulation approval required for any of the works or changes?</label>
                    <span class="eye-icon" onclick="sendQuestion('Was planning permission or building regulation approval required for any of the works or changes?')">&#128065;</span>
                    <select class="input-field">
                        <option>Yes</option>
                        <option>No</option>
                    </select>
                </div>
                <div class="question">
                    <label>Have solar panels been installed?</label>
                    <span class="eye-icon" onclick="sendQuestion('Have solar panels been installed?')">&#128065;</span>
                    <select class="input-field">
                        <option>Yes</option>
                        <option>No</option>
                    </select>
                </div>
                <div class="question">
                    <label>Is the property or any part of it a listed building?</label>
                    <span class="eye-icon" onclick="sendQuestion('Is the property or any part of it a listed building?')">&#128065;</span>
                    <select class="input-field">
                        <option>Yes</option>
                        <option>No</option>
                    </select>
                </div>
                <div class="question">
                    <label>Is the property or any part of it in a conservation area?</label>
                    <span class="eye-icon" onclick="sendQuestion('Is the property or any part of it in a conservation area?')">&#128065;</span>
                    <select class="input-field">
                        <option>Yes</option>
                        <option>No</option>
                    </select>
                </div>
                <div class="question">
                    <label>Are any of the trees on the property subject to a Tree Preservation Order?</label>
                    <span class="eye-icon" onclick="sendQuestion('Are any of the trees on the property subject to a Tree Preservation Order?')">&#128065;</span>
                    <select class="input-field">
                        <option>Yes</option>
                        <option>No</option>
                        <option>Not known</option>
                    </select>
                </div>

            </div>
            <div class="tab-content" id="warranties">
                <div class="question">
                <label for="new-home-warranty">Does the property benefit from a new home warranty, such as NHBC?</label>
                <span class="eye-icon" onclick="sendQuestion('Does the property benefit from a new home warranty, such as NHBC?')">&#128065;</span>
                <select class="input-field">
                    <option>Select an option</option>
                    <option>Yes</option>
                    <option>No</option>
                </select>
                </div>
                
                <div class="question">
                    <label for="damp-proof">Does the property benefit from a damp proof guarantee or warranty?</label>
                    <span class="eye-icon" onclick="sendQuestion('Does the property benefit from a damp proof guarantee or warranty?')">&#128065;</span>
                    <select class="input-field">
                        <option>Select an option</option>
                        <option>Yes</option>
                        <option>No</option>
                    </select>
                </div>
                
                <div class="question">
                    <label for="timber-treatment">Does the property benefit from a timber treatment guarantee or warranty?</label>
                    <span class="eye-icon" onclick="sendQuestion('Does the property benefit from a timber treatment guarantee or warranty?')">&#128065;</span>
                    <select class="input-field">
                        <option>Select an option</option>
                        <option>Yes</option>
                        <option>No</option>
                    </select>
                </div>
                
                <div class="question">
                    <label for="windows-roof">Does the property benefit from a guarantee or warranty for windows, roof lights, roof windows, or glazing?</label>
                    <span class="eye-icon" onclick="sendQuestion('Does the property benefit from a guarantee or warranty for windows, roof lights, roof windows, or glazing?')">&#128065;</span>
                    <select class="input-field">
                        <option>Select an option</option>
                        <option>Yes</option>
                        <option>No</option>
                    </select>
                </div>
                
                <div class="question">
                    <label for="electrical-work">Does the property benefit from a guarantee or warranty for electrical work?</label>
                    <span class="eye-icon" onclick="sendQuestion('Does the property benefit from a guarantee or warranty for electrical work?')">&#128065;</span>
                    <select class="input-field">
                        <option>Select an option</option>
                        <option>Yes</option>
                        <option>No</option>
                    </select>
                </div>
                
                <div class="question">
                    <label for="roofing">Does the property benefit from a roofing guarantee or warranty?</label>
                    <span class="eye-icon" onclick="sendQuestion('Does the property benefit from a roofing guarantee or warranty?')">&#128065;</span>
                    <select class="input-field">
                        <option>Select an option</option>
                        <option>Yes</option>
                        <option>No</option>
                    </select>
                </div>
                
                <div class="question">
                    <label for="central-heating">Does the property benefit from a central heating guarantee or warranty?</label>
                    <span class="eye-icon" onclick="sendQuestion('Does the property benefit from a central heating guarantee or warranty?')">&#128065;</span>
                    <select class="input-field">
                        <option>Select an option</option>
                        <option>Yes</option>
                        <option>No</option>
                    </select>
                </div>
                
                <div class="question">
                    <label for="underpinning">Does the property benefit from a guarantee or warranty for underpinning?</label>
                    <span class="eye-icon" onclick="sendQuestion('Does the property benefit from a guarantee or warranty for underpinning?')">&#128065;</span>
                    <select class="input-field">
                        <option>Select an option</option>
                        <option>Yes</option>
                        <option>No</option>
                    </select>
                </div>
                
                <div class="question">
                    <label for="other-warranty">Does the property benefit from a guarantee or warranty for anything else?</label>
                    <span class="eye-icon" onclick="sendQuestion('Does the property benefit from a guarantee or warranty for anything else?')">&#128065;</span>
                    <select class="input-field">
                        <option>Select an option</option>
                        <option>Yes</option>
                        <option>No</option>
                    </select>
                </div>
            </div>
            <div class="tab-content" id="insurance">
                <div class="question">
                <label for="propertyInsured">Do you insure the property?</label>
                <span class="eye-icon" onclick="sendQuestion('Do you insure the property?')">&#128065;</span>
                <select class="input-field">
                    <option>Select an option</option>
                    <option>Yes</option>
                    <option>No</option>
                </select>
                </div>
            
                <div class="question">
                    <label for="insurancePremiumRise">Has any buildings insurance taken out by you ever been subject to an abnormal rise in premiums?</label>
                    <span class="eye-icon" onclick="sendQuestion('Has any buildings insurance taken out by you ever been subject to an abnormal rise in premiums?')">&#128065;</span>
                    <select class="input-field">
                        <option>Select an option</option>
                        <option>Yes</option>
                        <option>No</option>
                    </select>
                </div>
            
                <div class="question">
                    <label for="insuranceHighExcess">Has any buildings insurance taken out by you ever been subject to high excesses?</label>
                    <span class="eye-icon" onclick="sendQuestion('Has any buildings insurance taken out by you ever been subject to high excesses?')">&#128065;</span>
                    <select class="input-field">
                        <option>Select an option</option>
                        <option>Yes</option>
                        <option>No</option>
                    </select>
                </div>
            
                <div class="question">
                    <label for="insuranceUnusualConditions">Has any buildings insurance taken out by you ever been subject to unusual conditions?</label>
                    <span class="eye-icon" onclick="sendQuestion('Has any buildings insurance taken out by you ever been subject to unusual conditions?')">&#128065;</span>
                    <select class="input-field">
                        <option>Select an option</option>
                        <option>Yes</option>
                        <option>No</option>
                    </select>
                </div>
            
                <div class="question">
                    <label for="insuranceRefused">Has any buildings insurance taken out by you ever been refused?</label>
                    <span class="eye-icon" onclick="sendQuestion('Has any buildings insurance taken out by you ever been refused?')">&#128065;</span>
                    <select class="input-field">
                        <option>Select an option</option>
                        <option>Yes</option>
                        <option>No</option>
                    </select>
                </div>
            
                <div class="question">
                    <label for="insuranceClaims">Have you made any buildings insurance claims?</label>
                    <span class="eye-icon" onclick="sendQuestion('Have you made any buildings insurance claims?')">&#128065;</span>
                    <select class="input-field">
                        <option>Select an option</option>
                        <option>Yes</option>
                        <option>No</option>
                    </select>
                </div>
            </div>
            <div class="tab-content" id="flooding">
                <div class="question">
                <label for="propertyFlooded">Has any part of the property (whether buildings or surrounding garden or land) ever been flooded?</label>
                <span class="eye-icon" onclick="sendQuestion('Has any part of the property (whether buildings or surrounding garden or land) ever been flooded?')">&#128065;</span>
                <select id="propertyFlooded" name="propertyFlooded" class="input-field">
                    <option value="">Select an option</option>
                    <option value="Yes">Yes</option>
                    <option value="No">No</option>
                </select>
                </div>

                <div class="question">
                    <label for="floodRiskReport">Has a flood risk report been prepared?</label>
                    <span class="eye-icon" onclick="sendQuestion('Has a flood risk report been prepared?')">&#128065;</span>
                    <select id="floodRiskReport" name="floodRiskReport" class="input-field">
                        <option value="">Select an option</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                    </select>
                </div>
            </div>
            <div class="tab-content" id="radon">
                <div class="question">
                <label for="radonTest">Has a Radon test been carried out on the property?</label>
                <span class="eye-icon" onclick="sendQuestion('Has a Radon test been carried out on the property?')">&#128065;</span>
                <select id="radonTest" name="radonTest" class="input-field">
                    <option value="">Select an option</option>
                    <option value="Yes">Yes</option>
                    <option value="No">No</option>
                </select>
                </div>

                <div class="question">
                    <label for="radonMeasures">Were any remedial measures undertaken on construction to reduce Radon gas levels in the property?</label>
                    <span class="eye-icon" onclick="sendQuestion('Were any remedial measures undertaken on construction to reduce Radon gas levels in the property?')">&#128065;</span>
                    <select id="radonMeasures" name="radonMeasures" class="input-field">
                        <option value="">Select an option</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                        <option value="Not known">Not known</option>
                    </select>
                </div>
            </div>
            <div class="tab-content" id="energy">
                <div class="question">
                <label for="epcCopy">Do you have a copy of the Energy Performance Certificate (EPC) for the property?</label>
                <span class="eye-icon" onclick="sendQuestion('Do you have a copy of the Energy Performance Certificate (EPC) for the property?')">&#128065;</span>
                <select id="epcCopy" name="epcCopy" class="input-field">
                    <option value="">Select an option</option>
                    <option value="Yes">Yes</option>
                    <option value="No">No</option>
                </select>
                </div>

                <div class="question">
                    <label for="greenDeal">Have any installations in the property been financed under the Green Deal scheme?</label>
                    <span class="eye-icon" onclick="sendQuestion('Have any installations in the property been financed under the Green Deal scheme?')">&#128065;</span>
                    <select id="greenDeal" name="greenDeal" class="input-field">
                        <option value="">Select an option</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                    </select>
                </div>
            </div>
            <div class="tab-content" id="knotweed">
                <div class="question">
                <label for="japaneseKnotweed">Is the property affected by Japanese knotweed?</label>
                <span class="eye-icon" onclick="sendQuestion('Is the property affected by Japanese knotweed?')">&#128065;</span>
                <select id="japaneseKnotweed" name="japaneseKnotweed" class="input-field">
                    <option value="">Select an option</option>
                    <option value="Yes">Yes</option>
                    <option value="No">No</option>
                </select>
                </div>  
            </div>
            <div class="tab-content" id="rights">
                <div class="question">
                <label for="jointlyUsedServices">Does ownership of the property carry a responsibility to contribute towards the cost of any jointly used services, such as maintenance of a private road, a shared driveway, a boundary or a drain?</label>
                <span class="eye-icon" onclick="sendQuestion('Does ownership of the property carry a responsibility to contribute towards the cost of any jointly used services, such as maintenance of a private road, a shared driveway, a boundary or a drain?')">&#128065;</span>
                <select id="jointlyUsedServices" name="jointlyUsedServices" class="input-field">
                    <option value="">Select an option</option>
                    <option value="Yes">Yes</option>
                    <option value="No">No</option>
                </select>
                </div>

                <div class="question">
                    <label for="rightsOverNeighbours">Does the property benefit from any rights or arrangements over any neighbouring property (including rights of way)?</label>
                    <span class="eye-icon" onclick="sendQuestion('Does the property benefit from any rights or arrangements over any neighbouring property (including rights of way)?')">&#128065;</span>
                    <select id="rightsOverNeighbours" name="rightsOverNeighbours" class="input-field">
                        <option value="">Select an option</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                    </select>
                </div>

                <div class="question">
                    <label for="preventedAccess">Has anyone taken steps to prevent access to the property, or to complain about or demand payment for access to the property?</label>
                    <span class="eye-icon" onclick="sendQuestion('Has anyone taken steps to prevent access to the property, or to complain about or demand payment for access to the property?')">&#128065;</span>
                    <select id="preventedAccess" name="preventedAccess" class="input-field">
                        <option value="">Select an option</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                    </select>
                </div>

                <div class="question">
                    <label for="rightToLight">Do you know if a right to light benefits the property?</label>
                    <span class="eye-icon" onclick="sendQuestion('Do you know if a right to light benefits the property?')">&#128065;</span>
                    <select id="rightToLight" name="rightToLight" class="input-field">
                        <option value="">Select an option</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                    </select>
                </div>

                <div class="question">
                    <label for="rightsSupport">Do you know if the property has rights of support from adjoining properties?</label>
                    <span class="eye-icon" onclick="sendQuestion('Do you know if the property has rights of support from adjoining properties?')">&#128065;</span>
                    <select id="rightsSupport" name="rightsSupport" class="input-field">
                        <option value="">Select an option</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                    </select>
                </div>

                <div class="question">
                    <label for="customaryRights">Do you know if the property benefits from customary rights?</label>
                    <span class="eye-icon" onclick="sendQuestion('Do you know if the property benefits from customary rights?')">&#128065;</span>
                    <select id="customaryRights" name="customaryRights" class="input-field">
                        <option value="">Select an option</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                    </select>
                </div>

                <div class="question">
                    <label for="mineralRights">Do you know if other people have rights to mines and minerals under the property or its land?</label>
                    <span class="eye-icon" onclick="sendQuestion('Do you know if other people have rights to mines and minerals under the property or its land?')">&#128065;</span>
                    <select id="mineralRights" name="mineralRights" class="input-field">
                        <option value="">Select an option</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                    </select>
                </div>

                <div class="question">
                    <label for="chancelRepair">Do you know if the property is subject to Chancel repair liability?</label>
                    <span class="eye-icon" onclick="sendQuestion('Do you know if the property is subject to Chancel repair liability?')">&#128065;</span>
                    <select id="chancelRepair" name="chancelRepair" class="input-field">
                        <option value="">Select an option</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                    </select>
                </div>

                <div class="question">
                    <label for="takeFromLand">Do you know if anyone else has rights to take things from the land (such as timber, hay or fish)?</label>
                    <span class="eye-icon" onclick="sendQuestion('Do you know if anyone else has rights to take things from the land (such as timber, hay or fish)?')">&#128065;</span>
                    <select id="takeFromLand" name="takeFromLand" class="input-field">
                        <option value="">Select an option</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                    </select>
                </div>

                <div class="question">
                    <label for="otherRights">Are there any other rights or arrangements affecting the property (including rights of way)?</label>
                    <span class="eye-icon" onclick="sendQuestion('Are there any other rights or arrangements affecting the property (including rights of way)?')">&#128065;</span>
                    <select id="otherRights" name="otherRights" class="input-field">
                        <option value="">Select an option</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                    </select>
                </div>
            </div>
            <div class="tab-content" id="services">

                <div class="question">
                    <label for="drainsCrossNeighbour">Do any drains, pipes or wires serving the property cross any neighbour's property?</label>
                    <span class="eye-icon" onclick="sendQuestion('Do any drains, pipes or wires serving the property cross any neighbours property?')">&#128065;</span>
                    <select id="drainsCrossNeighbour" name="drainsCrossNeighbour" class="input-field">
                        <option value="">Select an option</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                        <option value="Not known">Not known</option>
                    </select>
                </div>

                <div class="question">
                    <label for="drainsCrossProperty">Do any drains, pipes or wires leading to any neighbour's property cross your property?</label>
                    <span class="eye-icon" onclick="sendQuestion('Do any drains, pipes or wires leading to any neighbours property cross your property?')">&#128065;</span>
                    <select id="drainsCrossProperty" name="drainsCrossProperty" class="input-field">
                        <option value="">Select an option</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                        <option value="Not known">Not known</option>
                    </select>
                </div>

                <div class="question">
                    <label for="drainAgreement">Is there any agreement or arrangement about drains, pipes or wires?</label>
                    <span class="eye-icon" onclick="sendQuestion('Is there any agreement or arrangement about drains, pipes or wires?')">&#128065;</span>
                    <select id="drainAgreement" name="drainAgreement" class="input-field">
                        <option value="">Select an option</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                        <option value="Not known">Not known</option>
                    </select>
                </div>
            </div>


            <div class="tab-content" id="parking">
                <div class="question">
                <label for="parkingArrangements">What are the parking arrangements at the property?</label>
                <span class="eye-icon" onclick="sendQuestion('What are the parking arrangements at the property?')">&#128065;</span>
                <input type="text" id="parkingArrangements" name="parkingArrangements" class="input-field" placeholder="Enter parking details">
                </div>

                <div class="question">
                    <label for="controlledParking">Is the property in a controlled parking zone or within a local authority parking scheme?</label>
                    <span class="eye-icon" onclick="sendQuestion('Is the property in a controlled parking zone or within a local authority parking scheme?')">&#128065;</span>
                    <select id="controlledParking" name="controlledParking" class="input-field">
                        <option value="">Select an option</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                    </select>
                </div>
            </div>
            <div class="tab-content" id="charges">
                <div class="question">
                    <label for="propertyCharges">Do you have to pay any charges relating to the property (excluding payments such as council tax, utility charges, etc)?</label>
                    <span class="eye-icon" onclick="sendQuestion('Do you have to pay any charges relating to the property (excluding payments such as council tax, utility charges, etc)?')">&#128065;</span>
                    <select id="propertyCharges" name="propertyCharges" class="input-field">
                        <option value="">Select an option</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                    </select>
                </div>
            </div>
            <div class="tab-content" id="occupiers">
                <div class="question">
                    <label for="liveAtProperty">Do you live at the property?</label>
                    <span class="eye-icon" onclick="sendQuestion('Do you live at the property?')">&#128065;</span>
                    <select id="liveAtProperty" name="liveAtProperty" class="input-field">
                        <option value="">Select an option</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                    </select>
                </div>

                <div class="question">
                    <label for="othersLiveAtProperty">Does anyone else, aged 17 or over, live at the property?</label>
                    <span class="eye-icon" onclick="sendQuestion('Does anyone else, aged 17 or over, live at the property?')">&#128065;</span>
                    <select id="othersLiveAtProperty" name="othersLiveAtProperty" class="input-field">
                        <option value="">Select an option</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                    </select>
                </div>

                <div class="question">
                    <label for="vacantPossession">Is the property being sold with vacant possession?</label>
                    <span class="eye-icon" onclick="sendQuestion('Is the property being sold with vacant possession?')">&#128065;</span>
                    <select id="vacantPossession" name="vacantPossession" class="input-field">
                        <option value="">Select an option</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                    </select>
                </div>
            </div>
            <div class="tab-content" id="services_property">
                <div class="question">
                    <label for="electricalTested">Has the whole or any part of the electrical installation been tested by a qualified and registered electrician?</label>
                    <span class="eye-icon" onclick="sendQuestion('Has the whole or any part of the electrical installation been tested by a qualified and registered electrician?')">&#128065;</span>
                    <select id="electricalTested" name="electricalTested" class="input-field">
                        <option value="">Select an option</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                    </select>
                </div>

                <div class="question">
                    <label for="rewired">Has the property been rewired or had any electrical installation work carried out since 1 January 2005?</label>
                    <span class="eye-icon" onclick="sendQuestion('Has the property been rewired or had any electrical installation work carried out since 1 January 2005?')">&#128065;</span>
                    <select id="rewired" name="rewired" class="input-field">
                        <option value="">Select an option</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                        <option value="Not known">Not known</option>
                    </select>
                </div>

                <div class="question">
                    <label for="centralHeating">Does the property have a central heating system?</label>
                    <span class="eye-icon" onclick="sendQuestion('Does the property have a central heating system?')">&#128065;</span>
                    <select id="centralHeating" name="centralHeating" class="input-field">
                        <option value="">Select an option</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                    </select>
                </div>

                <div class="question">
                    <label for="inspectionReport">Do you have a copy of the last inspection report?</label>
                    <span class="eye-icon" onclick="sendQuestion('Do you have a copy of the last inspection report?')">&#128065;</span>
                    <select id="inspectionReport" name="inspectionReport" class="input-field">
                        <option value="">Select an option</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                    </select>
                </div>

                <div class="question">
                    <label for="foulWaterDrainage">Is the property connected to foul water drainage?</label>
                    <span class="eye-icon" onclick="sendQuestion('Is the property connected to foul water drainage?')">&#128065;</span>
                    <select id="foulWaterDrainage" name="foulWaterDrainage" class="input-field">
                        <option value="">Select an option</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                        <option value="Not known">Not known</option>
                    </select>
                </div>

                <div class="question">
                    <label for="surfaceWaterDrainage">Is the property connected to surface water drainage?</label>
                    <span class="eye-icon" onclick="sendQuestion('Is the property connected to surface water drainage?')">&#128065;</span>
                    <select id="surfaceWaterDrainage" name="surfaceWaterDrainage" class="input-field">
                        <option value="">Select an option</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                        <option value="Not known">Not known</option>
                    </select>
                </div>
            </div>
            <div class="tab-content" id="utilities">
                <div class="question">
                    <label for="mainsElectricity">Is the property connected to mains electricity?</label>
                    <span class="eye-icon" onclick="sendQuestion('Is the property connected to mains electricity?')">&#128065;</span>
                    <select id="mainsElectricity" name="mainsElectricity" class="input-field">
                        <option value="">Select an option</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                    </select>
                </div>

                <div class="question">
                    <label for="mainsGas">Is the property connected to mains gas?</label>
                    <span class="eye-icon" onclick="sendQuestion('Is the property connected to mains gas?')">&#128065;</span>
                    <select id="mainsGas" name="mainsGas" class="input-field">
                        <option value="">Select an option</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                    </select>
                </div>

                <div class="question">
                    <label for="mainsWater">Is the property connected to mains water?</label>
                    <span class="eye-icon" onclick="sendQuestion('Is the property connected to mains water?')">&#128065;</span>
                    <select id="mainsWater" name="mainsWater" class="input-field">
                        <option value="">Select an option</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                    </select>
                </div>

                <div class="question">
                    <label for="mainsSewerage">Is the property connected to mains sewerage?</label>
                    <span class="eye-icon" onclick="sendQuestion('Is the property connected to mains sewerage?')">&#128065;</span>
                    <select id="mainsSewerage" name="mainsSewerage" class="input-field">
                        <option value="">Select an option</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                    </select>
                </div>

                <div class="question">
                    <label for="landline">Does the property have a landline telephone connection?</label>
                    <span class="eye-icon" onclick="sendQuestion('Does the property have a landline telephone connection?')">&#128065;</span>
                    <select id="landline" name="landline" class="input-field">
                        <option value="">Select an option</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                    </select>
                </div>

                <div class="question">
                    <label for="cableTV">Does the property have a cable TV connection?</label>
                    <span class="eye-icon" onclick="sendQuestion('Does the property have a cable TV connection?')">&#128065;</span>
                    <select id="cableTV" name="cableTV" class="input-field">
                        <option value="">Select an option</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                    </select>
                </div>
            </div>
            <div class="tab-content" id="transaction">
                <div class="question">
                    <label for="saleDependent">Is this sale dependent on your completing the purchase of another property on the same day?</label>
                    <span class="eye-icon" onclick="sendQuestion('Is this sale dependent on your completing the purchase of another property on the same day?')">&#128065;</span>
                    <select id="saleDependent" name="saleDependent" class="input-field">
                        <option value="">Select an option</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                    </select>
                </div>

                <div class="question">
                    <label for="movingDateRequirements">Do you have any special requirements about a moving date?</label>
                    <span class="eye-icon" onclick="sendQuestion('Do you have any special requirements about a moving date?')">&#128065;</span>
                    <select id="movingDateRequirements" name="movingDateRequirements" class="input-field">
                        <option value="">Select an option</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                    </select>
                </div>

                <div class="question">
                    <label for="salePriceSufficient">Will the sale price be sufficient to repay all mortgages and charges secured on the property?</label>
                    <span class="eye-icon" onclick="sendQuestion('Will the sale price be sufficient to repay all mortgages and charges secured on the property?')">&#128065;</span>
                    <input type="text" id="salePriceSufficient" name="salePriceSufficient" class="input-field" placeholder="Input your answer">
                </div>

                <div class="question">
                    <label for="removeRubbish">Will you ensure that all rubbish is removed from the property (including from the loft, garden, outbuildings, garages, and sheds) and that the property will be left in a clean and tidy condition?</label>
                    <span class="eye-icon" onclick="sendQuestion('Will you ensure that all rubbish is removed from the property and that it will be left in a clean and tidy condition?')">&#128065;</span>
                    <select id="removeRubbish" name="removeRubbish" class="input-field">
                        <option value="">Select an option</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                    </select>
                </div>

                <div class="question">
                    <label for="replaceLightFittings">If light fittings are removed, will you replace the fittings with a ceiling rose, flex, bulb holder, and bulb?</label>
                    <span class="eye-icon" onclick="sendQuestion('If light fittings are removed, will you replace the fittings with a ceiling rose, flex, bulb holder, and bulb?')">&#128065;</span>
                    <select id="replaceLightFittings" name="replaceLightFittings" class="input-field">
                        <option value="">Select an option</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                    </select>
                </div>

                <div class="question">
                    <label for="reasonableCare">Will you take reasonable care when removing any other fittings or contents?</label>
                    <span class="eye-icon" onclick="sendQuestion('Will you take reasonable care when removing any other fittings or contents?')">&#128065;</span>
                    <select id="reasonableCare" name="reasonableCare" class="input-field">
                        <option value="">Select an option</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                    </select>
                </div>

                <div class="question">
                    <label for="keysLeft">Will keys to all windows and doors and details of alarm codes be left at the property or with the estate agent?</label>
                    <span class="eye-icon" onclick="sendQuestion('Will keys to all windows and doors and details of alarm codes be left at the property or with the estate agent?')">&#128065;</span>
                    <select id="keysLeft" name="keysLeft" class="input-field">
                        <option value="">Select an option</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                    </select>
                </div>
            </div>
        </div>
        </div>
        <div id="loading">Loading...</div>        
        <div id="overlay" class="overlay">
                <div class="overlay-content">
                    <div id="overlay-text"></div>
                    <button onclick="closeOverlay()">Close</button>
                </div>
            </div>
        <script>
            document.querySelectorAll('.tab-button').forEach(button => {
                button.addEventListener('click', function () {
                    document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
                    document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
                    this.classList.add('active');
                    document.getElementById(this.getAttribute('data-tab')).classList.add('active');
                });
            });
    
            //function sendQuestion(questionId) {
            //    alert('Fetching data for: ' + questionId);
            //}
            async function sendQuestion(question) {
                let storedResponse = sessionStorage.getItem(question);
                if (storedResponse) {
                    showOverlay(storedResponse);
                    return;
                }
                showLoading();
                try {
                    let response = await fetch('http://tooltip.legalworkflow.com/tooltip-info2', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ question: question, casetype: "Sale and/or Purchase", extra: "do not reference any information from a will case type. Use the guidance notes in Form LP12 in www.gov.uk for advice on questions about lasting powers of attorney.  For example, a question about when attorneys can act could use the content under the heading 'Section 5'." })
                    });
                    let data = await response.text();
                    //console.log(data)
                    //console.log(data.status)
                    //console.log("Test - "+data.content)
                    //console.log("Test1 - "+data.body)
                    sessionStorage.setItem(question, data);
                    showOverlay(data);
                } catch (error) {
                    console.error('Error:', error);
                } finally {
                    hideLoading();
                }
            }
            
            function showOverlay(message) {
                let overlay = document.getElementById('overlay');
                let overlayText = document.getElementById('overlay-text');
                overlayText.innerHTML = message;
                overlay.style.display = 'block';
                overlay.style.visibility = 'visible';
            }
            
            function closeOverlay() {
                document.getElementById('overlay').style.display = 'none';
            }

            function showLoading() {
                document.getElementById('loading').style.display = 'block';
            }
            
            function hideLoading() {
                document.getElementById('loading').style.display = 'none';
            }
        </script>
    </body>
    </html>`
    res.send(HTML)

}

const GetToolTip = async (req, res, next) => {
    //console.log(req.body)
    let question = req.body.question || req.query.question || "test"
    let casetype = req.body.casetype || req.query.casetype || "test"
    let extra = req.body.extra || req.query.extra || ""

    const response = await openai.responses.create({
        model: "gpt-4o-mini",
        input: `<role>You will act as a solicitor in England and Wales.</role>  
                <resources>  
                    <primary>Look first at LPA Guide or gov.uk for resources and information.</primary>  
                    <secondary>If more resources are needed, refer to respected law firm websites in England and Wales.</secondary>  
                </resources>  
                <capabilities>You will be able to answer any type of questions about the subject matter of this questionnaire, which is a <casetype>${casetype}</casetype>. </capabilities>  
                <advice>Provide professional but friendly responses. Mention any cons of the advice given, such as how selecting one option may make a specific process redundant.</advice>  
                <format>Encode answers using only HTML tags such as &lt;p&gt;, &lt;h2&gt;-&lt;h5&gt;, &lt;ul&gt;, &lt;ol&gt;, &lt;li&gt;. Please also provide some spacing by the &lt;BR&gt; tags.</format> 
                <restrictions>Do not ask if the user needs any additional help or any other questions.</restrictions>  
                <clarification>Can you give me more information about what this question is asking, so that I can understand it better? Please also explain any terminology that a non-lawyer of average intelligence might not be familiar with: <question>${question}</question>.</clarification>`,
        text: {
        "format": {
            "type": "text"
        }
        },
        reasoning: {},
        tools: [
            {
            "type": "file_search",
            "vector_store_ids": [
                "vs_68075672abe08191836256fb5c9aa957"
            ],
            max_num_results: 10
            }
        ],
        tool_choice: {
            "type": "file_search"
        },
        temperature: 0,
        max_output_tokens: 2048,
        top_p: 1,
        store: true,
    });
    console.log(response.output_text)
    res.send(response.output_text)

}

/* 
model: "gpt-4o",
        messages: [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": `<role>You will act as a solicitor in England and Wales.</role>  
                <resources>  
                    <primary>Look first at gov.uk for resources and information.</primary>  
                    <secondary>If more resources are needed, refer to respected law firm websites in England and Wales.</secondary>  
                </resources>  
                <capabilities>You will be able to answer any type of questions about the subject matter of this questionnaire, which is a <casetype>${casetype}</casetype>. </capabilities>  
                <advice>Provide professional but friendly responses. Mention any cons of the advice given, such as how selecting one option may make a specific process redundant.</advice>  
                <format>Encode answers using only HTML tags such as &lt;p&gt;, &lt;h1&gt;-&lt;h5&gt;, &lt;ul&gt;, &lt;ol&gt;, &lt;li&gt;.</format> 
                <restrictions>Do not ask if the user needs any additional help or any other questions.</restrictions>  
                <clarification>Can you give me more information about what this question is asking, so that I can understand it better? Please also explain any terminology that a non-lawyer of average intelligence might not be familiar with: <question>${question}</question>.</clarification>`,
                        "temperature": 0
                    }
                ]
            }
        ],
        store: true,

        */

const GetToolTipTA6 = async (req, res, next) => {
    console.log(req.body)
    let question = req.body.question || req.query.question || "test"
    let casetype = req.body.casetype || req.query.casetype || "test"
    let extra = req.body.extra || req.query.extra || ""

    const response = await openai.chat.completions.create({
        model: "o3-mini",
        messages: [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": `
                        <Objective>
        Develop a comprehensive AI-accessible knowledge base that enables a solicitor in England to provide legal guidance to clients completing the Law Society Property Information Form (TA6) (4th edition, 2020 – second revision). 
        The AI should use both the TA6 form content and the TA6 Explanatory Notes to offer clear, precise, and legally sound advice.
    </Objective>

    <Introduction>
        <Purpose>The TA6 form is a crucial part of the conveyancing process, requiring sellers to disclose key information about the property.</Purpose>
        <Legal_Implications>
            <Mandatory>No, but failure to disclose accurate information may result in legal action or financial liability.</Mandatory>
            <Accuracy>Answers must be truthful, complete, and updated if new information arises before completion.</Accuracy>
        </Legal_Implications>
    </Introduction>

    <General_Instructions>
        <Who_Completes>The seller(s) or legal representative (e.g., Power of Attorney, Probate).</Who_Completes>
        <Consequences_Of_Incorrect_Information>May lead to transaction withdrawal or legal claims.</Consequences_Of_Incorrect_Information>
        <Answering_All_Questions>Not mandatory but omissions may cause delays.</Answering_All_Questions>
        <Updating_Information>If new details arise, the seller must notify their solicitor.</Updating_Information>
    </General_Instructions>

    <TA6_Form_Guidance>
        <Section id="1">
            <Title>Boundaries</Title>
            <Description>Determines property boundary responsibilities (e.g., fences, walls).</Description>
            <Advice>Check Land Registry title plans, but verify with actual property boundaries.</Advice>
        </Section>

        <Section id="2">
            <Title>Disputes & Complaints</Title>
            <Description>Requires disclosure of any past or ongoing property disputes.</Description>
            <Advice>Include disputes over noise, boundaries, or legal actions.</Advice>
        </Section>

        <Section id="3">
            <Title>Notices & Proposals</Title>
            <Description>Includes planning applications, compulsory purchase orders.</Description>
            <Advice>Disclose any council-issued notices or nearby development plans.</Advice>
        </Section>

        <Section id="4">
            <Title>Alterations, Planning & Building Control</Title>
            <Description>Includes major work, extensions, and planning permissions.</Description>
            <Advice>Provide documents such as planning permissions and building regulation approvals.</Advice>
        </Section>

        <Section id="5">
            <Title>Guarantees & Warranties</Title>
            <Description>Disclose any warranties (e.g., damp proofing, windows, roofing).</Description>
            <Advice>Provide guarantee certificates where applicable.</Advice>
        </Section>

        <Section id="6">
            <Title>Insurance</Title>
            <Description>Building insurance details, including claims history.</Description>
            <Advice>Declare previous claims and unusual policy conditions.</Advice>
        </Section>

        <Section id="7">
            <Title>Environmental Matters</Title>
            <Description>Flood risks, energy efficiency, radon gas, Japanese knotweed.</Description>
            <Advice>Provide any environmental surveys or EPC ratings.</Advice>
        </Section>

        <Section id="8">
            <Title>Rights & Informal Arrangements</Title>
            <Description>Shared access, private roads, right of way agreements.</Description>
            <Advice>Disclose any rights-of-way or maintenance obligations.</Advice>
        </Section>

        <Section id="9">
            <Title>Parking</Title>
            <Description>On-street parking, permits, allocated spaces.</Description>
            <Advice>Confirm whether parking is subject to local restrictions.</Advice>
        </Section>

        <Section id="10">
            <Title>Other Charges</Title>
            <Description>Service charges, maintenance fees.</Description>
            <Advice>Provide details of any ongoing charges.</Advice>
        </Section>

        <Section id="11">
            <Title>Occupiers</Title>
            <Description>Existing tenants, lodgers, right to vacant possession.</Description>
            <Advice>Provide tenancy agreements if tenants will remain.</Advice>
        </Section>

        <Section id="12">
            <Title>Services</Title>
            <Description>Utility connections, heating systems, drainage.</Description>
            <Advice>Confirm compliance of septic tanks with environmental regulations.</Advice>
        </Section>

        <Section id="13">
            <Title>Connection to Utilities & Services</Title>
            <Description>Availability of mains electricity, gas, water, drainage.</Description>
            <Advice>Specify alternative connections if applicable.</Advice>
        </Section>

        <Section id="14">
            <Title>Transaction Information</Title>
            <Description>Onward purchase, dependencies for completion.</Description>
            <Advice>State if the sale depends on the seller securing another property.</Advice>
        </Section>
    </TA6_Form_Guidance>

    <Risk_Mitigation>
        <Common_Pitfalls>
            <Failure_To_Disclose>Can lead to legal claims or cancellation of the sale.</Failure_To_Disclose>
            <Missing_Documents>May delay the transaction; sellers should obtain replacement copies.</Missing_Documents>
            <Misleading_Answers>Buyers may claim compensation for false information.</Misleading_Answers>
        </Common_Pitfalls>
        <Risk_Reduction_Strategies>
            <Obtain_Indemnity_Insurance>If missing key documents.</Obtain_Indemnity_Insurance>
            <Seek_Legal_Advice>To verify answers before submission.</Seek_Legal_Advice>
            <Provide_Declaration_Of_Truth>If unsure about specific details.</Provide_Declaration_Of_Truth>
        </Risk_Reduction_Strategies>
    </Risk_Mitigation>

    <Final_Review>
        <Consistency_Check>Ensure answers match legal documents (e.g., title deeds).</Consistency_Check>
        <Solicitor_Review>Encouraged before submitting the form.</Solicitor_Review>
        <Update_If_Necessary>Notify the solicitor of any changes before exchange of contracts.</Update_If_Necessary>
    </Final_Review>

    <Explanatory_Notes>
        <Full_Text>
        Property Information Form (TA6)
        Explanatory Notes for Sellers
        Introduction
        The Law Society Property Information Form (TA6) is designed to be capable of being completed
        without reference to any other material. However some of the questions deal with legal concepts
        and other matters that might benefit from some additional explanation.
        These notes have been prepared to help sellers to understand the information that is being
        requested. In some cases, examples of what might be included in the answer to the question are
        provided. Occasionally buyers may find the notes helpful. They are intended to be reference
        material rather than for use on every transaction.
        Please note that while care has been taken in the preparation of the TA6 form and these notes
        the Law Society will not accept any legal liability in relation to them. If you have any queries, you
        should discuss these with your solicitor.
        Some answers may result in further questions being asked.
        Sellers
        If you are a seller you should read the Instructions to Sellers at the beginning of the Law Society
        Property Information Form (TA6). As it says in those instructions you should respond to the
        questions from your own knowledge. You are not expected to have expert knowledge of legal or
        technical matters, or matters that occurred prior to your ownership of the property.
        If you do not know the answer to any question, you must say so. If you are unsure of the
        meaning of any questions or answers, you should ask your solicitor. The Property Information
        Form (TA6) form can be completed in full, in part or not at all. Omissions or delay in providing
        information may delay the sale.
        If you later become aware of any information which would alter any replies you have given, you
        must inform your solicitor immediately. This is as important as giving the right answers in the first
        place. You should not change any arrangements concerning the property (such as a tenant or
        neighbour) without consulting your solicitor.
        It is very important that the answers are accurate. If you give incorrect or incomplete information
        to the buyer (on the Property Information Form (TA6) in writing or in conversation, whether
        through your estate agent or solicitor or directly to the buyer), the buyer may be able to make a
        claim for compensation or refuse to complete the purchase.
        You should give your solicitor any letters, agreements or other papers which help answer the
        questions. If you are aware of any which you are not supplying with the answers, you should tell
        your solicitor. If you indicate that some of the documentation is lost you may need to obtain
        copies at your own expense. You should also pass to your solicitor any notices you have
        received concerning the property and which arrive any time before completion of the sale.
        Sellers and buyers
        Sellers and buyers should note that, whilst every effort is made to ensure the accuracy of the
        information given in these notes, its accuracy is not guaranteed and it does not constitute legal
        advice and cannot be relied upon.
        If you have any queries arising from the information contained in these notes, you should
        discuss these with your solicitor.
        Section 1: Boundaries
        The location and ownership of boundaries can cause arguments between neighbours.
        This section aims to identify the broad extent of the property and set out who is responsible for
        the maintenance of the boundaries. .
        However the issues arising from boundaries can be complex and if necessary you should seek
        specialist advice.
        Further information can be found from the Land Registry at
        http://www.landregistry.gov.uk/public/guides/public-guide-19
        http://www.landregistry.gov.uk/public/faqs/how-do-i-find-out-where-the-boundary-of-my-propertyis
        http://www.landregistry.gov.uk/professional/guides/practice-guide-40s3
        Question 1.1
        This question aims to identify who is responsible for maintaining boundaries or boundary
        features. A boundary feature is a physical feature that separates property from a neighbouring
        property. Boundary features may be natural, such as hedges, or man-made, such as ditches,
        fences or walls.
        Responsibility for maintaining boundaries or boundary features may not be apparent on viewing
        a property and this information may not be clear from the title information as registered at the
        Land Registry, or in the title deeds.
        Maintenance responsibilities can be changed by steps taken by the seller or the owners of
        neighbouring property and may have changed during the time the seller has lived in the property.
        Question 1.2
        This question seeks to provide some further information about the location of the property
        boundaries where these are irregular
        Sellers should provide either a written description of who owns any irregular boundaries or a
        plan. Where a plan is provided, sellers should clearly mark the boundaries of the property on the
        plan.
        Where property is registered at Land Registry, the property boundaries will be marked but not
        usually in an absolute way on the title plan. Most land in England and Wales is registered with
        what is known as ‘general’ boundaries. This means that the registered title is not conclusive and
        the boundaries on the title plan may not exactly match the physical boundary features at the
        property.
        There may be land that is not shown within the boundaries of the property as shown on the
        registered title, but which the seller is nonetheless using. The seller may have acquired title to
        this land through 'adverse possession.' If this is the case, the buyer may wish to investigate
        whether they will also acquire title to that additional land.
        Conversely, there may be land that lies within the boundaries of the property as shown on the
        registered title, but which is being used and occupied by people other than the seller. The buyer
        may wish to investigate whether the people who are using the land may have acquired rights to
        continue doing so through adverse possession or squatting.
        Question 1.3
        As set out above ownership of land and boundaries can be changed by steps taken by the seller
        or the owners of neighbouring property and may have changed during the time the seller has
        lived in the property.
        This question aims to verify whether any boundary feature has been moved.
        If any property boundary has been moved, sellers should provide details.
        Information about the year when any change took place is significant.
        Question 1.4
        The seller should provide details of the sale of any land that was previously part of the property,
        including the dates of the sale.
        The seller should also provide details of any purchases of adjacent property, including dates of
        purchase.
        Question 1.5
        This question is to identify any part of the property that overhangs or projects under, the
        boundary of a neighbouring property or a road.
        Examples of property features that could be given in a reply to this question include:
         Canopies
         Overhanging eaves
         Flying freeholds
         Projecting signs
         Vaults beneath ground level
        Question 1.6
        This question aims to find out whether the seller has received any notices under the Party Wall
        Act 1996.
        If a boundary structure, such as a wall, is jointly used by the seller and a neighbouring property, it
        may be a party structure.
        The Party Wall Act 1996 prevents an owner carrying out work to a common structure, or
        excavation work near to the boundary, without giving notice to the neighbouring owner. If one
        owner does not comply with the legislation, any work done may have to be dismantled and the
        land restored to its former condition.
        Notice must be given for any of the following works:
         The construction of a new wall over or up to the boundary
         Any work affecting an existing party structure
         Excavations within certain distance of neighbouring buildings
        The seller should supply a copy of any notice received under the Party Wall Act 1996 to the
        buyer.
        Section 2: Disputes and complaints
        This section aims to provide information about any existing disputes, or complaints or anything
        that could lead to a dispute in the future.
        Question 2.1
        The seller should provide information about any existing disputes. This could include the cause
        of the dispute (for example, complaints relating to noise) and any action taken to resolve matters.
        The seller should also provide information about disputes that have arisen in the past.
        Question 2.2
        The seller should provide information about anything that could lead to a dispute in the future.
        Section 3: Notices and Proposals
        This section aims to provide information to the buyer about any notices or proposals that may
        affect the property. Some information may also be available from the search made of the local
        authority.
        Question 3.1
        The seller should provide copies of any letters or communications from neighbours, the local
        authority or government departments etc. which might affect the property.
        Question 3.2
        The seller should give details of any proposals to develop or change the use of nearby land or
        buildings.
        Section 4: Alterations, planning and building control
        This section is designed to establish, where any alterations or changes have been made to the
        property, that the works have the proper consents and approvals.
        Question 4.1
        The seller should provide copies of planning permissions, Building Regulations approvals and
        completion certificates.
        Completion certificates demonstrate that work was carried out in accordance with Building
        Regulations requirements
        If the work is self-certified by a member of a Competent Persons Scheme, their certification
        should be supplied. Some examples of Competent Persons Schemes include FENSA, Stroma
        and APHC.
        Further information about Competent Persons Schemes can be found at:
        https://www.gov.uk/building-regulations-competent-person-schemes/overview
        If copies of planning permissions, Building Regulations approvals and completion certificates are
        not available because planning permission or Building Regulations approval was not needed, the
        seller should explain why this was not required.
        Question 4.1(a)
        The seller should specify details of all building works carried out; including the date the work was
        completed.
        Question 4.1(b)
        The seller should provide details of any change of use at the property, including the date that this
        occurred.
        Question 4.1(c)
        The seller should specify details of all installation works carried out, including the date the work
        was completed.
        Question 4.1 (d)
        The seller should specify whether a conservatory has been added and the date the construction
        was completed.
        Question 4.2, 4.3 and 4.4
        Any of the works referred to in these three questions might be regarded as ‘concealed
        development’ if it does not have the required permissions or consents. If there is ‘concealed
        development’ enforcement action may be taken by the Local Authority at some point in the future
        .
        Question 4.2
        The seller should give details of any building or alteration work that does not have the necessary
        consents or permissions.
        Question 4.3
        The seller should give details of all unfinished building and alteration work.
        Question 4.4
        The seller should give details of work that does not comply with planning permission, Building
        Regulations consent conditions, unfinished work or work that does not have the necessary
        consents. Where possible, they should explain why the work does not comply.
        Question 4.5
        If works are not yet completed or have only recently been completed the seller may still be
        waiting for the required documentation, or there may be conditions that still have yet to be met. If
        the seller is aware of matters outstanding he should refer to these here.
        Question 4.6
        The seller should state whether solar panels have been installed at the property and whether
        they are owned outright by the seller or leased.
        Whether the panels are owned or leased, or if a lease of the air space has been granted may
        have an impact on mortgage lending.
        For further information see http://www.cml.org.uk/cml/policy/issues/6229
        Solar photovoltaic panels - leases of roof space
        The Department of Energy and Climate Change, via the Energy Act 2008, introduced a system of
        Feed In Tariffs (FITs) to incentivise small-scale low carbon electrical generation. These FITs
        intend to incentivise the generation of heat from renewable sources on all scales.
        FITs went live on 1 April 2010. Since then, a market of solar photovoltaic panel providers has
        emerged, offering free installation of solar panels onto roofs of residential housing in the United
        Kingdom. The providers receive a FIT payment, and those who have the solar panels installed,
        receive free electricity generated by the panels. Providers have settled on the use of a lease of
        airspace above the roof to protect their interest (they retain ownership of the solar panels).
        CML and BSA have produced joint guidance for providers on what lenders will typically seek
        comfort on before consenting to the lease of roof space. The guidance includes a template letter
        which can be used by the panel providers to confirm to lenders that their lease complies with the
        minimum requirements set out in the guidance. At this stage, the guidance applies to England
        and Wales only, however guidance for Scotland and Northern Ireland will be considered.
        Please note that this is guidance and as such, it is issued to inform the market of typical lender
        requirements. Given the complexity and variation of solar (PV) schemes and leases it cannot
        cover all issues but sets out areas where lenders may have minimum requirements. As with all
        guidance, it will be reviewed regularly.
        NB: We are aware that some leases are being described as 'CML approved'. CML does not
        'approve' solar (PV) leases, as the decision to consent to a solar (PV) lease is entirely up
        to the individual lender.
        Question 4.7
        Question 4.7 (a) - listed buildings
        Listed buildings are buildings of special architectural or historical interest. Properties can be
        listed as Grade I, Grade II* or Grade II.
        If a property is listed, listed building consent will be required in order to make any changes to the
        property.
        There are no time limits for enforcement action where there has been a breach of listed building
        control. Enforcement action can be taken against the owner of the property, irrespective of
        whether that person was the owner when the breach was committed.
        Sellers and buyers in England can find out if a property is listed by searching the National
        Heritage List for England at http://list.english-heritage.org.uk/.
        Further information about listed buildings is available from English Heritage at:
        http://www.english-heritage.org.uk/caring/listing/listed-buildings/
        and for properties in Wales: http://cadw.wales.gov.uk/historicenvironment/help-advice-andgrants/makingchanges/listedbuildconsent/?lang=en
        Question 4.7 (b) - conservation areas
        Conservation areas are areas of special historical or architectural interest. Areas may be
        designated for conservation by local planning authorities. There are over 8,000 conservation
        areas in England and over 500 in Wales.
        Properties that are situated in conservation areas may be subject to special planning controls.
        This means that consent may be required for work that would alter the appearance or character
        of the property, such as demolition, alterations and works to trees.
        The seller and the buyer can find out whether the property is in a conservation area by contacting
        the relevant local authority.
        Further information about conservation areas can be found at:
        http://www.english-heritage.org.uk/caring/listing/local/conservation-areas/
        Question 4.8
        Tree preservation orders (TPOs) protect trees that are desirable or useful in a local area. They
        are written orders made by a local planning authority.
        It is an offence to cut down, top, lop, uproot, wilfully damage or wilfully destroy a protected tree
        without the planning authority's permission.
        Sellers should supply copies of any tree preservation orders and local authority permissions for
        works, where relevant.
        Section 5: Guarantees and warranties
        This section provides information about any guarantees or warranties that relate to the property.
        Question 5.1
        Sellers should provide a copy of all available guarantees, warranties and supporting paperwork
        to the buyer.
        The company that has provided the guarantee may no longer exist. Buyers may wish to contact
        the company that gave the guarantee or warranty to establish whether that company is still
        trading and if so, whether the terms of the guarantee or warranty will apply to them. If the
        company is no longer trading the guarantee will almost certainly be worthless.
        Question 5.2
        Sellers should provide details of any claims made under a guarantee or warranty that relates to
        the property. This should include details of when the claim was made, what the claim was for,
        and any remedy provided.
        Section 6: Insurance
        This section aims to provide information about insurance taken out on the property.
        Question 6.1
        The seller should state whether or not they take out insurance on the property.
        Question 6.2
        When the buyer is investigating the terms on which they could get insurance for the property if
        they bought it insurance companies may ask whether the existing insurance has been subject to
        high excesses, or premiums or unusual conditions.
        However the lack of information given by a seller should not affect the ability of a buyer to insure
        the property. The buyer will apply for insurance cover on the basis of their actual knowledge.
        Question 6.3
        The seller should provide details of any current insurance claim
        Section 7: Environmental matters
        This section intends to provide information to buyers about environmental matters affecting the
        property.
        Question 7.1
        Flooding may take a variety of forms: It may be seasonal or irregular or simply a one-off
        occurrence. Further information about flooding can be found at:
        https://publications.environment-agency.gov.uk/ms/kpXMQ
        The most common types of flooding are:
         Surface water flooding, which occurs when heavy rainfall overwhelms the drainage
        capacity of an area.
         Groundwater flooding, which occurs when the water level in the ground rises above the
        surface level. This is most likely to occur in low lying areas underlain by permeable rocks.
         River flooding, which occurs when a watercourse cannot cope with the water draining into
        it from the surrounding land.
         Coastal flooding, which is caused when high tides or severe weather lead to sea
        defences being breached, flooding the surrounding land.
         Sewer flooding, which is caused when sewers overflow due to the amount of water
        travelling into them.
        If the property has been affected by flooding, the seller should state which parts of the property
        were affected and when the flooding occurred.
        Further information about obtaining flood insurance can be found at:
        http://www.defra.gov.uk/publications/2012/07/19/pb13082-flood-insurance/
        Question 7.2
        Where flooding has occurred, the seller should state which type of flooding affected the property.
        Question 7.3
        The seller should state whether a Flood Risk Report has been prepared for the property and
        supply a copy of the Report.
        Further information about Flood Risk Reports can be found at www.environment-agency.gov.uk
        Question 7.4
        This question seeks to find out whether the property is affected by radon.
        Radon is a naturally-occurring inert gas. It is a radioactive product of natural uranium which is
        present in all rocks and soils and enters property from the ground.
        The Health Protection Agency has published maps of radon-affected areas. Sellers and buyers
        can find out whether their property is in a radon-affected area by visiting:
        http://www.ukradon.org/article.php?key=indicativemap
        Sellers should supply a copy of any radon report and specify whether or not the test result was
        below the recommended action level of 200 Becquerels per cubic metre of indoor air.
        Question 7.5
        Where high levels of radon are present, property owners can install simple remedial measures to
        reduce radon levels.
        Remedial measures can be 'basic' (typically a gas resistant membrane across the ground
        footprint of the property) or 'full' measures (such a fitted ‘standby sump’ that can be activated if
        needed, or provision for adding powered ventilation to suspended floors).
        Building Regulations require that radon remedial measures are installed in all new buildings in
        high radon areas. Such measures must also be installed whenever existing buildings in high
        radon areas are extended.
        Further advice on remedial measures and regulations for new buildings can be found at
        www.ukradon.org or by contacting the Health Protection Agency on 01235 822622.
        Question 7.6
        An Energy Performance Certificate (EPC) must be provided whenever a property is built, sold or
        rented.
        An EPC rates the property's energy efficiency level from A (most efficient) to G (least efficient).
        An EPC is valid for 10 years
        Sellers must supply a copy of the EPC for the property. If the property does not currently have an
        EPC, the seller should obtain one from an accredited assessor. Sellers can find details of
        accredited Domestic Energy Assessors at:
        https://www.epcregister.com/searchAssessor.html
        EPCs that are registered may be retrieved free of charge from the Energy Performance
        Certificate Register: https://www.epcregister.com/home.html
        EPC’s should reveal whether or not the property has a ‘Green Deal’.
        Question 7.7
        This question is concerned with any Green Deal works that have been made to the property.
        The Green Deal is a Government initiative designed to help homeowners increase the energy
        efficiency of their home. It allows homeowners to pay for some or all energy-saving
        improvements, such as loft insulation and cavity wall insulation, over a period of time through
        their electricity bill. The aim of the scheme is that repayments should cost no more than the
        savings expected from the resultant reduced energy use.
        Buyers who are purchasing a property that has Green Deal works will need to make repayments
        for those improvements through their electricity bill.
        The seller should provide details of all improvements financed through the Green Deal scheme.
        They should also provide the buyer with a copy of all relevant Green Deal documentation. Buyers
        should ask for this documentation if it is not provided.
        Further information about the Green Deal can be found at:
        www.gov.uk/greendeal
        Question 7.8 - Japanese knotweed
        The seller should state whether the property is affected by Japanese knotweed.
        Japanese knotweed is a non-native invasive plant that can cause damage to property. Japanese
        knotweed requires several years of treatment before it can be eradicated.
        A Japanese knotweed management plan can help to control the spread of Japanese knotweed
        and eradicate it.
        If you are unsure whether the property is affected by Japanese Knotweed, which most
        sellers are your answer must be “Not Known”
        Most management plans provide a record of works carried out to control Japanese knotweed.
        They can provide reassurance to mortgage lenders who may be concerned about the impact of
        Japanese knotweed on the value of the property.
        Sellers should provide a copy of any Japanese knotweed management plan to the buyer.
        Section 8: Rights and informal arrangements
        This section is designed to provide information about any rights or arrangements.
        Rights and arrangements may relate to access or shared use. They may also include leases for
        less than seven years, rights to mines and minerals, manorial rights, chancel repair and similar
        matters. If you are uncertain about whether a right or arrangement is covered by this question,
        please ask your solicitor.
        Such rights and arrangements may have been created formally (by using a deed) or informally
        (by verbal agreement between property owners).
        There may also be public rights affecting the property eg part of the property may be used by
        members of the public for 'access.
        Question 8.1
        The seller should provide details of any obligation to contribute towards the cost of any jointlyused services. This may include information about the amount, the frequency of payments and
        who receives the payment.
        Question 8.2
        This question aims to identify any rights or arrangements that the property has over a
        neighbouring property. This may include rights of access, such as a road or footpath, or use of a
        shared driveway.
        The seller should provide details of all such rights and arrangements.
        Question 8.3
        This question aims to give the buyer information about the rights of access to the property.
        The seller should provide details of any steps taken by neighbours or others to prevent access to
        the property, or to complain about or demand payment for access to the property. This may
        include details of what action was taken, the reasons for access being denied and any steps
        taken by the seller to resolve the situation.
        The buyer may wish to ask their solicitor about information that the seller reveals in answer to
        this question.
        Question 8.4
        This question aims to ascertain whether any of the specific rights or arrangements in subquestions (a) to (f) affects the property.
        Some rights can affect land even if they are not registered at the Land Registry. These are called
        overriding interests. The legal position changes in October 2013.
        http://www.landregistry.gov.uk/professional/guides/practice-guide-15
        a) rights to light
        Rights to light give home owners the right to natural light through defined apertures (e.g. through
        windows) on buildings on their land.
        b) rights of support from adjoining properties
        Rights of support are where one building or part of a building gives support to a neighbouring
        building or another part of the same building.
        c) other people's rights to take things from the land, such as timber, hay or fish
        d) customary rights
        Customary rights are rights that are enjoyed by the inhabitants of a local community as a result of
        tradition or custom.
        e) rights to mines and minerals
        This is a legal right retained by a former landowner to extract any mines or minerals beneath the
        ground.
        f) chancel repair liability
        Chancel repair liability is an obligation to repair or contribute to the cost of repairing the chancel
        of a parish church. A property does not have to be near to or within sight of a church for its owner
        to be liable to contribute to the repair of the chancel.
        Questions 8.5-8.7
        The seller should give details of any agreements or arrangements relating to drains, pipes or
        wires that cross a neighbouring property. This may include details of arrangements or
        permissions relating to having access to the neighbouring property for the purposes of
        maintenance.
        Section 9: Parking
        Question 9.1
        This question seeks to provide the buyer with information about the parking arrangements at the
        property.
        Question 9.1 (a)
        In some cases, the property information prepared by the estate agents may say that there are
        parking facilities, such as a garage, car port or driveway.
        However, in other cases the parking arrangements will not be specified and they may not be
        obvious from an inspection of the property.
        Sellers should describe what the parking arrangements are. Examples of answers that could be
        given in reply to this question include:
         Garage
         Car port
         Driveway
         Allocated car parking space
         On-street parking
        If a license or permit is required to park vehicles at the property, this should be stated in the
        answer to this question.
        Question 9.1 (b)
        A controlled parking zone (CPZ) is an area where there are restrictions on parking during certain
        times. These restrictions only apply to public roads. The hours when the parking restrictions are
        in operation will be shown on 'Controlled Zone' signs at the entrance to the CPZ.
        A local authority parking scheme (such as a residents' parking scheme) is a scheme put in place
        by a council. Local authority parking schemes may require you to buy a permit in order to park in
        a designated zone.
        The seller should state whether parking for the property is within a CPZ or local authority parking
        scheme.
        Section 10: Other charges
        This section aims to find out whether there are any charges, such as payments to a management
        company, affecting the property.
        Question 10.1
        Sellers should provide details of all such charges, including the cost and frequency of payments
        required.
        If the property is leasehold, details of lease expenses such as service charges and ground rent
        should be set out on the Leasehold Information Form (TA7).
        Section 11: Occupiers
        This section relates to the rights of any occupiers who are currently living at the property and who
        may continue to remain in occupation on completion.
        Question 11.1
        The seller should specify whether or not they currently live at the property.
        Question 11.2 and 11.3
        Occupiers may have specific rights of occupation that need to be addressed (for example, a
        lease or licence). Some occupiers' rights may be protected.
        The seller should say whether anyone else over the age of 17 lives at the property and give their
        full names. Occupiers may have rights to continue living at the property unless they agree to
        vacate the property before completion.
        Question 11.4
        The seller should say whether any of the occupiers referred to in question 10.3 are tenants or
        lodgers.
        If the property is currently subject to a tenancy and is being sold with vacant possession, the
        tenancy will need to be terminated by serving the appropriate notice on the tenant. Sellers and
        buyers should speak to their solicitor where this applies.
        Question 11.5
        The seller should state whether or not the property will be vacant on completion (when the
        purchase money is paid and the title to the property passes from the seller to the buyer).
        All adults living at the property must sign the sale contract to confirm that they will leave the
        property before completion. If they do not sign, they may have a right to continue living at the
        property after completion.
        If occupiers aged 17 or over have not agreed to sign the sale contract, the seller should provide
        evidence to the buyer that the property will be vacant at completion. Where this is the case,
        buyers should speak to their solicitor.
        Section 12: Services
        This section is concerned with the services supplied to the property.
        Question 12.1
        The seller should state whether the whole or part of the electrical installation has been tested by
        an electrician who is qualified and registered with an approved body such as:
         Electrical Contractors' Association (ECA)
         National Association for Professional Inspectors and Testers (NAPIT)
         Ascertiva (formerly NICEIC)
        The seller should also state the year the test was carried out and supply a copy of the test
        certificate.
        Question 12.2 Electricity
        From 1st January 2005, all electrical work must be carried out in accordance with Building
        Regulations.
        All electrical installation and rewiring work must comply with the safety standards set out in the
        wiring regulations (BS7671).
        In order to evidence compliance with requirements for electrical work carried out the seller should
        provide the Building Control Completion Certificate, the installer's Building Regulations
        Compliance Certificate or the BS7671 Electrical Safety Certificate.
        The BS7671 Electrical Safety Certificate confirms that the installation and rewiring work has been
        completed in accordance with the safety standards set out in the Building Regulations.
        The installer's Building Regulations Compliance Certificate confirms that the work was carried out
        in accordance with Building Regulations.
        Question 12.3 Central heating
        The seller should state whether or not the property has central heating.
        Question 12.3 (a)
        The seller should specify what type of fuel the central heating system uses. Some examples
        include:
         Gas
         Electricity
         Heating oil
         Liquid petroleum gas (LPG)
         Coal
         Biomass
        Question 12.3 (b)
        Completion certificates are necessary to show that the installation of the central heating system
        was carried out in accordance with Building Regulations.
        The Building Regulations say that any replacement or new gas fired boiler installed after 1st April
        2005 must be a condensing type boiler. However, a standard efficiency boiler may be installed in
        exceptional circumstances. If this is the case, a copy of the 'exceptional circumstances form' will
        usually be provided when the system is installed.
        The seller should provide a copy of the completion certificate for the heating system, or a copy of
        the exceptional circumstances form, to the buyer before completion.
        Question 12.3 (c)
        The seller should state whether or not the heating system is in good working order.
        Question 12.3 (d)
        The seller should state the year that the heating system was last serviced and provide a copy of
        the inspection report.
        Question 12.4
        Foul water drainage drains the used water from toilets, sinks, baths, showers, washing machines
        and dishwashers.
        Surface water drainage carries rainwater from hard surfaces. Gutters and rainwater pipes will
        carry the rainwater to the underground drainage pipes.
        Question 12.5
        A septic tank is a tank buried in ground that has an inflow of sewage from the house and an
        outflow from the tank. Treatment of sewage is takes place by bacteria and the settlement of
        solids which should be periodically removed.
        Types of sewage treatment plant include infiltration systems, soakaways, package sewage
        treatment works.
        A cesspool is a sealed tank used to collect sewage with no discharge to the environment. It has
        no outlet and requires regular emptying.
        Question 12.6
        Where there is a septic tank, sewage treatment plant or cesspool the seller should state whether
        this is shared with other properties, and if so, how many.
        Question 12.7
        Some drainage systems require regular emptying. Other drainage systems only need to be
        emptied occasionally.
        The seller should state the year that the drainage system was last emptied
        Question 12.8
        The seller should give the year that the sewage treatment plant was last serviced.
        Question 12.9
        The seller should state the year that the drainage system was installed.
        Question 12.10
        Where a septic tank, sewage treatment plant or cesspool is in place at the property, the seller
        should state whether any part of the system lies outside the boundary of the property. The seller
        should provide details of how access to the system is obtained and provide the buyer with plan
        showing the location of the system.
        Section 13: Connection to utilities and services
        This section aims to provide the buyer with details of who is currently supplying utilities and
        services to the property. The seller should state the name of the provider of each utility and
        service and state the location of the meter where applicable.
        Section 14: Transaction information
        This section deals with information that affects the sale of the property but which is not related to
        the property itself.
        Question 14.1
        This question is to establish whether or not the seller wishes to buy another property at the same
        time. This will enable the buyer to understand whether or not there is a linked chain of sales and
        purchases.
        Question 14.2
        The seller should set out any special requirements about a moving date so that this can be
        negotiated with the buyer if necessary.
        Question 14.3
        It is important for the buyer and the buyer’s solicitors to know that the amount being paid for the
        property is enough to pay off outstanding mortgages. If there isn’t enough it doesn’t mean the
        sale cannot proceed but it may mean that some extra steps need to be taken.
        Question 14.4
        These sub-questions are designed to ensure that the seller agrees to leave the property in a
        clean and tidy condition.
        </Full_Text>
    </Explanatory_Notes>

    <Usage>
        This structured XML data ensures that an AI model acting as a solicitor can provide tailored, legally sound advice to clients completing the TA6 form.
    </Usage>
                       
                <capabilities>You will be able to answer any type of questions about the subject matter of this questionnaire, which is a <casetype>${casetype}</casetype>. </capabilities>  
                <advice>Provide professional but friendly responses. Mention any cons of the advice given, such as how selecting one option may make a specific process redundant.</advice>  
                <format>Encode answers using only HTML tags such as &lt;p&gt;, &lt;h1&gt;-&lt;h5&gt;, &lt;ul&gt;, &lt;ol&gt;, &lt;li&gt;.</format> 
                <restrictions>Do not ask if the user needs any additional help or any other questions.</restrictions>  
                <clarification>Can you give me more information about what this question is asking, so that I can understand it better? Please also explain any terminology that a non-lawyer of average intelligence might not be familiar with: <question>${question}</question>.</clarification>`,
                        "temperature": 0
                    }
                ]
            }
        ],
        store: true,
    });
    //console.log(response.choices[0].message.content)
    res.send(response.choices[0].message.content)

}

export {
    GetWebPage,
    GetWebPageLPA,
    GetWebPageTA6,
    GetToolTip,
    GetToolTipTA6
}