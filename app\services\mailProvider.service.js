import sgMail from "@sendgrid/mail";
import { graphMailService } from "./graphMail.service.js";
import pool from "../config/db.js";
import { runQuery } from "#utils";
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from "#middlewares/ErrorClass.js";
import dotenv from "dotenv";

dotenv.config();

/**
 * Mail Provider Factory
 * Chooses between SendGrid and Microsoft Graph API based on law firm configuration
 */
export class MailProviderService {
  constructor() {
    // Initialize SendGrid
    sgMail.setApiKey(process.env.SENDGRID_API_KEY);
  }

  /**
   * Check if law firm has Graph API configured and active
   * @param {number} lf_id - Law firm ID
   * @returns {boolean} True if Graph API is configured and active
   */
  async hasGraphConfig(lf_id) {
    if (!lf_id) return false;

    let con = await pool.getConnection();
    try {
      const result = await runQuery(
        con,
        "SELECT id, is_active FROM graph_credentials WHERE lf_id = ?",
        [lf_id]
      );

      if (result.length === 0) {
        return false; // No credentials configured
      }

      return result[0].is_active === 1; // Return true only if active
    } catch (error) {
      console.error('Error checking Graph config:', error);
      return false;
    } finally {
      con.destroy();
    }
  }

  /**
   * Check if law firm has Graph API credentials (regardless of active status)
   * @param {number} lf_id - Law firm ID
   * @returns {Object} Status object with details
   */
  async getGraphConfigStatus(lf_id) {
    if (!lf_id) return { configured: false, active: false };

    let con = await pool.getConnection();
    try {
      const result = await runQuery(
        con,
        "SELECT id, is_active FROM graph_credentials WHERE lf_id = ?",
        [lf_id]
      );

      if (result.length === 0) {
        return { configured: false, active: false };
      }

      return {
        configured: true,
        active: result[0].is_active === 1
      };
    } catch (error) {
      console.error('Error checking Graph config status:', error);
      return { configured: false, active: false };
    } finally {
      con.destroy();
    }
  }

  /**
   * Send email using the appropriate provider
   * @param {Object} emailData - Email data
   * @param {number} lf_id - Law firm ID (optional)
   * @param {string} user_email - Email of the requesting user (optional)
   * @param {Object} options - Additional options
   * @returns {Object} Send result
   */
  async sendMail(emailData, lf_id = null, user_email = null, options = {}) {
    const { retryCount = 1, fallbackToSendGrid = true } = options;
    let lastError = null;

    try {
      // Check Graph configuration status
      if (lf_id) {
        const graphStatus = await this.getGraphConfigStatus(lf_id);

        if (graphStatus.configured && !graphStatus.active) {
          // Credentials exist but are inactive - log warning and fall back to SendGrid
          console.warn(`Graph credentials are disabled for law firm ${lf_id}, falling back to SendGrid`);
          // Don't throw error, let it fall through to SendGrid
        }

        if (graphStatus.configured && graphStatus.active) {
          // Use Graph API
          for (let attempt = 1; attempt <= retryCount; attempt++) {
          try {
            console.log(`Attempting to send email via Graph API (attempt ${attempt}/${retryCount}) for law firm ${lf_id}`);
            const result = await this.sendViaGraph(emailData, lf_id, user_email);
            console.log(`Email sent successfully via Graph API on attempt ${attempt}`);
            return result;
          } catch (graphError) {
            lastError = graphError;
            console.error(`Graph API attempt ${attempt} failed:`, graphError.message);

            // If this is the last attempt and fallback is enabled, break to fallback
            if (attempt === retryCount && fallbackToSendGrid) {
              console.log('All Graph API attempts failed, falling back to SendGrid');
              break;
            }

            // If not the last attempt, wait before retrying
            if (attempt < retryCount) {
              const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000); // Exponential backoff, max 5s
              console.log(`Waiting ${delay}ms before retry...`);
              await new Promise(resolve => setTimeout(resolve, delay));
            }
          }
        }

        // If we reach here and fallback is disabled, throw the last error
        if (!fallbackToSendGrid) {
          throw lastError || new Error('Graph API failed after all retry attempts');
        }

        // Fall back to SendGrid
        console.log('Falling back to SendGrid after Graph API failures');
        try {
          const result = await this.sendViaSendGrid(emailData);
          console.log('Email sent successfully via SendGrid fallback');
          return {
            ...result,
            fallbackUsed: true,
            originalError: lastError?.message
          };
        } catch (sendGridError) {
          console.error('SendGrid fallback also failed:', sendGridError.message);
          throw ErrorHandler.badRequestError(
            `Both Graph API and SendGrid failed. Graph: ${lastError?.message}, SendGrid: ${sendGridError.message}`
          );
        }
        }
      }

      // Default to SendGrid
      console.log('Using SendGrid as primary provider');
      return await this.sendViaSendGrid(emailData);
    } catch (error) {
      console.error('Mail provider service error:', error);
      throw ErrorHandler.badRequestError(`Failed to send email: ${error.message}`);
    }
  }

  /**
   * Send email via Microsoft Graph API
   * @param {Object} emailData - Email data
   * @param {number} lf_id - Law firm ID
   * @param {string} user_email - Email of the requesting user
   * @returns {Object} Send result
   */
  async sendViaGraph(emailData, lf_id, user_email) {
    return await graphMailService.sendMail(emailData, lf_id, user_email);
  }

  /**
   * Send email via SendGrid
   * @param {Object} emailData - Email data
   * @returns {Object} Send result
   */
  async sendViaSendGrid(emailData) {
    try {
      // Convert emailData to SendGrid format if needed
      const msg = this.formatForSendGrid(emailData);
      
      const result = await sgMail.send(msg);
      
      return {
        success: true,
        messageId: result[0]?.headers?.['x-message-id'] || 'sent',
        provider: 'sendgrid'
      };
    } catch (error) {
      throw ErrorHandler.badRequestError(`SendGrid error: ${error.message}`);
    }
  }

  /**
   * Format email data for SendGrid
   * @param {Object} emailData - Email data
   * @returns {Object} SendGrid formatted message
   */
  formatForSendGrid(emailData) {
    const msg = {
      to: emailData.to,
      from: emailData.from || { name: "Propero Support", email: "<EMAIL>" },
      subject: emailData.subject,
      html: emailData.html,
      text: emailData.text
    };

    // Add optional fields
    if (emailData.cc) msg.cc = emailData.cc;
    if (emailData.bcc) msg.bcc = emailData.bcc;
    if (emailData.personalizations) msg.personalizations = emailData.personalizations;

    return msg;
  }

  /**
   * Get mail provider info for a law firm
   * @param {number} lf_id - Law firm ID
   * @returns {Object} Provider info
   */
  async getProviderInfo(lf_id) {
    const graphStatus = await this.getGraphConfigStatus(lf_id);
    return {
      primaryProvider: (graphStatus.configured && graphStatus.active) ? 'microsoft-graph' : 'sendgrid',
      fallbackProvider: 'sendgrid',
      graphConfigured: graphStatus.configured,
      graphActive: graphStatus.active,
      status: graphStatus.configured ? (graphStatus.active ? 'active' : 'disabled') : 'not-configured'
    };
  }

  /**
   * Test email providers for a law firm
   * @param {number} lf_id - Law firm ID
   * @returns {Object} Test results
   */
  async testProviders(lf_id) {
    const results = {
      sendgrid: { available: true, message: 'SendGrid is available' },
      graph: { available: false, message: 'Not configured' }
    };

    // Test SendGrid
    try {
      if (!process.env.SENDGRID_API_KEY) {
        results.sendgrid = { available: false, message: 'SendGrid API key not configured' };
      }
    } catch (error) {
      results.sendgrid = { available: false, message: `SendGrid error: ${error.message}` };
    }

    // Test Graph API if configured
    if (lf_id) {
      const graphStatus = await this.getGraphConfigStatus(lf_id);

      if (!graphStatus.configured) {
        results.graph = { available: false, message: 'Not configured' };
      } else if (!graphStatus.active) {
        results.graph = { available: false, message: 'Configured but disabled' };
      } else {
        try {
          const graphTest = await graphMailService.testConnection(lf_id);
          results.graph = {
            available: graphTest.success,
            message: graphTest.message
          };
        } catch (error) {
          results.graph = {
            available: false,
            message: `Graph API error: ${error.message}`
          };
        }
      }
    }

    return results;
  }

  /**
   * Send email with enhanced error handling and monitoring
   * @param {Object} emailData - Email data
   * @param {number} lf_id - Law firm ID (optional)
   * @param {string} user_email - Email of the requesting user (optional)
   * @param {Object} options - Additional options
   * @returns {Object} Send result with detailed status
   */
  async sendMailWithMonitoring(emailData, lf_id = null, user_email = null, options = {}) {
    const startTime = Date.now();
    const sendAttempt = {
      timestamp: new Date().toISOString(),
      lf_id,
      user_email,
      recipient: emailData.to,
      subject: emailData.subject,
      attempts: []
    };

    try {
      const result = await this.sendMail(emailData, lf_id, user_email, options);
      const duration = Date.now() - startTime;

      return {
        ...result,
        monitoring: {
          ...sendAttempt,
          duration,
          status: 'success',
          finalProvider: result.provider
        }
      };
    } catch (error) {
      const duration = Date.now() - startTime;

      return {
        success: false,
        error: error.message,
        monitoring: {
          ...sendAttempt,
          duration,
          status: 'failed',
          errorMessage: error.message
        }
      };
    }
  }

  /**
   * Validate email data before sending
   * @param {Object} emailData - Email data to validate
   * @returns {Object} Validation result
   */
  validateEmailData(emailData) {
    const errors = [];

    if (!emailData.to) {
      errors.push('Recipient email is required');
    }

    if (!emailData.subject) {
      errors.push('Email subject is required');
    }

    if (!emailData.html && !emailData.text) {
      errors.push('Email content (html or text) is required');
    }

    if (emailData.to && typeof emailData.to === 'string' && !this.isValidEmail(emailData.to)) {
      errors.push('Invalid recipient email format');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Simple email validation
   * @param {string} email - Email to validate
   * @returns {boolean} Is valid email
   */
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
}

export const mailProviderService = new MailProviderService();
