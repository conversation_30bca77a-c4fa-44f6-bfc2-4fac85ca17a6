import swaggerJsdoc from "swagger-jsdoc";
import swaggerUi from "swagger-ui-express";

const options = {
  definition: {
    openapi: "3.0.0",
    info: {
      title: "Propero Node Server API",
      version: "1.0.0",
      description: "API documentation for Propero Node Server",
    },
    servers: [
      {
        url: "http://localhost:8989",
        description: "Development server",
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: "http",
          scheme: "bearer",
          bearerFormat: "JWT",
          description: "JWT token for standard authentication"
        },
        apiTokenAuth: {
          type: "http",
          scheme: "bearer",
          bearerFormat: "API Token",
          description: "API token for firm-level access (format: propero_xxxxxxxx_...)"
        }
      }
    },
  },
  apis: [
    "./app/routes/*.js",
    "./app/routes/private/*.js",
    "./app/routes/public/*.js",
    "./app/routes/api/*.js",
  ],
};

const specs = swaggerJsdoc(options);

export { swaggerUi, specs };
