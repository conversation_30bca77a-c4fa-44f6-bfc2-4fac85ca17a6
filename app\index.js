import express from "express";
import env from "dotenv";
import logger from "#logger";
// IMPORT DB CONNECTION

// IMPORT ALL MIDDILEARES
import cookieParser from "cookie-parser";
import cors from "cors";
import helmet from "helmet";
import morgan from "morgan";

//IMPORT ALL ROUTES
import PrivateRouter from "./routes/private/index.js";
import PublicRouter from "./routes/public/index.js";
import ApiRouter from "./routes/api/index.js";
import homeRoute from "./routes/home.route.js";

// IMPORTS ALL CUSTOM MIDDLEWARES
import { notFound, errorHandler } from "./middlewares/errorMiddleware.js";

const app = express();
app.use(
    helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'","'unsafe-eval'"],
          connectSrc: ["'self'", "https://propero.nobisoft.vn", "wss://propero.nobisoft.vn", "https://demo-api.propero.co.uk", "https://uat-api.propero.co.uk","wss://demo-api.propero.co.uk","wss://uat-api.propero.co.uk"], // Add your allowed domains here
          // Add other directives as needed
        },
      },
    })
  );
  
env.config();

// SETUP ALL MIDDLEWARES
app.use(morgan("dev"));
app.use(cors());
app.use(cookieParser());
app.use((req, res, next) => {
  res.header("Access-Control-Allow-Origin", ["*"]);
  res.header(
    "Access-Control-Allow-Methods",
    "GET, POST, PATCH, PUT, DELETE, OPTIONS"
  );
  res.header(
    "Access-Control-Allow-Headers",
    "Origin, X-Requested-With, Content-Type, Accept"
  );
  next();
});

app.use("/static", (_, res, next) => {
  res.set("Cross-Origin-Resource-Policy", "cross-origin");
  next();
});
app.use("/static", express.static("static"));

app.use(express.json({ limit: "5mb" }));
app.use(express.urlencoded({ limit: "5mb", extended: false }));

// Swagger Documentation
import { swaggerUi, specs } from "./config/swagger.js";
app.use("/api-docs", swaggerUi.serve, swaggerUi.setup(specs));

// DEFINE ALL ROUTES ENTRY POINTS
app.use("/", homeRoute);
app.use("/public", PublicRouter);
app.use("/private", PrivateRouter);
app.use("/api", ApiRouter);
app.get("/favicon.ico", function (req, res) {
  // Process the data received in req.body
  res.redirect("/static/favicon.ico");
});
// SETUP ALL CUSTOM MIDDLEWARES
app.use(notFound);
app.use(errorHandler);

export default app;
