SELECT DISTINCT * FROM (
	SELECT 
		u.user_id
		, ui.first_name 
		, u.email 
		, u.`role`
	FROM users u 
	JOIN user_info ui ON u.user_id = ui.user_id 
	JOIN law_firm lf ON u.lf_id = lf.lf_id 
	WHERE ( u.role = 5 OR u.role = 2 ) AND lf.lf_id = ?
	UNION
	SELECT 
		c.user_id 
		, ui.first_name
		, u.email 
		, u.`role` 
	FROM cases c
	JOIN users u ON c.user_id = u.user_id 
	JOIN user_info ui ON u.user_id = ui.user_id 
	WHERE id = ?
	UNION 
	SELECT 
		c.created_by  
		, ui.first_name
		, u.email 
		, u.`role` 
	FROM cases c
	JOIN users u ON c.created_by = u.user_id 
	JOIN user_info ui ON u.user_id = ui.user_id 
	WHERE id = ?
) AS a