SELECT 
	u.user_id
	, ui.org_type
	, ui.org_name
	, ui.first_name 
	, ui.last_name 
	, u.email 
	, ui.country 
	, ui.mb_phone AS phone_number
	, u.role
	, u.status 
FROM users u
JOIN user_info ui 
ON u.user_id  = ui.user_id 
JOIN `roles` r 
ON r.id = u.role
JOIN `status` s 
ON s.id = u.status
WHERE u.lf_id = ? 
AND u.role in (2,3,5)
AND (ui.first_name like ?
OR ui.last_name like ?
OR u.email like ?
OR ui.country like ?
OR u.user_id like ?
OR COALESCE(NULLIF(ui.mb_phone, ''), NULLIF(ui.wrk_phone, ''), NULLIF(ui.home_phone, '')) like ?
OR r.name like ?
OR s.name like ?
)
ORDER BY u.user_id DESC
LIMIT ?, ?;
