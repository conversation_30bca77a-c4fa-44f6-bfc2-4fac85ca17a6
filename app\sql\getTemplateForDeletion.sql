-- Optimized query to get all template related data for deletion
-- This eliminates N+1 problem by getting all related IDs in single queries
SELECT 
    g.id as group_id,
    q.id as question_id,
    a.ans_id,
    ans.id as answers_id
FROM templates t
LEFT JOIN `groups` g ON g.tem_id = t.id
LEFT JOIN questions q ON q.gr_id = g.id
LEFT JOIN answer a ON a.ques_id = q.id
LEFT JOIN answers ans ON ans.ques_id = a.ans_id
WHERE t.id = ?
ORDER BY g.id, q.id, a.ans_id, ans.id;
