#!/usr/bin/env node

/**
 * Setup script for API Token System
 * 
 * This script helps set up the API token system by:
 * 1. Creating the database table
 * 2. Creating an AdminAPI user if needed
 * 3. Generating an initial API token
 */

import mysql from 'mysql2/promise';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';
import { apiTokenService } from '../app/services/index.js';
import { passwordHashing } from '../utils/passwordHashing.js';
import generator from 'generate-password';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

dotenv.config();

async function setupApiTokenSystem() {
  let connection;
  
  try {
    console.log('🚀 Setting up API Token System...\n');

    // Create database connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'propero'
    });

    console.log('✅ Connected to database');

    // 1. Create API tokens table
    console.log('📋 Creating api_tokens table...');
    const migrationSql = fs.readFileSync(
      path.join(__dirname, '../migrations/003_create_api_tokens_table.sql'),
      'utf8'
    );
    
    await connection.execute(migrationSql);
    console.log('✅ API tokens table created');

    // 2. Check if AdminAPI user exists
    console.log('👤 Checking for AdminAPI users...');
    const [adminUsers] = await connection.execute(
      "SELECT user_id, email FROM users WHERE role = '6' LIMIT 1"
    );

    let adminUserId;
    let adminEmail;

    if (adminUsers.length === 0) {
      console.log('⚠️  No AdminAPI user found. Creating one...');
      
      // Create AdminAPI user
      const email = '<EMAIL>';
      const password = generator.generate({
        length: 15,
        numbers: true,
        uppercase: true,
        lowercase: true,
        symbols: true,
        strict: true
      });
      
      const hashedPassword = await passwordHashing(password);
      
      // Get first law firm for the user
      const [lawFirms] = await connection.execute(
        "SELECT lf_id FROM law_firm LIMIT 1"
      );
      
      if (lawFirms.length === 0) {
        throw new Error('No law firms found. Please create a law firm first.');
      }
      
      const lfId = lawFirms[0].lf_id;
      
      // Insert user
      const [userResult] = await connection.execute(
        `INSERT INTO users (email, password, role, status, created_at, lf_id, is_owner) 
         VALUES (?, ?, '6', 1, NOW(), ?, 0)`,
        [email, hashedPassword, lfId]
      );
      
      adminUserId = userResult.insertId;
      
      // Insert user info
      await connection.execute(
        `INSERT INTO user_info (user_id, first_name, last_name, org_type, org_name) 
         VALUES (?, 'Admin', 'API', 'System', 'API User')`,
        [adminUserId]
      );
      
      adminEmail = email;
      
      console.log(`✅ AdminAPI user created:`);
      console.log(`   Email: ${email}`);
      console.log(`   Password: ${password}`);
      console.log(`   User ID: ${adminUserId}`);
      console.log(`   ⚠️  SAVE THESE CREDENTIALS SECURELY!`);
    } else {
      adminUserId = adminUsers[0].user_id;
      adminEmail = adminUsers[0].email;
      console.log(`✅ Found AdminAPI user: ${adminEmail} (ID: ${adminUserId})`);
    }

    // 3. Create initial API token
    console.log('🔑 Creating initial API token...');
    
    const [userLawFirm] = await connection.execute(
      "SELECT lf_id FROM users WHERE user_id = ?",
      [adminUserId]
    );
    
    const lfId = userLawFirm[0].lf_id;
    
    const tokenData = await apiTokenService.createToken({
      lf_id: lfId,
      user_id: adminUserId,
      token_name: 'Initial Setup Token',
      created_by: adminUserId
    });

    console.log(`✅ API token created:`);
    console.log(`   Token: ${tokenData.token}`);
    console.log(`   Name: ${tokenData.token_name}`);
    console.log(`   Prefix: ${tokenData.prefix}`);
    console.log(`   ⚠️  SAVE THIS TOKEN SECURELY - IT WON'T BE SHOWN AGAIN!`);

    // 4. Test the token
    console.log('🧪 Testing token...');
    const verifyResult = await apiTokenService.verifyToken(tokenData.token);
    
    if (verifyResult) {
      console.log('✅ Token verification successful');
    } else {
      console.log('❌ Token verification failed');
    }

    console.log('\n🎉 API Token System setup complete!');
    console.log('\nNext steps:');
    console.log('1. Save the AdminAPI user credentials securely');
    console.log('2. Save the API token securely');
    console.log('3. Test the API endpoints using the token');
    console.log('4. Create additional tokens as needed via the API');
    
    console.log('\nExample API usage:');
    console.log(`curl -H "Authorization: Bearer ${tokenData.token}" \\`);
    console.log(`     http://localhost:3000/api/firm/token-info`);

  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run setup if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  setupApiTokenSystem();
}

export { setupApiTokenSystem };
