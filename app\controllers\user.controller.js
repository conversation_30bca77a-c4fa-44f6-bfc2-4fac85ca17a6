import { userService, mailService, graphCredentialsService, mailProviderService } from "#services";
import { catchAsync } from "#utils";
import validator from "validator";
import { roles } from "../middlewares/roles.js";
import generator from "generate-password";
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from "#middlewares/ErrorClass.js";

// import { ChecksumMode } from "@aws-sdk/client-s3";

//============================ADMIN=================================

//get lawfirm
export const getLawfirm = catchAsync(async (req, res) => {
  const role = req.user.role;
  if (role == roles.Admin) {
    const lawfirm = await userService.getLawfirm(
      req.query.page,
      req.query.size,
      req.query.keyword
    );
    return res.status(200).json({
      success: true,
      message: "Law firm fetched",
      data: lawfirm,
    });
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const getListAdmin = catchAsync(async (req, res) => {
  const role = req.user.role;
  if (role == roles.Admin) {
    let { page, size } = req.query;
    let lst_admin = await userService.getListAdmin(
      page,
      size,
      req.query.keyword
    );
    return res.status(200).json({
      success: true,
      message: "List admin fetched",
      data: lst_admin,
    });
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const getUser = catchAsync(async (req, res) => {
  const user = req.user;
  const user_info = await userService.findUserById(user.user_id);
  if (user.length == 0) {
    return res.status(500).json({
      success: false,
      message: "User not found",
    });
  } else {
    return res.status(200).json({
      success: true,
      message: "User found successfully",
      data: user_info,
    });
  }
});

//get user lawfirm
export const getUserLawfirm = catchAsync(async (req, res) => {
  const role = req.user.role;
  if (role == roles.Admin) {
    let checkLawfirm = await userService.findLawfirmById(req.query.lf_id);
    if (checkLawfirm.length == 0) {
      return res.status(500).json({
        success: false,
        message: "Law firm not found",
      });
    } else {
      const { userlf, count } = await userService.getUserLawfirm(
        req.query.lf_id,
        req.query.page,
        req.query.size,
        req.query.keyword
      );
      const formattedUsers = userlf.map((user) => ({
        user_id: user.user_id,
        email: user.email,
        role: user.role,
      }));

      return res.status(200).json({
        success: true,
        message: "Law firm's users fetched",
        data: {
          userlf: formattedUsers,
          count,
        },
      });
    }
  } else if (role == roles.LawfirmAdmin || role == roles.LawfirmSuperAdmin) {
    const userlf = await userService.getUserLawfirm(
      req.user.lf_id,
      req.query.page,
      req.query.size,
      req.query.keyword
    );
    return res.status(200).json({
      success: true,
      message: "Law firm's users fetched",
      data: userlf,
    });
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

// delete user
export const deleteUser = catchAsync(async (req, res) => {
  const user_id = req.body.user_id;
  const role_ = req.user.role;
  const user = await userService.findUserById(user_id);
  if (
    role_ == roles.Client ||
    role_ == roles.LawfirmUser ||
    role_ == roles.LawfirmAdmin
  ) {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
  if (role_ == roles.LawfirmSuperAdmin) {
    if (req.user.lf_id != user[0].lf_id) {
      return res.status(500).json({
        success: false,
        message: "Permission denied!",
      });
    }
  }
  await userService.deleteUser(user_id);
  return res.status(200).json({
    success: true,
    message: "User deleted",
  });
});

// find user by email
export const findUser = catchAsync(async (req, res) => {
  const role = req.user.role;
  // if (
  //   req.body.lf_id == null ||
  //   req.body.lf_id == "" ||
  //   req.body.lf_id == undefined
  // ) {
  //   return res.status(500).json({
  //     success: false,
  //     message: "Lawfirm ID is required",
  //   });
  // }
  if (role == roles.Admin) {
    const email = req.query.email.toLowerCase();
    try {
      const user = await userService.findUser(email);
      return res.status(200).json({
        success: true,
        message: "User",
        data: user,
      });
    } catch (error) {
      return res.status(500).json({
        success: false,
        message: "User not found",
      });
    }
  } else if (role == roles.LawfirmAdmin || role == roles.LawfirmSuperAdmin) {
    const email = req.query.email.toLowerCase();
    try {
      const user = await userService.findUser(email);
      if (user.length == 0) {
        return res.status(500).json({
          success: false,
          message: "User not found",
        });
      } else {
        for (let i = 0; i < user.length; i++) {
          if (user[i].lf_id == req.user.lf_id) {
            return res.status(200).json({
              success: true,
              message: "User",
              data: user,
            });
          }
        }
        return res.status(500).json({
          success: false,
          message: "Permission denied!",
        });
      }
    } catch (error) {
      return res.status(500).json({
        success: false,
        message: "Error finding user",
      });
    }
  } else {
    try {
      const email = req.query.email.toLowerCase();
      const user = await userService.findUser(email);
      if (user.length == 0) {
        return res.status(500).json({
          success: false,
          message: "User not found",
        });
      } else if (user[0].email == req.user.email) {
        return res.status(200).json({
          success: true,
          message: "User",
          data: user,
        });
      } else {
        return res.status(500).json({
          success: false,
          message: "Permission denied!",
        });
      }
    } catch (error) {}
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

//find user by id
export const findUserById = catchAsync(async (req, res) => {
  const role = req.user.role;
  if (role == roles.Admin) {
    const user_id = req.query.user_id;
    try {
      const user = await userService.findUserById(user_id);
      if (user.length == 0) {
        return res.status(500).json({
          success: false,
          message: "User not found",
        });
      } else if (user[0].role != roles.Admin) {
        return res.status(500).json({
          success: false,
          message: "Permission denied!",
        });
      } else {
        return res.status(200).json({
          success: true,
          message: "User found successfully",
          data: user,
        });
      }
    } catch (error) {
      return res.status(500).json({
        success: false,
        message: "Error finding user",
      });
    }
  } else if (
    role == roles.LawfirmAdmin ||
    role == roles.LawfirmSuperAdmin ||
    role == roles.LawfirmUser
  ) {
    const user_id = req.query.user_id;
    try {
      const user = await userService.findUserById(user_id);
      if (user.length == 0) {
        return res.status(500).json({
          success: false,
          message: "User not found",
        });
      } else if (user[0].lf_id != req.user.lf_id) {
        return res.status(500).json({
          success: false,
          message: "Permission denied!",
        });
      } else if (role == roles.LawfirmUser && user[0].role != roles.Client) {
        return res.status(500).json({
          success: false,
          message: "Permission denied!",
        });
      } else {
        return res.status(200).json({
          success: true,
          message: "User found successfully",
          data: user,
        });
      }
    } catch (error) {
      return res.status(500).json({
        success: false,
        message: "Error finding user",
      });
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const updateLawfirm = catchAsync(async (req, res) => {
  const role = req.user.role;
  if (role == roles.Admin) {
    let check = await userService.findLawfirmById(req.body.lf_id);
    if (check.length == 0) {
      return res.status(400).json({
        success: false,
        message: "Lawfirm does not exist",
      });
    } else {
      const {
        email,
        status,
        title,
        lf_name,
        first_name,
        middle_name,
        last_name,
        dob,
        gender,
        home_phone,
        mb_phone,
        wrk_phone,
        adr_1,
        adr_2,
        adr_3,
        state,
        town,
        country,
        post_code,
        color,
        font,
        lf_id,
        user_id,
        pms_id,
      } = req.body;
      const lowerEmail = email.toLowerCase();
      let checkDuplicateEmail = await userService.findUserLawfirm(lowerEmail);
      let cur_user = await userService.findUserById(user_id);
      if (lowerEmail != cur_user[0].email && checkDuplicateEmail.length != 0) {
        return res.status(500).json({
          success: false,
          message: "Email already exist",
        });
      } else if (!validator.isEmail(lowerEmail)) {
        return res.status(500).json({
          success: false,
          message: "Invalid email",
        });
        // } else if (validator.isEmpty(title)) {
        //   return res.status(500).json({
        //     success: false,
        //     message: "Title is required",
        //   });
        // } else if (validator.isEmpty(first_name)) {
        //   return res.status(500).json({
        //     success: false,
        //     message: "First name is required",
        //   });
      } else if (validator.isEmpty(last_name)) {
        return res.status(500).json({
          success: false,
          message: "Surname is required",
        });
        // } else if (validator.isEmpty(adr_1)) {
        //   return res.status(500).json({
        //     success: false,
        //     message: "Address 1 is required",
        //   });
        // } else if (validator.isEmpty(town)) {
        //   return res.status(500).json({
        //     success: false,
        //     message: "Town is required",
        //   });
        // } else if (validator.isEmpty(state)) {
        //   return res.status(500).json({
        //     success: false,
        //     message: "State is required",
        //   });
        // } else if (!validator.isPostalCode(post_code, "any")) {
        //   return res.status(500).json({
        //     success: false,
        //     message: "Invalid postal code",
        //   });
      } else if (validator.isEmpty(mb_phone)) {
        return res.status(500).json({
          success: false,
          message: "Mobile phone number is required",
        });
      } else if (mb_phone && !validator.isMobilePhone(mb_phone)) {
        return res.status(500).json({
          success: false,
          message: "Invalid mobile phone number",
        });
      } else if (wrk_phone && !validator.isMobilePhone(wrk_phone)) {
        return res.status(500).json({
          success: false,
          message: "Invalid work phone number",
        });
      } else if (home_phone && !validator.isMobilePhone(home_phone)) {
        return res.status(500).json({
          success: false,
          message: "Invalid home phone number",
        });
      }
      try {
        const lawfirm = await userService.updateLawFirm(
          lowerEmail,
          title,
          lf_name,
          first_name,
          middle_name == "" ? null : middle_name,
          last_name,
          dob,
          gender == "" ? null : gender,
          home_phone == "" ? null : home_phone,
          mb_phone == "" ? null : mb_phone,
          wrk_phone == "" ? null : wrk_phone,
          adr_1 == "" ? null : adr_1,
          adr_2 == "" ? null : adr_2,
          adr_3 == "" ? null : adr_3,
          state,
          town,
          country,
          post_code,
          color,
          font,
          lf_id,
          user_id,
          pms_id
        );
        return res.status(200).json({
          success: true,
          message: "Lawfirm updated",
        });
      } catch (error) {
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

// add user -> lawfirm
export const addUser = catchAsync(async (req, res) => {
  const role_ = req.user.role;
  const {
    org_type,
    org_name,
    email,
    role,
    status,
    lf_id,
    title,
    first_name,
    middle_name,
    last_name,
    dob,
    gender,
    home_phone,
    mb_phone,
    wrk_phone,
    adr_1,
    adr_2,
    adr_3,
    state,
    town,
    country,
    post_code,
    pms_id,
  } = req.body;
  const lowerEmail = email.toLowerCase();
  let password = generator.generate({
    length: 15,
    numbers: true,
    uppercase: true,
    lowercase: true,
    symbols: true,
    strict: true,
  });
  if (role_ == roles.Client) {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
  if (
    role_ == roles.LawfirmAdmin ||
    role_ == roles.LawfirmSuperAdmin ||
    role_ == roles.LawfirmUser
  ) {
    if (role_ == roles.LawfirmUser && role != roles.Client) {
      return res.status(500).json({
        success: false,
        message: "Permission denied!",
      });
    }
    if (lf_id == null) {
      lf_id = req.user.lf_id;
    } else if (req.user.lf_id != lf_id) {
      return res.status(500).json({
        success: false,
        message: "Permission denied!",
      });
    }
  }
  // Validate that the law firm exists
  const lawFirmCheck = await userService.findLawfirmById(lf_id);
  if (lawFirmCheck.length === 0) {
    return res.status(400).json({
      success: false,
      message: `Law firm with ID ${lf_id} does not exist`,
    });
  }

  let checkDup = await userService.findUserInLawfirm(lowerEmail, lf_id);
  if (checkDup.length != 0) {
    return res.status(500).json({
      success: false,
      message: "Email already exist",
    });
  }
  let checkDuplicateEmail = await userService.findUserLawfirm(lowerEmail);
  if (checkDuplicateEmail.length != 0 && role != roles.Client) {
    return res.status(500).json({
      success: false,
      message: "Email already exist",
    });
  } else if (!validator.isEmail(lowerEmail)) {
    return res.status(500).json({
      success: false,
      message: "Invalid email",
    });
    // } else if (!validator.isStrongPassword(password)) {
    //   return res.status(500).json({
    //     success: false,
    //     message: "Invalid password",
    //   });
    // } else if (validator.isEmpty(title)) {
    //   return res.status(500).json({
    //     success: false,
    //     message: "Title is required",
    //   });
  } else if (validator.isEmpty(last_name)) {
    return res.status(500).json({
      success: false,
      message: "Surname is required",
    });
    // } else if (validator.isEmpty(last_name)) {
    //   return res.status(500).json({
    //     success: false,
    //     message: "Last name is required",
    //   });
    // } else if (validator.isEmpty(adr_1)) {
    //   return res.status(500).json({
    //     success: false,
    //     message: "Address 1 is required",
    //   });
    // } else if (validator.isEmpty(state)) {
    //   return res.status(500).json({
    //     success: false,
    //     message: "State is required",
    //   });
    // } else if (validator.isEmpty(town)) {
    //   return res.status(500).json({
    //     success: false,
    //     message: "Town is required",
    //   });
    // } else if (!validator.isPostalCode(post_code, "any")) {
    //   return res.status(500).json({
    //     success: false,
    //     message: "Invalid postal code",
    //   });
  } else if (validator.isEmpty(mb_phone)) {
    return res.status(500).json({
      success: false,
      message: "Mobile phone number is required",
    });
  } else if (mb_phone && !validator.isMobilePhone(mb_phone)) {
    return res.status(500).json({
      success: false,
      message: "Invalid mobile phone number",
    });
  } else if (wrk_phone && !validator.isMobilePhone(wrk_phone)) {
    return res.status(500).json({
      success: false,
      message: "Invalid work phone number",
    });
  } else if (home_phone && !validator.isMobilePhone(home_phone)) {
    return res.status(500).json({
      success: false,
      message: "Invalid home phone number",
    });
  } else {
    const token = await userService.addUser(
      org_type == "" ? null : org_type,
      org_name == "" ? null : org_name,
      lowerEmail,
      password,
      role,
      status,
      lf_id,
      title,
      first_name,
      middle_name == "" ? null : middle_name,
      last_name,
      dob,
      gender == "" ? null : gender,
      home_phone == "" ? null : home_phone,
      mb_phone == "" ? null : mb_phone,
      wrk_phone == "" ? null : wrk_phone,
      adr_1 == "" ? null : adr_1,
      adr_2 == "" ? null : adr_2,
      adr_3 == "" ? null : adr_3,
      state,
      town,
      country,
      post_code,
      req.user.user_id,
      pms_id
    );
    if (role != roles.Client) {
      // Use smart wrapper that automatically tries Graph API first, then falls back to SendGrid
      const mailer = await mailService.sendTempPasswordSmart(
        lowerEmail,
        password,
        first_name,
        req.user
      );
    }
    return res.status(200).json({
      success: true,
      message: "User added",
      user_id: token.user_id,
      data: {
        user_id: token.user_id,
        email: lowerEmail,
        role: role,
        lf_id: lf_id
      }
    });
  }
});

// update user
export const updateUser = catchAsync(async (req, res) => {
  const role_ = req.user.role;
  if (
    (role_ == roles.Admin && req.body.role == roles.Admin) ||
    role_ == roles.LawfirmAdmin ||
    role_ == roles.LawfirmSuperAdmin ||
    role_ == roles.LawfirmUser ||
    role_ == roles.AdminAPI
  ) {
    const {
      new_email,
      title,
      org_type,
      org_name,
      first_name,
      middle_name,
      last_name,
      dob,
      gender,
      home_phone,
      mb_phone,
      wrk_phone,
      adr_1,
      adr_2,
      adr_3,
      state,
      town,
      country,
      post_code,
      user_id,
      role,
      pms_id,
    } = req.body;
    const cur_user = await userService.findUserById(user_id);
    if (new_email != cur_user[0].email) {
      if (role_ != roles.Admin) {
        return res.status(403).json({
          success: false,
          message: "Permission denied!",
        });
      }
    }

    const lowerEmail = new_email.toLowerCase();
    let checkDuplicateEmail = await userService.findUserLawfirm(lowerEmail);
    let checkDup = await userService.findUserInLawfirm(
      lowerEmail,
      cur_user[0].lf_id
    );
    if (checkDup.length != 0 && checkDup[0].user_id != user_id) {
      return res.status(500).json({
        success: false,
        message: "Email already exist",
      });
    } else if (
      lowerEmail != cur_user[0].email &&
      checkDuplicateEmail.length != 0
    ) {
      return res.status(500).json({
        success: false,
        message: "Email already exist",
      });
    } else if (!validator.isEmail(lowerEmail)) {
      return res.status(500).json({
        success: false,
        message: "Invalid email",
      });
    } else if (validator.isEmpty(last_name)) {
      return res.status(500).json({
        success: false,
        message: "Surname is required",
      });
    } else if (validator.isEmpty(mb_phone)) {
      return res.status(500).json({
        success: false,
        message: "Mobile phone number is required",
      });
    } else if (mb_phone && !validator.isMobilePhone(mb_phone)) {
      return res.status(500).json({
        success: false,
        message: "Invalid mobile phone number",
      });
    } else if (wrk_phone && !validator.isMobilePhone(wrk_phone)) {
      return res.status(500).json({
        success: false,
        message: "Invalid work phone number",
      });
    } else if (home_phone && !validator.isMobilePhone(home_phone)) {
      return res.status(500).json({
        success: false,
        message: "Invalid home phone number",
      });
    } else {
      const emailToUpdate =
        role_ === roles.Admin ? lowerEmail : cur_user[0].email;
      if (user_id == req.user.user_id) {
        const token = await userService.updateUser(
          emailToUpdate,
          title,
          org_type == "" ? null : org_type,
          org_name == "" ? null : org_name,
          first_name,
          middle_name == "" ? null : middle_name,
          last_name,
          dob,
          gender == "" ? null : gender,
          home_phone == "" ? null : home_phone,
          mb_phone == "" ? null : mb_phone,
          wrk_phone == "" ? null : wrk_phone,
          adr_1 == "" ? null : adr_1,
          adr_2 == "" ? null : adr_2,
          adr_3 == "" ? null : adr_3,
          state,
          town,
          country,
          post_code,
          user_id,
          role,
          pms_id
        );
        return res.status(200).json({
          success: true,
          message: "User updated",
        });
      }
      if (
        role_ == roles.LawfirmAdmin ||
        role_ == roles.LawfirmSuperAdmin ||
        role_ == roles.LawfirmUser
      ) {
        const user = await userService.findUserById(user_id);
        if (user.length == 0) {
          return res.status(500).json({
            success: false,
            message: "User not found",
          });
        } else if (user[0].lf_id != req.user.lf_id) {
          return res.status(500).json({
            success: false,
            message: "Permission denied!",
          });
        } else if (role_ == roles.LawfirmUser && user[0].role != roles.Client) {
          return res.status(500).json({
            success: false,
            message: "Permission denied!",
          });
        }
      }
      const token = await userService.updateUser(
        emailToUpdate,
        title,
        org_type == "" ? null : org_type,
        org_name == "" ? null : org_name,
        first_name,
        middle_name == "" ? null : middle_name,
        last_name,
        dob,
        gender == "" ? null : gender,
        home_phone == "" ? null : home_phone,
        mb_phone == "" ? null : mb_phone,
        wrk_phone == "" ? null : wrk_phone,
        adr_1 == "" ? null : adr_1,
        adr_2 == "" ? null : adr_2,
        adr_3 == "" ? null : adr_3,
        state,
        town,
        country,
        post_code,
        user_id,
        role,
        pms_id
      );
      return res.status(200).json({
        success: true,
        message: "User updated",
      });
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const updateRole = catchAsync(async (req, res) => {
  const role_ = req.user.role;
  if (role_ == roles.Admin) {
    const { role, user_id } = req.body;
    const check = await userService.findUserById(user_id);
    if (check.length == 0) {
      return res.status(500).json({
        success: false,
        message: "User not found",
      });
    } else if (role == "" || role == undefined || role == null) {
      return res.status(500).json({
        success: false,
        message: "Role is required",
      });
    } else if (user_id == "" || user_id == undefined || user_id == null) {
      return res.status(500).json({
        success: false,
        message: "User id is required",
      });
    } else if (user_id == req.user.user_id) {
      return res.status(500).json({
        success: false,
        message: "You cannot update your own role",
      });
    } else if (
      role == roles.Admin ||
      role == roles.Client ||
      check[0].role == roles.Admin ||
      check[0].role == roles.Client
    ) {
      return res.status(500).json({
        success: false,
        message: "Permission denied!",
      });
    }
    await userService.updateRole(role, user_id);
    return res.status(200).json({
      success: true,
      message: "Done",
    });
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

//add admin lawfirm
export const addUserLawFirm = catchAsync(async (req, res) => {
  const role_ = req.user.role;
  if (role_ == roles.Admin) {
    const {
      email,
      role,
      status,
      title,
      org_type,
      org_name,
      lf_name,
      first_name,
      middle_name,
      last_name,
      dob,
      gender,
      home_phone,
      mb_phone,
      wrk_phone,
      adr_1,
      adr_2,
      adr_3,
      state,
      town,
      country,
      post_code,
    } = req.body;

    const lowerEmail = email?.toLowerCase();
    let password = generator.generate({
      length: 15,
      numbers: true,
      uppercase: true,
      lowercase: true,
      symbols: true,
      strict: true,
    });
    let checkLawfirm = await userService.findLawfirm(lf_name);
    let checkDuplicateEmail = await userService.findUserLawfirm(lowerEmail);
    if (!checkLawfirm.length == 0) {
      return res.status(500).json({
        success: false,
        message: "Law firm already exist",
      });
    } else if (!checkDuplicateEmail.length == 0) {
      return res.status(500).json({
        success: false,
        message: "Email already exist",
      });
    } else if (!lowerEmail || !validator.isEmail(lowerEmail)) {
      return res.status(500).json({
        success: false,
        message: "Invalid email",
      });
      // } else if (!validator.isStrongPassword(password)) {
      //   return res.status(500).json({
      //     success: false,
      //     message: "Invalid password",
      //   });
      // } else if (validator.isEmpty(title)) {
      //   return res.status(500).json({
      //     success: false,
      //     message: "Title is required",
      //   });
    } else if (validator.isEmpty(last_name)) {
      return res.status(500).json({
        success: false,
        message: "Surname is required",
      });
      // } else if (validator.isEmpty(last_name)) {
      //   return res.status(500).json({
      //     success: false,
      //     message: "Last name is required",
      //   });
      // } else if (validator.isEmpty(adr_1)) {
      //   return res.status(500).json({
      //     success: false,
      //     message: "Address 1 is required",
      //   });
      // } else if (validator.isEmpty(state)) {
      //   return res.status(500).json({
      //     success: false,
      //     message: "State is required",
      //   });
      // } else if (validator.isEmpty(town)) {
      //   return res.status(500).json({
      //     success: false,
      //     message: "Town is required",
      //   });
      // } else if (!validator.isPostalCode(post_code, "any")) {
      //   return res.status(500).json({
      //     success: false,
      //     message: "Invalid postal code",
      //   });
    } else if (validator.isEmpty(mb_phone)) {
      return res.status(500).json({
        success: false,
        message: "Mobile phone number is required",
      });
    } else if (mb_phone && !validator.isMobilePhone(mb_phone)) {
      return res.status(500).json({
        success: false,
        message: "Invalid mobile phone number",
      });
    } else if (wrk_phone && !validator.isMobilePhone(wrk_phone)) {
      return res.status(500).json({
        success: false,
        message: "Invalid work phone number",
      });
    } else if (home_phone && !validator.isMobilePhone(home_phone)) {
      return res.status(500).json({
        success: false,
        message: "Invalid home phone number",
      });
    } else {
      console.log(1);
      const token = await userService.addUserLawFirm(
        lowerEmail,
        password,
        role,
        status,
        title,
        org_type == "" ? null : org_type,
        org_name == "" ? null : org_name,
        lf_name,
        first_name,
        middle_name == "" ? null : middle_name,
        last_name,
        dob,
        gender == "" ? null : gender,
        home_phone == "" ? null : home_phone,
        mb_phone == "" ? null : mb_phone,
        wrk_phone == "" ? null : wrk_phone,
        adr_1 == "" ? null : adr_1,
        adr_2 == "" ? null : adr_2,
        adr_3 == "" ? null : adr_3,
        state,
        town,
        country,
        post_code,
        req.user.user_id
      );
      if (role != roles.Client) {
        // Use smart wrapper that automatically tries Graph API first, then falls back to SendGrid
        const mailer = await mailService.sendTempPasswordSmart(
          lowerEmail,
          password,
          first_name,
          req.user
        );
      }
      return res.status(200).json({
        success: true,
        message: "User added",
        user_id: token.user_id,
        lf_id: token.lf_id,
        data: {
          user_id: token.user_id,
          lf_id: token.lf_id,
          email: lowerEmail,
          role: role
        }
      });
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});
//-------------------------------------------------------------

// export const login = catchAsync(async (req, res) => {
//   const { email, password } = req.body;
//   const lowerEmail = email.toLowerCase();
//   if (lowerEmail != req.user.reason) {
//     return res.status(500).json({
//       success: false,
//       message: "Unauthorized",
//     });
//   }
//   const token = await userService.login(lowerEmail, password);
//   const country = await userService.getCountry(lowerEmail);
//   await userService.displayTimeInTimezone(
//     await userService.convertCountryNameToISOCode(country)
//   );
//   // console.log(Date(Date.now()).toString());
//   return res.status(200).json({
//     success: true,
//     message: "token",
//     data: token,
//   });
// });

export const login = catchAsync(async (req, res) => {
  const { email, password } = req.body;
  const lowerEmail = email.toLowerCase();
  const checkLogin = await userService.login(lowerEmail, password);
  if (checkLogin.checkUser == 1) {
    const country = await userService.getCountry(lowerEmail);
    await userService.displayTimeInTimezone(
      await userService.convertCountryNameToISOCode(country)
    );
    return res.status(200).json({
      success: true,
      message: "token",
      token: checkLogin.token,
      prefix: checkLogin.prefix,
    });
  } else
    return res.status(400).json({
      success: false,
      message: "Please check your email and password again",
    });
});

export const refreshToken = catchAsync(async (req, res) => {
  const { old_access_token, user_id } = req.body;
  const old_refresh_token = req.headers.authorization.split(" ")[1];
  console.log(user_id, req.user.user_id);
  if (user_id != req.user.user_id) {
    throw ErrorHandler.badRequestError("Session has expired");
  }
  let { access_token, refresh_token } = await userService.refreshToken(
    old_refresh_token,
    old_access_token,
    user_id
  );
  return res.status(200).json({
    success: true,
    message: "access_token",
    access_token: access_token,
    refresh_token: refresh_token,
  });
});

export const changePassword = catchAsync(async (req, res) => {
  const { email, old_password, new_password } = req.body;
  const lowerEmail = email.toLowerCase();
  if (lowerEmail != req.user.email) {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
  if (!validator.isStrongPassword(new_password)) {
    return res.status(500).json({
      success: false,
      message: "Invalid password",
    });
  }
  const token = await userService.changePassword(
    lowerEmail,
    old_password,
    new_password
  );
  return res.status(200).json({
    success: true,
    message: "Password changed successfully",
  });
});
export const resetPassword = catchAsync(async (req, res) => {
  const { email, new_password } = req.body;
  const lowerEmail = email.toLowerCase();
  if (lowerEmail != req.user.reason) {
    return res.status(500).json({
      success: false,
      message: "Unauthorized",
    });
  }
  if (!validator.isStrongPassword(new_password)) {
    return res.status(500).json({
      success: false,
      message: "Invalid password",
    });
  }
  const token = await userService.resetPassword(lowerEmail, new_password);
  return res.status(200).json({
    success: true,
    message: "Password changed successfully",
  });
});
//============================LAWFIRM=================================

export const getLawFirmDetails = catchAsync(async (req, res) => {
  const role_ = req.user.role;
  if (role_ == roles.Admin) {
    const { lf_id } = req.body;
    let checkLawfirm = await userService.getLawfirmDetails(lf_id);
    if (checkLawfirm.length != 0) {
      return res.status(200).json({
        success: true,
        message: "Law firm details fetched",
        data: checkLawfirm[0],
      });
    } else {
      return res.status(500).json({
        success: false,
        message: "Error getting law firm",
      });
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});
export const updateLawFirmStatus = catchAsync(async (req, res) => {
  const role_ = req.user.role;
  const { status, lf_id } = req.body;
  let checkLawfirm = await userService.getLawfirmDetails(lf_id);
  if (
    role_ == roles.Admin ||
    (checkLawfirm[0].status == 3 && req.user.lf_id == lf_id)
  ) {
    try {
      let checkLawfirm = await userService.updateLawFirmStatus(status, lf_id);
      return res.status(200).json({
        success: true,
        message: "Law firm status updated",
      });
    } catch (error) {
      return res.status(500).json({
        success: false,
        message: "Error updating law firm",
      });
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});
export const getLawfirmSettings = catchAsync(async (req, res) => {
  let settings = await userService.getLawfirmSettings(req.query.prefix);
  return res.status(200).json({
    success: true,
    message: "Settings fetched",
    data: settings,
  });
});
export const updateLawFirmMandate = catchAsync(async (req, res) => {
  const role_ = req.user.role;
  const { mandate, lf_id } = req.body;
  if (role_ == roles.Admin) {
    try {
      let checkLawfirm = await userService.updateLawFirmMandate(mandate, lf_id);
      return res.status(200).json({
        success: true,
        message: "Law firm mandate updated",
      });
    } catch (error) {
      return res.status(500).json({
        success: false,
        message: "Error getting law firm",
      });
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});
export const updateLawFirmPrefix = catchAsync(async (req, res) => {
  const role_ = req.user.role;
  const { prefix, lf_id } = req.body;
  if (role_ == roles.Admin) {
    try {
      let checkLawfirm = await userService.updateLawFirmPrefix(prefix, lf_id);
      return res.status(200).json({
        success: true,
        message: "Law firm subdomain updated",
      });
    } catch (error) {
      return res.status(500).json({
        success: false,
        message: "Error getting law firm",
      });
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});
export const uploadBanner = catchAsync(async (req, res) => {
  const role_ = req.user.role;
  if (role_ == roles.Admin) {
    try {
      const { color, font, lf_id, banner } = req.body;
      let checkLawfirm = await userService.uploadBanner(
        banner,
        color,
        font,
        lf_id
      );
      return res.status(200).json({
        success: true,
        message: "Lawfirm updated",
      });
    } catch (error) {
      return res.status(500).json({
        success: false,
        message: "Error getting lawfirm",
      });
    }
  } else if (role_ == roles.LawfirmAdmin || role_ == roles.LawfirmSuperAdmin) {
    try {
      const { color, font, banner } = req.body;
      let checkLawfirm = await userService.uploadBanner(
        banner,
        color,
        font,
        req.user.lf_id
      );
      return res.status(200).json({
        success: true,
        message: "Lawfirm updated",
      });
    } catch (error) {
      console.log(error);
      return res.status(500).json({
        success: false,
        message: "Error getting lawfirm",
      });
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const updateUserStatus = catchAsync(async (req, res) => {
  const role = req.user.role;
  const { status, user_id } = req.body;
  const email = req.user.email;

  if (user_id == req.user.user_id) {
    return res.status(403).json({
      success: false,
      message: "You cannot update your own status!",
    });
  }

  const user = await userService.findUserById(user_id);
  if (user.length == 0) {
    return res.status(404).json({
      success: false,
      message: "User not found",
    });
  }

  // Admin có toàn quyền
  if (role == roles.Admin) {
    const token = await userService.updateUserStatus(status, email, user_id);
    return res.status(200).json({
      success: true,
      message: "User status updated successfully",
    });
  }

  // LawfirmSuperAdmin và LawfirmAdmin
  if (role == roles.LawfirmSuperAdmin || role == roles.LawfirmAdmin) {
    if (user[0].lf_id != req.user.lf_id) {
      return res.status(403).json({
        success: false,
        message: "Permission denied! You can only update users in your lawfirm",
      });
    }

    if (user[0].role == roles.Admin) {
      return res.status(403).json({
        success: false,
        message: "Permission denied! Cannot update Admin status",
      });
    }

    const token = await userService.updateUserStatus(status, email, user_id);
    return res.status(200).json({
      success: true,
      message: "User status updated successfully",
    });
  }

  // LawfirmUser
  if (role == roles.LawfirmUser) {
    if (user[0].lf_id != req.user.lf_id || user[0].role != roles.Client) {
      return res.status(403).json({
        success: false,
        message: "Lawfirm users can only update client status in their lawfirm",
      });
    }

    const token = await userService.updateUserStatus(status, email, user_id);
    return res.status(200).json({
      success: true,
      message: "User status updated successfully",
    });
  }

  return res.status(403).json({
    success: false,
    message: "Permission denied!",
  });
});

export const getClientLawfirm = catchAsync(async (req, res) => {
  const role = req.user.role;
  // if (role == roles.Admin) {
  //   let checkLawfirm = await userService.findLawfirmById(req.query.lf_id);
  //   if (checkLawfirm.length == 0) {
  //     return res.status(500).json({
  //       success: false,
  //       message: "Law firm not found",
  //     });
  //   } else {
  //     const userlf = await userService.getClientLawfirm(
  //       req.query.lf_id,
  //       req.query.page,
  //       req.query.size,
  //       req.query.keyword
  //     );
  //     return res.status(200).json({
  //       success: true,
  //       message: "Law firm's clients fetched",
  //       data: userlf,
  //     });
  //   }
  // } else
  if (
    role == roles.LawfirmAdmin ||
    role == roles.LawfirmSuperAdmin ||
    role == roles.LawfirmUser
  ) {
    const userlf = await userService.getClientLawfirm(
      req.user.lf_id,
      req.query.page,
      req.query.size,
      req.query.keyword
    );
    return res.status(200).json({
      success: true,
      message: "Law firm's clients fetched",
      data: userlf,
    });
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const logout = catchAsync(async (req, res) => {
  const { refresh_token, access_token } = req.body;
  let logout = await userService.logout(req.user.user_id);
  if (logout == 1) {
    return res.status(200).json({
      success: true,
      message: "Logout",
    });
  } else
    return res.status(500).json({
      success: false,
      message: "Error",
    });
});

export const resendInvitation = catchAsync(async (req, res) => {
  const { user_id } = req.body;
  let resend = await userService.resendInvitation(user_id);
  if (resend == 0) {
    return res.status(403).json({
      success: false,
      message: "User not found",
    });
  } else if (resend == 1) {
    return res.status(403).json({
      success: false,
      message: "Account has been activated",
    });
  } else if (resend == 2) {
    return res.status(403).json({
      success: false,
      message: "Account has been activated",
    });
  } else
    return res.status(200).json({
      success: true,
      message: "A new invitation has been sent",
    });
});

export const saveClientFromPMS = catchAsync(async (req, res) => {
  const {
    org_type,
    org_name,
    email,
    title,
    first_name,
    middle_name,
    last_name,
    dob,
    gender,
    home_phone,
    mb_phone,
    wrk_phone,
    adr_1,
    adr_2,
    adr_3,
    state,
    town,
    country,
    post_code,
    pms_id,
  } = req.body;
  const lf_id = JSON.stringify(req.user.lf_id);
  if (!email || validator.isEmpty(email)) {
    return res.status(200).json({
      success: false,
      message: "Email is required",
    });
  }
  const lowerEmail = email.toLowerCase();
  let password = generator.generate({
    length: 15,
    numbers: true,
    uppercase: true,
    lowercase: true,
    symbols: true,
    strict: true,
  });
  let checkDup = await userService.findUserInLawfirm(lowerEmail, lf_id);
  if (checkDup.length != 0) {
    return res.status(200).json({
      success: false,
      message: "Email already exist",
    });
  }
  let checkDuplicateEmail = await userService.findUserLawfirm(lowerEmail);
  if (checkDuplicateEmail.length != 0) {
    return res.status(200).json({
      success: false,
      message: "Email already exist",
    });
  } else if (!validator.isEmail(lowerEmail)) {
    return res.status(200).json({
      success: false,
      message: "Invalid email",
    });
  } else if (!lf_id || validator.isEmpty(lf_id)) {
    return res.status(200).json({
      success: false,
      message: "Lawfirm id is required",
    });
  } else if (!first_name || validator.isEmpty(first_name)) {
    return res.status(200).json({
      success: false,
      message: "First name is required",
    });
  } else if (!last_name || validator.isEmpty(last_name)) {
    return res.status(200).json({
      success: false,
      message: "Surname is required",
    });
  } else if (!gender || validator.isEmpty(gender)) {
    return res.status(200).json({
      success: false,
      message: "Gender is required",
    });
  } else if (!dob || validator.isEmpty(dob)) {
    return res.status(200).json({
      success: false,
      message: "Date of birth is required",
    });
  } else if (!mb_phone || validator.isEmpty(mb_phone)) {
    return res.status(200).json({
      success: false,
      message: "Mobile phone number is required",
    });
  } else if (mb_phone && !validator.isMobilePhone(mb_phone)) {
    return res.status(200).json({
      success: false,
      message: "Invalid mobile phone number",
    });
  } else if (wrk_phone && !validator.isMobilePhone(wrk_phone)) {
    return res.status(200).json({
      success: false,
      message: "Invalid work phone number",
    });
  } else if (home_phone && !validator.isMobilePhone(home_phone)) {
    return res.status(200).json({
      success: false,
      message: "Invalid home phone number",
    });
  } else if (!adr_1 || validator.isEmpty(adr_1)) {
    return res.status(200).json({
      success: false,
      message: "Address 1 is required",
    });
    // } else if (!state || validator.isEmpty(state)) {
    //   return res.status(200).json({
    //     success: false,
    //     message: "Count/State is required",
    //   });
  } else if (!town || validator.isEmpty(town)) {
    return res.status(200).json({
      success: false,
      message: "Town/City is required",
    });
  } else if (!country || validator.isEmpty(country)) {
    return res.status(200).json({
      success: false,
      message: "Country is required",
    });
  } else if (!post_code || validator.isEmpty(post_code)) {
    return res.status(200).json({
      success: false,
      message: "Postal code is required",
    });
  } else if (!validator.isPostalCode(post_code, "any")) {
    return res.status(200).json({
      success: false,
      message: "Invalid postal code",
    });
  } else {
    const token = await userService.saveClientFromPMS(
      org_type == "" ? null : org_type,
      org_name == "" ? null : org_name,
      lowerEmail,
      password,
      lf_id,
      title,
      first_name,
      middle_name == "" ? null : middle_name,
      last_name,
      dob,
      gender == "" ? null : gender,
      home_phone == "" ? null : home_phone,
      mb_phone == "" ? null : mb_phone,
      wrk_phone == "" ? null : wrk_phone,
      adr_1 == "" ? null : adr_1,
      adr_2 == "" ? null : adr_2,
      adr_3 == "" ? null : adr_3,
      state,
      town,
      country,
      post_code,
      pms_id
    );
    return res.status(200).json({
      success: true,
      message: "User added",
    });
  }
});

export const createKeyForLawFirm = catchAsync(async (req, res) => {
  if (req.user.role != roles.Admin) {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
  const key = await userService.createKeyForLawFirm(req.body.lf_id);
  return res.status(200).json({
    success: true,
    message: "Key",
    data: key,
  });
});

export const regenKeyForLawFirm = catchAsync(async (req, res) => {
  if (req.user.role != roles.Admin) {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
  const key = await userService.regenKeyForLawFirm(req.body.lf_id);
  return res.status(200).json({
    success: true,
    message: "Key",
    data: key,
  });
});

export const tokenForAddClientPMS = catchAsync(async (req, res) => {
  const token = await userService.tokenForAddClientPMS(req.body.key);
  if (token == 0) {
    return res.status(200).json({
      success: false,
      message: "Invalid key",
    });
  }
  return res.status(200).json({
    success: true,
    message: "Token",
    data: token,
  });
});

export const updateProfileAdmin = catchAsync(async (req, res) => {
  const role_ = req.user.role;

  if (role_ == roles.Admin) {
    const {
      new_email,
      title = null,
      first_name,
      middle_name,
      last_name,
      mb_phone,
      country,
      user_id,
    } = req.body;
    const email = req.user.email;
    let cur_user = await userService.findUserById(user_id);
    if (cur_user.length === 0) {
      return res.status(500).json({
        success: false,
        message: "User not found",
      });
    }
    if (cur_user[0].role == roles.Admin && role_ != roles.Admin) {
      console.log("Only admin can update admin");
      return res.status(500).json({
        success: false,
        message: "Permission denied!",
      });
    }

    if (new_email == null || new_email == "" || new_email == undefined) {
      return res.status(500).json({
        success: false,
        message: "Email is required",
      });
    }
    const lowerEmail = new_email.toLowerCase();
    if (!validator.isEmail(lowerEmail)) {
      return res.status(500).json({
        success: false,
        message: "Invalid email",
      });
    } else if (validator.isEmpty(last_name)) {
      return res.status(500).json({
        success: false,
        message: "Surname is required",
      });
    } else if (validator.isEmpty(mb_phone)) {
      return res.status(500).json({
        success: false,
        message: "Mobile phone number is required",
      });
    } else if (mb_phone && !validator.isMobilePhone(mb_phone)) {
      return res.status(500).json({
        success: false,
        message: "Invalid mobile phone number",
      });
    } else {
      const token = await userService.updateProfileAdmin(
        lowerEmail,
        title,
        first_name,
        middle_name == "" ? null : middle_name,
        last_name,
        mb_phone == "" ? null : mb_phone,
        country,
        user_id
      );
      return res.status(200).json({
        success: true,
        message: "User updated",
      });
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const updateConnectPMS = catchAsync(async (req, res) => {
  const role_ = req.user.role;
  if (role_ == roles.Admin) {
    const { lf_id } = req.body;
    await userService.updateStatusConnectPMS(lf_id);
    return res.status(200).json({
      success: true,
      message: "Done",
    });
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const updateDeletionTime = catchAsync(async (req, res) => {
  if (req.user.role == roles.Admin) {
    try {
      let result = await userService.updateDeletionTime(
        req.body.lf_id,
        req.body.time
      );
      return res.status(200).json({
        success: true,
        message: "done",
        data: result,
      });
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  } else if (
    req.user.role == roles.LawfirmSuperAdmin ||
    req.user.role == roles.LawfirmAdmin
  ) {
    if (req.body.lf_id != req.user.lf_id) {
      return res.status(500).json({
        success: false,
        message: "Permission denied!",
      });
    } else {
      try {
        let result = await userService.updateDeletionTime(
          req.body.lf_id,
          req.body.time
        );
        return res.status(200).json({
          success: true,
          message: "done",
          data: result,
        });
      } catch (error) {
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }
    }
  }
});
export const getDeletionTime = catchAsync(async (req, res) => {
  if (req.user.role == roles.Admin) {
    try {
      let result = await userService.getDeletionTime(req.body.lf_id);
      return res.status(200).json({
        success: true,
        message: "done",
        data: result,
      });
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  } else if (
    req.user.role == roles.LawfirmSuperAdmin ||
    req.user.role == roles.LawfirmAdmin
  ) {
    if (req.body.lf_id != req.user.lf_id) {
      return res.status(500).json({
        success: false,
        message: "Permission denied!",
      });
    } else {
      try {
        let result = await userService.getDeletionTime(req.body.lf_id);
        return res.status(200).json({
          success: true,
          message: "done",
          data: result,
        });
      } catch (error) {
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }
    }
  }
});

//============================GRAPH CREDENTIALS=================================

/**
 * Get Graph credentials status for law firm details
 */
export const getLawFirmDetailsWithGraph = catchAsync(async (req, res) => {
  const role_ = req.user.role;
  if (role_ == roles.Admin) {
    const { lf_id } = req.body;
    try {
      // Get law firm details
      let checkLawfirm = await userService.getLawfirmDetails(lf_id);
      if (checkLawfirm.length == 0) {
        return res.status(500).json({
          success: false,
          message: "Error getting law firm",
        });
      }

      // Get Graph credentials status
      const graphCredentials = await graphCredentialsService.getCredentials(lf_id, false);
      const providerInfo = await mailProviderService.getProviderInfo(lf_id);

      return res.status(200).json({
        success: true,
        message: "Law firm details fetched",
        data: {
          ...checkLawfirm[0],
          graphCredentials: graphCredentials ? {
            id: graphCredentials.id,
            tenant_id: graphCredentials.tenant_id,
            client_id: graphCredentials.client_id,
            is_active: graphCredentials.is_active,
            created_at: graphCredentials.created_at,
            updated_at: graphCredentials.updated_at
          } : null,
          mailProvider: providerInfo
        },
      });
    } catch (error) {
      return res.status(500).json({
        success: false,
        message: "Error getting law firm details",
      });
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});
