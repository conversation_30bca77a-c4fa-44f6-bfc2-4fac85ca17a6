import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from "#middlewares/ErrorClass.js";
import { runQuery } from "#utils";
import pool from "../config/db.js";
import * as fs from "fs";
import crypto from "crypto";

/**
 * API Token Service
 * Manages firm-specific API tokens for AdminAPI users
 */
class ApiTokenService {
  
  /**
   * Generate a secure API token
   * @returns {Object} Token object with token and hash
   */
  generateToken() {
    // Generate a random token
    const token = crypto.randomBytes(32).toString('hex');
    
    // Create a prefix for easy identification (first 8 chars)
    const prefix = token.substring(0, 8);
    
    // Hash the token for storage
    const hash = crypto.createHash('sha256').update(token).digest('hex');
    
    return {
      token: `propero_${prefix}_${token}`,
      hash,
      prefix: `propero_${prefix}`
    };
  }

  /**
   * Hash a token for verification
   * @param {string} token - The full token to hash
   * @returns {string} Token hash
   */
  hashToken(token) {
    // Remove prefix if present
    const cleanToken = token.replace(/^propero_[a-f0-9]{8}_/, '');
    return crypto.createHash('sha256').update(cleanToken).digest('hex');
  }

  /**
   * Create a new API token for a law firm user
   * @param {Object} params - Token creation parameters
   * @returns {Object} Created token information
   */
  async createToken({
    lf_id,
    user_id,
    token_name,
    permissions = null,
    expires_at = null,
    created_by
  }) {
    let con = await pool.getConnection();
    try {
      await con.beginTransaction();

      // Verify user is AdminAPI role and belongs to the law firm
      const userCheck = await runQuery(
        con,
        "SELECT user_id, role, lf_id, status FROM users WHERE user_id = ? AND role = '6' AND lf_id = ? AND status IN (1, 3)",
        [user_id, lf_id]
      );

      if (userCheck.length === 0) {
        throw ErrorHandler.badRequestError("User must be AdminAPI role and belong to the specified law firm");
      }

      // Validate created_by user exists if provided
      let validatedCreatedBy = null;
      if (created_by) {
        const createdByCheck = await runQuery(
          con,
          "SELECT user_id FROM users WHERE user_id = ?",
          [created_by]
        );
        if (createdByCheck.length > 0) {
          validatedCreatedBy = created_by;
        }
      }

      // Generate token
      const { token, hash, prefix } = this.generateToken();

      // Insert token into database
      const sql = fs.readFileSync("app/sql/addApiToken.sql").toString();
      const result = await runQuery(con, sql, [
        lf_id,
        user_id,
        token_name,
        hash,
        prefix,
        permissions ? JSON.stringify(permissions) : null,
        true,
        expires_at,
        validatedCreatedBy
      ]);

      await con.commit();

      return {
        id: result.insertId,
        token, // Only returned once during creation
        token_name,
        prefix,
        expires_at,
        created_at: new Date()
      };

    } catch (error) {
      await con.rollback();
      console.error('API Token creation error:', error);

      // Provide more specific error messages
      if (error.code === 'ER_NO_REFERENCED_ROW_2') {
        if (error.message.includes('fk_api_tokens_created_by')) {
          throw ErrorHandler.badRequestError("The creating user does not exist in the system");
        } else if (error.message.includes('fk_api_tokens_user_id')) {
          throw ErrorHandler.badRequestError("The target user does not exist in the system");
        } else if (error.message.includes('fk_api_tokens_lf_id')) {
          throw ErrorHandler.badRequestError("The law firm does not exist in the system");
        }
      }

      throw ErrorHandler.badRequestError(error.message);
    } finally {
      con.destroy();
    }
  }

  /**
   * Verify and get token information
   * @param {string} token - The API token to verify
   * @returns {Object|null} Token information if valid
   */
  async verifyToken(token) {
    let con = await pool.getConnection();
    try {
      const hash = this.hashToken(token);
      
      const sql = fs.readFileSync("app/sql/getApiTokenByHash.sql").toString();
      const result = await runQuery(con, sql, [hash]);

      if (result.length === 0) {
        return null;
      }

      const tokenData = result[0];

      // Update last used timestamp
      const updateSql = fs.readFileSync("app/sql/updateApiTokenLastUsed.sql").toString();
      await runQuery(con, updateSql, [hash]);

      return {
        id: tokenData.id,
        lf_id: tokenData.lf_id,
        user_id: tokenData.user_id,
        email: tokenData.email,
        role: tokenData.role,
        token_name: tokenData.token_name,
        permissions: tokenData.permissions ? JSON.parse(tokenData.permissions) : null,
        lf_org_name: tokenData.lf_org_name,
        lf_status: tokenData.lf_status
      };

    } catch (error) {
      throw ErrorHandler.badRequestError(error.message);
    } finally {
      con.destroy();
    }
  }

  /**
   * Get all tokens for a law firm
   * @param {number} lf_id - Law firm ID
   * @returns {Array} List of tokens (without sensitive data)
   */
  async getTokensByLawFirm(lf_id) {
    let con = await pool.getConnection();
    try {
      const sql = fs.readFileSync("app/sql/getApiTokensByLawFirm.sql").toString();
      const result = await runQuery(con, sql, [lf_id]);

      return result.map(token => ({
        id: token.id,
        token_name: token.token_name,
        token_prefix: token.token_prefix,
        user_email: token.email,
        user_name: token.user_name,
        permissions: token.permissions ? JSON.parse(token.permissions) : null,
        is_active: token.is_active,
        expires_at: token.expires_at,
        last_used_at: token.last_used_at,
        created_at: token.created_at
      }));

    } catch (error) {
      throw ErrorHandler.badRequestError(error.message);
    } finally {
      con.destroy();
    }
  }

  /**
   * Revoke an API token
   * @param {number} token_id - Token ID to revoke
   * @param {number} lf_id - Law firm ID (for security)
   * @param {number} updated_by - User ID performing the revocation
   * @returns {boolean} Success status
   */
  async revokeToken(token_id, lf_id, updated_by) {
    let con = await pool.getConnection();
    try {
      await con.beginTransaction();

      const sql = fs.readFileSync("app/sql/revokeApiToken.sql").toString();
      const result = await runQuery(con, sql, [updated_by, token_id, lf_id]);

      await con.commit();

      return result.affectedRows > 0;

    } catch (error) {
      await con.rollback();
      throw ErrorHandler.badRequestError(error.message);
    } finally {
      con.destroy();
    }
  }
}

export default new ApiTokenService();
