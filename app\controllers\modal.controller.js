import { modalService } from "#services";
import { catchAsync } from "#utils";
import { roles } from "../middlewares/roles.js";

const generateId = () => {
  let d = new Date().getTime(),
    d2 =
      (typeof performance !== "undefined" &&
        performance.now &&
        performance.now() * 1000) ||
      0;
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (c) => {
    let r = Math.random() * 16;
    if (d > 0) {
      r = (d + r) % 16 | 0;
      d = Math.floor(d / 16);
    } else {
      r = (d2 + r) % 16 | 0;
      d2 = Math.floor(d2 / 16);
    }
    return (c == "x" ? r : (r & 0x7) | 0x8).toString(16);
  });
};

export const createModal = catchAsync(async (req, res) => {
  const role = req.user.role;
  if (role == roles.Admin) {
    try {
      const modal = await modalService.createModal(req.body);
      return res.status(200).json({
        success: true,
        message: "Modal created",
        modal,
      });
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const getModal = catchAsync(async (req, res) => {
  const { id } = req.body;
  try {
    const modal = await modalService.getModal(id);
    return res.status(200).json({
      success: true,
      message: "Modal fetched",
      modal,
    });
  } catch (error) {
    return res.status(400).json({
      success: false,
      message: error.message,
    });
  }
});
export const getAllModal = catchAsync(async (req, res) => {
  const role = req.user.role;
  const { page, size, keyword, status } = req.body;
  if (role == roles.Admin || role == roles.LawfirmSuperAdmin) {
    try {
      console.log(req.body);
      const modal = await modalService.getAllModal(page, size, keyword, status);
      return res.status(200).json({
        success: true,
        message: "Modal fetched",
        modal,
      });
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const deleteModal = catchAsync(async (req, res) => {
  const role = req.user.role;
  if (role == roles.Admin) {
    const { id } = req.body;
    try {
      const template = await modalService.deleteModal(id);
      return res.status(200).json({
        success: true,
        message: "Modal deleted",
      });
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});
export const updateModalStatus = catchAsync(async (req, res) => {
  const role = req.user.role;
  if (role == roles.Admin) {
    let check = await modalService.getModal(req.body.id);
    if (check.length == 0) {
      return res.status(400).json({
        success: false,
        message: "Modal does not exist",
      });
    } else {
      try {
        const template = await modalService.updateModalStatus(req.body);
        return res.status(200).json({
          success: true,
          message: "Modal updated",
        });
      } catch (error) {
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});
export const updateModal = catchAsync(async (req, res) => {
  const role = req.user.role;
  if (role == roles.Admin) {
    let check = await modalService.getModal(req.body.id);
    if (check.length == 0) {
      return res.status(400).json({
        success: false,
        message: "Modal does not exist",
      });
    } else {
      try {
        const modal = await modalService.updateModal(req.body);
        return res.status(200).json({
          success: true,
          message: "Modal updated",
        });
      } catch (error) {
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const insertModalAnswer = catchAsync(async (req, res) => {
  const role = req.user.role;
  if (role == roles.Admin) {
    try {
      const modal = await modalService.insertModalAnswer(req.body);
      return res.status(200).json({
        success: true,
        message: "Modal answer inserted",
      });
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const getModalType = catchAsync(async (req, res) => {
  try {
    const modal_type = await modalService.getModalType();
    return res.status(200).json({
      success: true,
      message: "Modal type fetched",
      modal_type,
    });
  } catch (error) {
    return res.status(400).json({
      success: false,
      message: error.message,
    });
  }
});
export const addModalType = catchAsync(async (req, res) => {
  const role = req.user.role;
  if (
    role == roles.Admin ||
    role == roles.LawfirmSuperAdmin ||
    role == roles.LawfirmAdmin ||
    role == roles.LawfirmUser
  ) {
    try {
      const modal = await modalService.addModalType(req.body.name);
      return res.status(200).json({
        success: true,
        message: "Modal type added",
        modalId: modal,
      });
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const deleteModalType = catchAsync(async (req, res) => {
  const role = req.user.role;
  if (role == roles.Admin) {
    try {
      const modal = await modalService.deleteModalType(req.body.id);
      return res.status(200).json({
        success: true,
        message: "Modal type deleted",
      });
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});
