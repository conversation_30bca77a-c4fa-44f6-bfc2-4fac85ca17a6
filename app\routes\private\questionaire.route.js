import express from "express";
import {
  getAll<PERSON><PERSON>ionaire,
  create<PERSON><PERSON>ionaire,
  getQuestionaire,
  deleteQuestionaire,
  updateQuestionaire,
  requestUpdateQuestionaire,
  updateQuestionaireStatus,
  createLink,
  modifyQuestion,
  getCompletedRequests,
  findRequest,
  getAllQuestionaireForPMS,
  updateQuestionaireExportConfig,
  getQuestionaireExportConfig,
} from "#controllers/questionaire.controller.js";

/**
 * @swagger
 * components:
 *   schemas:
 *     Questionnaire:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: Questionnaire ID
 *         qtn_name:
 *           type: string
 *           description: Questionnaire name
 *         tem_id:
 *           type: integer
 *           description: Template ID
 *         lf_id:
 *           type: integer
 *           description: Law firm ID
 *         price:
 *           type: number
 *           description: Price of questionnaire
 *         status:
 *           type: integer
 *           description: Status of questionnaire (0=draft, 1=active, 2=inactive, 3=pending)
 *     Error:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           example: false
 *         message:
 *           type: string
 */

const questionaireRoutes = express.Router();

/**
 * @swagger
 * /private/questionaire/createQuestionaire:
 *   post:
 *     summary: Create a new questionnaire
 *     tags: [Questionnaire]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - qtn_name
 *               - tem_id
 *               - lf_id
 *               - price
 *             properties:
 *               qtn_name:
 *                 type: string
 *               tem_id:
 *                 type: integer
 *               lf_id:
 *                 type: integer
 *               price:
 *                 type: number
 *     responses:
 *       200:
 *         description: Questionnaire created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Questionnaire created
 *                 questionaire_id:
 *                   type: integer
 *       400:
 *         description: Invalid request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */

questionaireRoutes.route("/createQuestionaire").post(createQuestionaire);

/**
 * @swagger
 * /private/questionaire/getAllQuestionaire:
 *   post:
 *     summary: Get all questionnaires with pagination
 *     tags: [Questionnaire]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - page
 *               - size
 *             properties:
 *               page:
 *                 type: integer
 *                 description: Page number
 *               size:
 *                 type: integer
 *                 description: Items per page
 *               keyword:
 *                 type: string
 *                 description: Search keyword
 *               status:
 *                 type: integer
 *                 description: Filter by status
 *               lf_id:
 *                 type: integer
 *                 description: Law firm ID (required for Admin role only)
 *     responses:
 *       200:
 *         description: List of questionnaires
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: ok
 *                 data:
 *                   type: object
 *                   properties:
 *                     items:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Questionnaire'
 *                     total:
 *                       type: integer
 *       400:
 *         description: Invalid request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Permission denied
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
questionaireRoutes.route("/getAllQuestionaire").post(getAllQuestionaire);

/**
 * @swagger
 * /private/questionaire/getQuestionaire:
 *   post:
 *     summary: Get a specific questionnaire by ID
 *     tags: [Questionnaire]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - id
 *             properties:
 *               id:
 *                 type: integer
 *                 description: Questionnaire ID
 *     responses:
 *       200:
 *         description: Questionnaire details
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Questionnaire fetched
 *                 questionaire:
 *                   $ref: '#/components/schemas/Questionnaire'
 *       400:
 *         description: Invalid request or questionnaire not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
questionaireRoutes.route("/getQuestionaire").post(getQuestionaire);

/**
 * @swagger
 * /private/questionaire/deleteQuestionaire:
 *   post:
 *     summary: Delete a questionnaire (Admin only)
 *     tags: [Questionnaire]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - id
 *             properties:
 *               id:
 *                 type: integer
 *                 description: Questionnaire ID to delete
 *     responses:
 *       200:
 *         description: Questionnaire deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Questionnaire deleted
 *       400:
 *         description: Invalid request or questionnaire not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Permission denied (non-admin users)
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
questionaireRoutes.route("/deleteQuestionaire").post(deleteQuestionaire);

/**
 * @swagger
 * /private/questionaire/updateQuestionaire:
 *   post:
 *     summary: Update a questionnaire (Admin or LawfirmSuperAdmin only)
 *     tags: [Questionnaire]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - id
 *             properties:
 *               id:
 *                 type: integer
 *                 description: Questionnaire ID to update
 *               qtn_name:
 *                 type: string
 *                 description: New questionnaire name
 *               tem_id:
 *                 type: integer
 *                 description: New template ID
 *               price:
 *                 type: number
 *                 description: New price
 *     responses:
 *       200:
 *         description: Questionnaire updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Questionnaire updated
 *       400:
 *         description: Invalid request or questionnaire not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Permission denied (insufficient role)
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
questionaireRoutes.route("/updateQuestionaire").post(updateQuestionaire);

/**
 * @swagger
 * /private/questionaire/updateQuestionaireStatus:
 *   post:
 *     summary: Update questionnaire status (Admin or LawfirmSuperAdmin only)
 *     tags: [Questionnaire]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - id
 *               - status
 *             properties:
 *               id:
 *                 type: integer
 *                 description: Questionnaire ID
 *               status:
 *                 type: integer
 *                 description: New status (0=draft, 1=active, 2=inactive, 3=pending)
 *     responses:
 *       200:
 *         description: Status updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Questionnaire updated
 *       400:
 *         description: Invalid request or questionnaire not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Permission denied (insufficient role)
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
questionaireRoutes
  .route("/updateQuestionaireStatus")
  .post(updateQuestionaireStatus);

/**
 * @swagger
 * /private/questionaire/requestUpdateQuestionaire:
 *   post:
 *     summary: Request to update a questionnaire (LawfirmAdmin or LawfirmUser only)
 *     tags: [Questionnaire]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - qtn_id
 *             properties:
 *               qtn_id:
 *                 type: integer
 *                 description: Questionnaire ID to request update for
 *     responses:
 *       200:
 *         description: Update request submitted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Questionnaire update requested
 *       400:
 *         description: Invalid request, questionnaire not found, or already has pending request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Permission denied (wrong role or law firm)
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
questionaireRoutes
  .route("/requestUpdateQuestionaire")
  .post(requestUpdateQuestionaire);
/**
 * @swagger
 * /private/questionaire/createLink:
 *   post:
 *     summary: Create a link for a questionnaire (Admin or LawfirmSuperAdmin only)
 *     tags: [Questionnaire]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - questionaire_id
 *             properties:
 *               questionaire_id:
 *                 type: integer
 *                 description: ID of the questionnaire to create link for
 *     responses:
 *       200:
 *         description: Link created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Link created
 *                 questionnaire:
 *                   type: object
 *       400:
 *         description: Invalid request or questionnaire not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Permission denied (insufficient role)
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
questionaireRoutes.route("/createLink").post(createLink);

/**
 * @swagger
 * /private/questionaire/findRequest:
 *   post:
 *     summary: Find questionnaire requests by status (Non-Client users only)
 *     tags: [Questionnaire]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - qtn_id
 *               - status
 *             properties:
 *               qtn_id:
 *                 type: integer
 *                 description: Questionnaire ID
 *               status:
 *                 type: integer
 *                 description: Request status to filter by
 *     responses:
 *       200:
 *         description: Request details fetched successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Request fetched
 *                 request:
 *                   type: object
 *       400:
 *         description: Invalid request or questionnaire not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Permission denied (Client role or wrong law firm)
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
questionaireRoutes.route("/findRequest").post(findRequest);

/**
 * @swagger
 * /private/questionaire/modifyQuestion:
 *   post:
 *     summary: Modify questions in a questionnaire (Admin or LawfirmSuperAdmin only)
 *     tags: [Questionnaire]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - questionnaire_id
 *               - questions
 *             properties:
 *               questionnaire_id:
 *                 type: integer
 *                 description: ID of the questionnaire to modify
 *               questions:
 *                 type: array
 *                 description: Array of modified questions
 *                 items:
 *                   type: object
 *     responses:
 *       200:
 *         description: Questions modified successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Question Edited
 *                 questionnaire:
 *                   type: object
 *       400:
 *         description: Invalid request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Permission denied (insufficient role)
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
questionaireRoutes.route("/modifyQuestion").post(modifyQuestion);

/**
 * @swagger
 * /private/questionaire/getCompletedRequests:
 *   post:
 *     summary: Get completed requests for a questionnaire
 *     tags: [Questionnaire]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - id
 *             properties:
 *               id:
 *                 type: integer
 *                 description: Questionnaire ID
 *     responses:
 *       200:
 *         description: Completed requests fetched successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Requests fetched
 *                 requests:
 *                   type: array
 *                   items:
 *                     type: object
 *       400:
 *         description: Invalid request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
questionaireRoutes.route("/getCompletedRequests").post(getCompletedRequests);

/**
 * @swagger
 * /private/questionaire/getAllQuestionaireForPMS:
 *   post:
 *     summary: Get questionnaires for PMS with pagination
 *     tags: [Questionnaire]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - page
 *               - size
 *             properties:
 *               page:
 *                 type: integer
 *                 description: Page number
 *               size:
 *                 type: integer
 *                 description: Items per page
 *               keyword:
 *                 type: string
 *                 description: Search keyword
 *               status:
 *                 type: integer
 *                 description: Filter by status
 *     responses:
 *       200:
 *         description: List of questionnaires for PMS
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: ok
 *                 data:
 *                   type: object
 *                   properties:
 *                     items:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Questionnaire'
 *                     total:
 *                       type: integer
 *       400:
 *         description: Invalid request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
questionaireRoutes
  .route("/getAllQuestionaireForPMS")
  .post(getAllQuestionaireForPMS);

/**
 * @swagger
 * /private/questionaire/{id}/export-config:
 *   put:
 *     summary: Update questionnaire export configuration
 *     tags: [Questionnaire]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Questionnaire ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               pms_enabled:
 *                 type: boolean
 *                 description: Enable PMS export (PDF and Data to PMS)
 *               data_enabled:
 *                 type: boolean
 *                 description: Enable Data export (Excel/CSV spreadsheets)
 *               form_enabled:
 *                 type: boolean
 *                 description: Enable Form export (Form Evo integration)
 *               form_names:
 *                 type: string
 *                 description: Comma-separated list of form names for Form Evo
 *                 example: "form1,form2,form3"
 *     responses:
 *       200:
 *         description: Export configuration updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Questionnaire export configuration updated successfully"
 *       500:
 *         description: Permission denied (Admin or LawfirmSuperAdmin role required)
 *   get:
 *     summary: Get questionnaire export configuration
 *     tags: [Questionnaire]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Questionnaire ID
 *     responses:
 *       200:
 *         description: Questionnaire export configuration
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     pms_enabled:
 *                       type: boolean
 *                       description: PMS export enabled
 *                     data_enabled:
 *                       type: boolean
 *                       description: Data export enabled
 *                     form_enabled:
 *                       type: boolean
 *                       description: Form export enabled
 *                     form_names:
 *                       type: string
 *                       description: Comma-separated form names
 */
questionaireRoutes.put("/:id/export-config", updateQuestionaireExportConfig);
questionaireRoutes.get("/:id/export-config", getQuestionaireExportConfig);

export default questionaireRoutes;

