import {
  editStatement,
  editTemplateMail,
  getTemplateMail,
  uploadLogo,
} from "#controllers/customMail.controller.js";
import express from "express";

const customMailRoutes = express.Router();

/**
 * @swagger
 * /private/customMail/editStatement:
 *   post:
 *     summary: Edit law firm statement
 *     description: Update the statement for a law firm. Requires LawfirmSuperAdmin role for the specific law firm or Admin role.
 *     tags: [Custom Mail]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - lf_id
 *               - lf_statement
 *             properties:
 *               lf_id:
 *                 type: integer
 *                 description: Law firm ID
 *               lf_statement:
 *                 type: string
 *                 description: New statement text
 *     responses:
 *       200:
 *         description: Statement updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Success
 *       400:
 *         description: Invalid request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Please check again
 *       500:
 *         description: Permission denied or server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Permission denied!
 */
customMailRoutes.route("/editStatement").post(editStatement);

/**
 * @swagger
 * /private/customMail/editTemplateMail:
 *   post:
 *     summary: Edit email template
 *     description: Update the email template for a law firm. Requires LawfirmSuperAdmin role for the specific law firm.
 *     tags: [Custom Mail]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - lf_id
 *               - mail_type
 *             properties:
 *               logo_name:
 *                 type: string
 *                 description: Name of the logo file
 *               title:
 *                 type: string
 *                 description: Email title
 *               header:
 *                 type: string
 *                 description: Email header content
 *               body:
 *                 type: string
 *                 description: Email body content
 *               footer:
 *                 type: string
 *                 description: Email footer content
 *               logo:
 *                 type: string
 *                 description: Logo URL or base64 string
 *               lf_id:
 *                 type: integer
 *                 description: Law firm ID
 *               mail_type:
 *                 type: string
 *                 description: Type of email template
 *               remind_time:
 *                 type: integer
 *                 description: Reminder time in days
 *     responses:
 *       200:
 *         description: Template updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Success
 *       400:
 *         description: Invalid request
 *       500:
 *         description: Permission denied or server error
 */
customMailRoutes.route("/editTemplateMail").post(editTemplateMail);

/**
 * @swagger
 * /private/customMail/getTemplateMail:
 *   get:
 *     summary: Get email template
 *     description: Retrieve the email template for a law firm. Requires LawfirmSuperAdmin role for the specific law firm.
 *     tags: [Custom Mail]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: lf_id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Law firm ID
 *       - in: query
 *         name: mail_type
 *         required: true
 *         schema:
 *           type: string
 *         description: Type of email template
 *     responses:
 *       200:
 *         description: Template retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Template Mail fetched
 *                 data:
 *                   type: object
 *                   description: Template data
 *       500:
 *         description: Permission denied or server error
 */
customMailRoutes.route("/getTemplateMail").get(getTemplateMail);

/**
 * @swagger
 * /private/customMail/uploadLogo:
 *   post:
 *     summary: Upload law firm logo
 *     description: Upload a logo for the law firm's email templates. Requires LawfirmSuperAdmin role for the specific law firm.
 *     tags: [Custom Mail]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - lf_id
 *               - file
 *             properties:
 *               lf_id:
 *                 type: integer
 *                 description: Law firm ID
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: Logo file (base64 encoded)
 *     responses:
 *       200:
 *         description: Logo uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Logo uploaded
 *                 data:
 *                   type: object
 *                   description: Upload result data
 *       400:
 *         description: No file uploaded
 *       500:
 *         description: Permission denied or server error
 */
customMailRoutes.route("/uploadLogo").post(uploadLogo);

export default customMailRoutes;
