import jwt from "jsonwebtoken";
import env from "dotenv";

env.config();

export const generateToken = (
  user_id,
  email,
  role,
  lf_id,
  secret = process.env.JWT_SECRET,
  expirey_time = process.env.JWT_EXPIRY_TIME
) => {
  return jwt.sign({ user_id, email, role, lf_id }, secret, {
    expiresIn: expirey_time,
  });
};
// console.log(generateToken(null,null,"6",null,process.env.JWT_SECRET,"365d"));
export const generateTempToken = (reason) => {
  return jwt.sign({ reason }, process.env.JWT_SECRET, {
    expiresIn: "1h",
  });
};

export const generateTempTokenLogin = (reason) => {
  return jwt.sign({ reason }, process.env.JWT_SECRET, {
    expiresIn: "5m",
  });
};
