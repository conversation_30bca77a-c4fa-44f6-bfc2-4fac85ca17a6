import express from "express";
import {
  createModal,
  getModal,
  deleteModal,
  updateModal,
  getAllModal,
  updateModalStatus,
  insertModalAnswer,
  getModalType,
  addModalType,
  deleteModalType,
} from "#controllers/modal.controller.js";

/**
 * @swagger
 * components:
 *   schemas:
 *     Modal:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Modal unique identifier
 *         name:
 *           type: string
 *           description: Modal name
 *         type:
 *           type: string
 *           description: Modal type
 *         status:
 *           type: string
 *           enum: [active, inactive]
 *           description: Modal status
 *         content:
 *           type: object
 *           description: Modal content and configuration
 *     ModalAnswer:
 *       type: object
 *       properties:
 *         modalId:
 *           type: string
 *           description: Modal identifier
 *         answers:
 *           type: object
 *           description: Answer content
 *     ModalType:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Type identifier
 *         name:
 *           type: string
 *           description: Type name
 */

const modalRoute = express.Router();

/**
 * @swagger
 * /private/modal/createModal:
 *   post:
 *     summary: Create a new modal
 *     tags: [Modal]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               type:
 *                 type: string
 *               content:
 *                 type: object
 *     responses:
 *       200:
 *         description: Modal created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 modal:
 *                   $ref: '#/components/schemas/Modal'
 *       400:
 *         description: Invalid request
 *       500:
 *         description: Permission denied or server error
 */
modalRoute.route("/createModal").post(createModal);

/**
 * @swagger
 * /private/modal/getModal:
 *   post:
 *     summary: Get modal by ID
 *     tags: [Modal]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               id:
 *                 type: string
 *                 description: Modal ID
 *     responses:
 *       200:
 *         description: Modal details fetched successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 modal:
 *                   $ref: '#/components/schemas/Modal'
 *       400:
 *         description: Invalid request or modal not found
 */
modalRoute.route("/getModal").post(getModal);

/**
 * @swagger
 * /private/modal/deleteModal:
 *   post:
 *     summary: Delete a modal
 *     tags: [Modal]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               id:
 *                 type: string
 *                 description: Modal ID to delete
 *     responses:
 *       200:
 *         description: Modal deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *       400:
 *         description: Invalid request or modal not found
 *       500:
 *         description: Permission denied or server error
 */
modalRoute.route("/deleteModal").post(deleteModal);

/**
 * @swagger
 * /private/modal/updateModal:
 *   post:
 *     summary: Update modal details
 *     tags: [Modal]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [id]
 *             properties:
 *               id:
 *                 type: string
 *                 description: Modal ID
 *               name:
 *                 type: string
 *                 description: New modal name
 *               type:
 *                 type: string
 *                 description: New modal type
 *               content:
 *                 type: object
 *                 description: Updated modal content
 *     responses:
 *       200:
 *         description: Modal updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *       400:
 *         description: Invalid request or modal not found
 *       500:
 *         description: Permission denied or server error
 */
modalRoute.route("/updateModal").post(updateModal);

/**
 * @swagger
 * /private/modal/getAllModal:
 *   post:
 *     summary: Get all modals with pagination and filtering
 *     tags: [Modal]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               page:
 *                 type: number
 *                 description: Page number
 *               size:
 *                 type: number
 *                 description: Items per page
 *               keyword:
 *                 type: string
 *                 description: Search keyword
 *               status:
 *                 type: string
 *                 enum: [active, inactive]
 *                 description: Filter by status
 *     responses:
 *       200:
 *         description: Modals fetched successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 modal:
 *                   type: object
 *                   properties:
 *                     rows:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Modal'
 *                     count:
 *                       type: number
 *       400:
 *         description: Invalid request
 *       500:
 *         description: Permission denied or server error
 */
modalRoute.route("/getAllModal").post(getAllModal);

/**
 * @swagger
 * /private/modal/updateModalStatus:
 *   post:
 *     summary: Update modal status
 *     tags: [Modal]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [id, status]
 *             properties:
 *               id:
 *                 type: string
 *                 description: Modal ID
 *               status:
 *                 type: string
 *                 enum: [active, inactive]
 *                 description: New status
 *     responses:
 *       200:
 *         description: Modal status updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *       400:
 *         description: Invalid request or modal not found
 *       500:
 *         description: Permission denied or server error
 */
modalRoute.route("/updateModalStatus").post(updateModalStatus);

/**
 * @swagger
 * /private/modal/insertModalAnswer:
 *   post:
 *     summary: Insert answers for a modal
 *     tags: [Modal]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ModalAnswer'
 *     responses:
 *       200:
 *         description: Modal answer inserted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *       400:
 *         description: Invalid request
 *       500:
 *         description: Permission denied or server error
 */
modalRoute.route("/insertModalAnswer").post(insertModalAnswer);

/**
 * @swagger
 * /private/modal/getModalType:
 *   get:
 *     summary: Get all modal types
 *     tags: [Modal]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Modal types fetched successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 modal_type:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ModalType'
 *       400:
 *         description: Invalid request
 */
modalRoute.route("/getModalType").get(getModalType);

/**
 * @swagger
 * /private/modal/addModalType:
 *   post:
 *     summary: Add a new modal type
 *     tags: [Modal]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [name]
 *             properties:
 *               name:
 *                 type: string
 *                 description: Name of the modal type
 *     responses:
 *       200:
 *         description: Modal type added successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 modalId:
 *                   type: string
 *       400:
 *         description: Invalid request
 *       500:
 *         description: Permission denied or server error
 */
modalRoute.route("/addModalType").post(addModalType);

/**
 * @swagger
 * /private/modal/deleteModalType:
 *   post:
 *     summary: Delete a modal type
 *     tags: [Modal]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [id]
 *             properties:
 *               id:
 *                 type: string
 *                 description: Modal type ID to delete
 *     responses:
 *       200:
 *         description: Modal type deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *       400:
 *         description: Invalid request
 *       500:
 *         description: Permission denied or server error
 */
modalRoute.route("/deleteModalType").post(deleteModalType);

export default modalRoute;
