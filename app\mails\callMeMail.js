import sgMail from "@sendgrid/mail";
import dotenv from "dotenv";

dotenv.config();
export const callMeMail = async (
  email,
  first_name,
  phone_number,
  best_time
) => {
  sgMail.setApiKey(process.env.SENDGRID_API_KEY);
  let msg = {
    from: { name: "Propero Support", email: "<EMAIL>" },
    to: email,
    subject: `Callback Request`,
    html: `<!DOCTYPE html>
        <html lang="en">
        
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Document</title>
            <style>
                * {
                    box-sizing: border-box;
                }
        
                body {
                    margin: 0;
                    padding: 0;
                    font-family: 'Inter', sans-serif;
                    background-color: #fff;
                }
        
                p {
                    margin: 0;
                }
        
                .card {
                    padding: 24px;
                    width: 460px;
                    border: 1px solid rgba(0, 0, 0, 0.75);
                }
                .card .logo {
                    width: 80px;
                    height: 80px;
                }
                .card .logo-wrapper {
                    text-align: center;
                    align-items: center;
                    margin-bottom: 20px;
                }
        
                .card .notification-wrapper {
                    background-color: #F5F5F5;
                    height: 54px;
                    padding: 0 8px;
                }
                
                .card .notification-wrapper .notification-text {
                    line-height: 54px;
                    color: #000;
                    font-weight: 800;
                    font-size: 14px;
                }
        
                .card .description-wrapper {
                    margin: 8px;
                    color: #94A3B8;
                    font-size: 11px;
                }
        
                .card .message-wrapper {
                    color: #667085;
                    font-size: 14px;
                    line-height: 1.5;
                    margin: 0 8px;
                    gap: 4px;
                }
        
                .card .callback-details {
                    margin: 16px 8px;
                    padding: 16px;
                    background-color: #F8F9FC;
                    border-radius: 8px;
                }

                .card .callback-details p {
                    margin-bottom: 8px;
                    color: #667085;
                }

                .card .callback-details strong {
                    color: #344054;
                }
        
                .card .regards-wrapper {
                    margin: 24px 8px 8px;
                    line-height: 1.5;
                    color: #667085;
                    font-size: 14px;
                }
            </style>
        </head>
        
        <body>
            <div class="card">
                <div class="logo-wrapper">
                    <img class="logo" src="${process.env.IMAGE_URL}/static/images/logo.png" alt="Company Logo">
                </div>
                <div class="notification-wrapper">
                    <p class="notification-text">Callback Request</p>
                </div>
                <div class="description-wrapper">
                    <p>
                        This is an automatically generated email, please do not reply.
                    </p>
                </div>
                <div class="message-wrapper">
                    <p>
                        Hello ${first_name},
                    </p>
                    <p>
                        You have received a callback request with the following details:
                    </p>
                </div>
                <div class="callback-details">
                    <p><strong>Best numbers to call:</strong> ${phone_number}</p>
                    <p><strong>Best days and times to call:</strong> ${best_time}</p>
                </div>
                <div class="regards-wrapper">
                    <p>
                        Regards,
                    </p>
                    <p>
                        Propero Team
                    </p>
                </div>
            </div>
        </body>
        </html>`,
  };

  try {
    await sgMail.send(msg);
    console.log("Email sent successfully");
  } catch (error) {
    console.error(error);

    if (error.response) {
      console.error(error.response.body);
    }
  }
};
