#!/usr/bin/env node

/**
 * Script to check billing records and verify duplicate prevention
 */

import dotenv from 'dotenv';
import pool from '../app/config/db.js';
import { runQuery } from '../utils/query.js';

dotenv.config();

async function checkBillingRecords() {
  let con = await pool.getConnection();
  
  try {
    console.log('🔍 CHECKING BILLING RECORDS');
    console.log('═'.repeat(80));
    
    // Get current date info
    const today = new Date();
    const currentMonth = today.getMonth();
    const currentYear = today.getFullYear();
    
    console.log(`📅 Current Date: ${today.toLocaleDateString('en-GB')}`);
    console.log(`📅 Current Month/Year: ${currentMonth + 1}/${currentYear}`);
    console.log('');
    
    // Check all billing records
    console.log('📋 ALL BILLING RECORDS:');
    console.log('─'.repeat(80));
    
    const allRecords = await runQuery(con, "SELECT * FROM billing ORDER BY year DESC, month DESC, id DESC");
    
    if (allRecords.length === 0) {
      console.log('❌ No billing records found in database');
    } else {
      console.log(`✅ Found ${allRecords.length} billing records:`);
      console.log('');
      console.log('| ID | Mandate        | Month | Year | Month Name |');
      console.log('─'.repeat(60));
      
      allRecords.forEach(record => {
        const monthName = new Date(record.year, record.month, 1).toLocaleDateString('en-GB', { month: 'long' });
        const id = record.id.toString().padStart(3);
        const mandate = record.mandate.padEnd(14);
        const month = record.month.toString().padStart(5);
        const year = record.year.toString().padStart(4);
        
        console.log(`| ${id} | ${mandate} | ${month} | ${year} | ${monthName} |`);
      });
    }
    
    console.log('');
    
    // Check records for current month
    console.log(`🔍 RECORDS FOR CURRENT MONTH (${currentMonth + 1}/${currentYear}):`);
    console.log('─'.repeat(80));
    
    const currentMonthRecords = await runQuery(
      con,
      "SELECT * FROM billing WHERE month = ? AND year = ? ORDER BY id",
      [currentMonth, currentYear]
    );
    
    if (currentMonthRecords.length === 0) {
      console.log('❌ No billing records found for current month');
      console.log('✅ Billing can proceed for this month');
    } else {
      console.log(`⚠️  Found ${currentMonthRecords.length} billing records for current month:`);
      console.log('');
      
      currentMonthRecords.forEach((record, index) => {
        console.log(`   ${index + 1}. ID: ${record.id}, Mandate: ${record.mandate}`);
      });
      
      console.log('');
      console.log('🚫 Billing should be BLOCKED for this month due to existing records');
    }
    
    console.log('');
    
    // Check records for previous months
    console.log('📊 BILLING HISTORY BY MONTH:');
    console.log('─'.repeat(80));
    
    const monthlyStats = await runQuery(
      con,
      `SELECT 
        year, 
        month, 
        COUNT(*) as record_count,
        MIN(id) as first_id,
        MAX(id) as last_id
      FROM billing 
      GROUP BY year, month 
      ORDER BY year DESC, month DESC`
    );
    
    if (monthlyStats.length === 0) {
      console.log('❌ No billing history found');
    } else {
      console.log('| Year | Month | Month Name | Records | First ID | Last ID |');
      console.log('─'.repeat(70));
      
      monthlyStats.forEach(stat => {
        const monthName = new Date(stat.year, stat.month, 1).toLocaleDateString('en-GB', { month: 'long' }).padEnd(9);
        const year = stat.year.toString().padStart(4);
        const month = (stat.month + 1).toString().padStart(5);
        const records = stat.record_count.toString().padStart(7);
        const firstId = stat.first_id.toString().padStart(8);
        const lastId = stat.last_id.toString().padStart(7);
        
        console.log(`| ${year} | ${month} | ${monthName} | ${records} | ${firstId} | ${lastId} |`);
      });
    }
    
    console.log('');
    console.log('🔧 RECOMMENDATIONS:');
    console.log('─'.repeat(80));
    
    if (currentMonthRecords.length > 0) {
      console.log('⚠️  Billing records exist for current month');
      console.log('   - The system should prevent duplicate billing');
      console.log('   - If you need to re-run billing, delete current month records first');
      console.log(`   - SQL: DELETE FROM billing WHERE month = ${currentMonth} AND year = ${currentYear};`);
    } else {
      console.log('✅ No billing records for current month');
      console.log('   - Billing can proceed normally');
      console.log('   - Records will be created after successful email send');
    }
    
    console.log('');
    console.log('═'.repeat(80));
    
  } catch (error) {
    console.error('❌ Error checking billing records:', error);
  } finally {
    con.destroy();
  }
}

// Run the check if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  checkBillingRecords().catch(console.error);
}

export { checkBillingRecords };
