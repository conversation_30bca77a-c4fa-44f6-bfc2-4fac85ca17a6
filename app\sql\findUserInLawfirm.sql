SELECT 
    u.user_id
    , u.email
    , u.status
    , u.role
    , u.created_at
    , u.created_by
    , u.update_at
    , u.update_by
    , u.pms_id
    , ui.title
    , ui.org_type
    , ui.org_name
    , ui.first_name
    , ui.middle_name
    , ui.last_name
    , ui.dob
    , ui.gender
    , ui.home_phone
    , ui.mb_phone
    , ui.wrk_phone
    , ui.adr_1
    , ui.adr_2
    , ui.adr_3
    , ui.state
    , ui.town
    , ui.country
    , ui.post_code

FROM users u
LEFT JOIN user_info ui ON u.user_id = ui.user_id
WHERE u.email = ?
AND u.lf_id = ?