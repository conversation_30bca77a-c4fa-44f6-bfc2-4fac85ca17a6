{"name": "graph-mail-test", "version": "1.0.0", "description": "Standalone test for Microsoft Graph API email sending", "type": "module", "main": "test-graph-mail.js", "scripts": {"test": "node test-graph-mail.js", "install-deps": "npm install @azure/msal-node @microsoft/microsoft-graph-client"}, "dependencies": {"@azure/msal-node": "^2.6.6", "@microsoft/microsoft-graph-client": "^3.0.7"}, "keywords": ["microsoft-graph", "email", "test", "azure"], "author": "Propero Team", "license": "MIT"}