SELECT 
    at.id,
    at.lf_id,
    at.user_id,
    at.token_name,
    at.token_prefix,
    at.permissions,
    at.is_active,
    at.expires_at,
    at.last_used_at,
    at.created_at,
    at.updated_at,
    u.email,
    CONCAT(ui.first_name, ' ', ui.last_name) as user_name
FROM api_tokens at
JOIN users u ON at.user_id = u.user_id
JOIN user_info ui ON u.user_id = ui.user_id
WHERE at.lf_id = ?
ORDER BY at.created_at DESC;
