import { S3Client } from "@aws-sdk/client-s3";
import { S3SyncClient } from 's3-sync-client';
import dotenv from "dotenv";

dotenv.config();

export const s3Client = new S3Client({
    region: process.env.AWS_REGION,
    credentials: {
        accessKeyId: process.env.ACCESS_KEY_ID,
        secretAccessKey: process.env.SECRET_ACCESS_KEY
    }
});
const { sync } = new S3SyncClient({ client: s3Client });
await sync(process.env.S3_PATH, 'static', { del: true });

