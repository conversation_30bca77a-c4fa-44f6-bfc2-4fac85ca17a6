{"name": "boiler", "version": "0.0.1", "description": "template for nodejs", "main": "index.js", "type": "module", "private": true, "scripts": {"start": "node index.js", "dev": "npx nodemon index.js", "start:local": "node -r dotenv/config . dotenv_config_path=./.env.local", "start:dev": "node -r dotenv/config . dotenv_config_path=./.env.dev", "start:uat": "node -r dotenv/config . dotenv_config_path=./.env.uat", "start:prod": "node -r dotenv/config . dotenv_config_path=./.env.production", "start:demo": "node -r dotenv/config . dotenv_config_path=./.env.demo", "test": "node --no-warnings --experimental-vm-modules $( [ -f ./node_modules/.bin/jest ] && echo ./node_modules/.bin/jest || which jest )", "generate-swagger": "node utils/generateSwagger.js", "setup-api-tokens": "node scripts/setup-api-tokens.js"}, "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.515.0", "@aws-sdk/s3-request-presigner": "^3.701.0", "@azure/msal-node": "^3.6.0", "@microsoft/microsoft-graph-client": "^3.0.7", "@sendgrid/mail": "^8.1.3", "axios": "^1.7.9", "bcrypt": "^5.1.1", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "countries-and-timezones": "^3.6.0", "country-codes-list": "^1.6.11", "date-fns": "^3.6.0", "dotenv": "^16.3.1", "exceljs": "^4.4.0", "express": "^4.18.2", "express-basic-auth": "^1.2.1", "form-data": "^4.0.0", "generate-password": "^1.7.1", "get-stream": "^9.0.1", "gocardless-nodejs": "^3.21.0", "google-auth-library": "^9.4.2", "helmet": "^7.2.0", "jsonwebtoken": "^9.0.2", "moment-timezone": "^0.5.45", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.5", "node-schedule": "^2.1.1", "nodemailer": "^6.9.8", "openai": "^4.97.0", "pdfkit": "^0.15.0", "s3-sync-client": "^4.3.1", "socket.io": "^4.5.3", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "twilio": "^5.0.0", "validator": "^13.11.0", "winston": "^3.11.0"}, "imports": {"#controllers/*.js": "./app/controllers/*.js", "#middlewares/*.js": "./app/middlewares/*.js", "#logger": "./utils/logger.js", "#app": "./app/index.js", "#utils": "./utils/index.js", "#services": "./app/services/index.js"}, "devDependencies": {"jest": "^29.7.0", "supertest": "^6.3.3"}}