import { mailProviderService } from "../services/mailProvider.service.js";
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from "#middlewares/ErrorClass.js";
import dotenv from "dotenv";

dotenv.config();

function escapeHtml(unsafe) {
    return unsafe
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;")
        .replace(/\//g, "&#x2F;")
        .replace(/`/g, "&#x60;")
        .replace(/=/g, "&#x3D;");
}

/**
 * Enhanced temp password mail function that uses the mail provider factory
 * Supports both SendGrid and Microsoft Graph API based on law firm configuration
 */
export const enhancedTempPasswordMail = async (email, password, first_name, lf_id = null, user_id = 'me') => {
    const emailData = {
        from: { name: "Propero Support", email: "<EMAIL>" },
        to: email,
        subject: `Propero new password`,
        html: `<!DOCTYPE html>
        <html lang="en">
        
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Document</title>
            <style>
                * {
                    box-sizing: border-box;
                }
        
                body {
                    margin: 0;
                    padding: 0;
                    font-family: 'Inter', sans-serif;
                    background-color: #fff;
                }
        
                p {
                    margin: 0;
                }
        
                .card {
                    padding: 24px;
                    width: 460px;
                    border: 1px solid rgba(0, 0, 0, 0.75);
                }
        
                .card .logo-wrapper {
                    text-align: center;
                    align-items: center;
                    margin-bottom: 20px;
                }
        
                .card .logo-wrapper .logo {
                    max-width: 70px;
                    height: auto;
                }
        
                .card .invitation-wrapper {
                    background-color: #F5F5F5;
                    height: 54px;
                    padding: 0 8px;
                }
                
                .card .invitation-wrapper .invitation-text {
                    line-height: 54px;
                    color: #000;
                    font-weight: 800;
                    font-size: 14px;
                }
        
                .card .description-wrapper {
                    margin: 8px;
                    color: #94A3B8;
                    font-size: 11px;
                }
        
                .card .invite-wrapper {
                    color: #667085;
                    font-size: 14px;
                    line-height: 1.5;
                    margin: 0 8px;
                    gap: 4px;
                }
        
                .card .button-wrapper {
                    margin: 32px 8px;
                }
        
                .card .button-wrapper .button {
                    background-color: rgba(213, 49, 49, 0.71);
                    color: #fff;
                    font-size: 14px;
                    height: 40px;
                    border-radius: 16px;
                    cursor: pointer;
                    width: 100%;
                    border: none;
                }
        
                .card .regards-wrapper {
                    margin: 8px;
                    line-height: 1.5;
                    color: #667085;
                    font-size: 14px;
                }
                
                .password-box {
                    background-color: #f8f9fa;
                    border: 1px solid #dee2e6;
                    border-radius: 8px;
                    padding: 12px;
                    margin: 16px 8px;
                    font-family: 'Courier New', monospace;
                    font-size: 16px;
                    font-weight: bold;
                    text-align: center;
                    color: #495057;
                }
            </style>
        </head>
        
        <body>
            <div class="card">
                <div class="logo-wrapper">
                    <img src="${process.env.IMAGE_URL}/static/images/logo.png" alt="Company Logo" class="logo">
                </div>
                <div class="invitation-wrapper">
                    <p class="invitation-text">New Password</p>
                </div>
                <div class="description-wrapper">
                    <p>
                        This is an automatically generated email, please do not reply.
                    </p>
                </div>
                <div class="invite-wrapper">
                    <p>
                        Hello ${escapeHtml(first_name)},
                    </p>
                    <p>
                        Your new temporary password has been generated. Please use the password below to log in and change it immediately for security purposes.
                    </p>
                </div>
                
                <div class="password-box">
                    ${escapeHtml(password)}
                </div>
                
                <div class="button-wrapper">
                    <a target="_blank" href="${process.env.FRONTEND_URL}/login" style="display: inline-block; padding: 10px 20px; font-size: 14px; color: #fff; background-color: rgba(213, 49, 49, 0.71); text-align: center; text-decoration: none; border-radius: 16px; width: 100%;">
                        Login to Propero
                    </a>
                </div>
                
                <div class="regards-wrapper">
                    <p>
                        Best regards,
                    </p>
                    <p>
                        Propero Team
                    </p>
                </div>
            </div>
        </body>
        
        </html>`
    };

    try {
        // Use the mail provider factory to send the email
        const result = await mailProviderService.sendMail(emailData, lf_id, user_id);
        console.log(`Temp password email sent via ${result.provider}:`, result.messageId);
        return result;
    } catch (error) {
        console.error('Enhanced temp password mail error:', error);
        throw ErrorHandler.badRequestError(`Failed to send temp password email: ${error.message}`);
    }
};
