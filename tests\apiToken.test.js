import request from 'supertest';
import app from '../app/index.js';
import { apiTokenService } from '../app/services/index.js';

describe('API Token System', () => {
  let adminJwtToken;
  let apiToken;
  let testLfId = 1;
  let testUserId = 1;

  beforeAll(async () => {
    // This would normally be set up with proper test data
    // For now, we'll assume test environment is configured
  });

  describe('Token Management', () => {
    test('should create API token with valid admin JWT', async () => {
      const response = await request(app)
        .post('/private/api-tokens')
        .set('Authorization', `Bearer ${adminJwtToken}`)
        .send({
          token_name: 'Test Token',
          user_id: testUserId,
          lf_id: testLfId
        });

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data.token).toMatch(/^propero_[a-f0-9]{8}_/);
      
      apiToken = response.body.data.token;
    });

    test('should list API tokens for law firm', async () => {
      const response = await request(app)
        .get('/private/api-tokens')
        .set('Authorization', `Bearer ${adminJwtToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
    });

    test('should reject token creation without proper role', async () => {
      const response = await request(app)
        .post('/private/api-tokens')
        .set('Authorization', `Bearer invalid_token`)
        .send({
          token_name: 'Test Token',
          user_id: testUserId
        });

      expect(response.status).toBe(403);
    });
  });

  describe('API Token Authentication', () => {
    test('should authenticate with valid API token', async () => {
      const response = await request(app)
        .get('/api/firm/token-info')
        .set('Authorization', `Bearer ${apiToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.lf_id).toBe(testLfId);
    });

    test('should reject invalid API token', async () => {
      const response = await request(app)
        .get('/api/firm/token-info')
        .set('Authorization', 'Bearer invalid_token');

      expect(response.status).toBe(403);
      expect(response.body.success).toBe(false);
    });

    test('should reject API token without proper format', async () => {
      const response = await request(app)
        .get('/api/firm/token-info')
        .set('Authorization', 'Bearer not_propero_token');

      expect(response.status).toBe(403);
      expect(response.body.message).toContain('Invalid API token format');
    });
  });

  describe('Firm API Operations', () => {
    test('should login with API token', async () => {
      const response = await request(app)
        .post('/api/firm/login')
        .set('Authorization', `Bearer ${apiToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.access_token).toBeDefined();
    });

    test('should create user via API token', async () => {
      const response = await request(app)
        .post('/api/firm/users')
        .set('Authorization', `Bearer ${apiToken}`)
        .send({
          email: '<EMAIL>',
          first_name: 'Test',
          last_name: 'User',
          role: '3' // LawfirmUser
        });

      // This might fail in test environment without proper setup
      // but should show the endpoint structure
      expect([201, 400]).toContain(response.status);
    });

    test('should get cases via API token', async () => {
      const response = await request(app)
        .get('/api/firm/cases')
        .set('Authorization', `Bearer ${apiToken}`);

      expect([200, 400]).toContain(response.status);
      // 400 might occur if database isn't properly set up for tests
    });
  });

  describe('Token Service Unit Tests', () => {
    test('should generate valid token format', () => {
      const tokenData = apiTokenService.generateToken();
      
      expect(tokenData.token).toMatch(/^propero_[a-f0-9]{8}_[a-f0-9]{64}$/);
      expect(tokenData.prefix).toMatch(/^propero_[a-f0-9]{8}$/);
      expect(tokenData.hash).toHaveLength(64);
    });

    test('should hash token correctly', () => {
      const token = 'propero_12345678_abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';
      const hash1 = apiTokenService.hashToken(token);
      const hash2 = apiTokenService.hashToken(token);
      
      expect(hash1).toBe(hash2);
      expect(hash1).toHaveLength(64);
    });
  });

  afterAll(async () => {
    // Clean up test data
    if (apiToken) {
      // Revoke test token
      await request(app)
        .put(`/private/api-tokens/1/revoke`)
        .set('Authorization', `Bearer ${adminJwtToken}`)
        .send({ lf_id: testLfId });
    }
  });
});

// Helper function to create test JWT token
function createTestJwtToken(userId, role, lfId) {
  // This would use the actual generateToken function
  // For testing purposes, you might need to mock this
  return 'test_jwt_token';
}

export default {
  createTestJwtToken
};
