#!/usr/bin/env node

/**
 * Test script for consolidated billing email functionality
 * This script demonstrates the new consolidated billing system
 */

import dotenv from 'dotenv';

dotenv.config();

// Configuration constants (same as in billing service)
const MONTHLY_FEE = 25000; // £250 in pence
const VAT_RATE = 0.20; // 20% VAT rate

// Helper functions (same as in billing service)
const calculateVAT = (amount) => Math.round(amount * VAT_RATE);

const calculateTotalWithVAT = (baseAmount) => baseAmount + calculateVAT(baseAmount);

const calculateProRatedAmount = (lawFirmCreatedDate, billingMonth) => {
  const createdDate = new Date(lawFirmCreatedDate);
  const billingDate = new Date(billingMonth);
  
  const isFirstMonth = (
    createdDate.getFullYear() === billingDate.getFullYear() &&
    createdDate.getMonth() === billingDate.getMonth()
  );
  
  if (!isFirstMonth) {
    return MONTHLY_FEE;
  }
  
  const daysInMonth = new Date(billingDate.getFullYear(), billingDate.getMonth() + 1, 0).getDate();
  const daysUsed = daysInMonth - createdDate.getDate() + 1;
  return Math.round((MONTHLY_FEE * daysUsed) / daysInMonth);
};

// Mock law firms data
const mockLawFirms = [
  {
    lf_id: 1,
    lf_org_name: "Alpha Law Firm Ltd",
    mandate: "MD123456",
    owner_created_at: "2024-01-01" // Full month
  },
  {
    lf_id: 2,
    lf_org_name: "Beta Legal Services",
    mandate: "MD789012",
    owner_created_at: "2024-01-15" // Pro-rated (17 days)
  },
  {
    lf_id: 3,
    lf_org_name: "Gamma Solicitors",
    mandate: null, // No mandate
    owner_created_at: "2024-01-31" // Pro-rated (1 day)
  },
  {
    lf_id: 4,
    lf_org_name: "Delta Legal Partners",
    mandate: "MD345678",
    owner_created_at: "2023-12-01" // Previous month (full)
  },
  {
    lf_id: 5,
    lf_org_name: "Epsilon Law Group",
    mandate: "MD901234",
    owner_created_at: "2024-01-10" // Pro-rated (22 days)
  }
];

// Function to generate consolidated billing data
function generateConsolidatedBillingData(lawFirms, billingMonth) {
  const billingData = [];
  
  lawFirms.forEach(firm => {
    const baseAmount = calculateProRatedAmount(firm.owner_created_at, billingMonth);
    const vatAmount = calculateVAT(baseAmount);
    const totalAmount = calculateTotalWithVAT(baseAmount);
    
    billingData.push({
      lf_id: firm.lf_id,
      lawFirmName: firm.lf_org_name,
      mandate: firm.mandate,
      baseAmount: baseAmount,
      vatAmount: vatAmount,
      totalAmount: totalAmount,
      isProRated: baseAmount < MONTHLY_FEE
    });
  });
  
  return billingData;
}

// Function to display consolidated billing summary
function displayBillingSummary(billingData, billingMonth) {
  const totalAmount = billingData.reduce((sum, item) => sum + item.totalAmount, 0);
  const totalFirms = billingData.length;
  const proRatedCount = billingData.filter(item => item.isProRated).length;
  
  console.log('\n📧 CONSOLIDATED BILLING SUMMARY');
  console.log('═'.repeat(80));
  console.log(`📅 Billing Period: ${new Date(billingMonth).toLocaleDateString('en-GB', { month: 'long', year: 'numeric' })}`);
  console.log(`🏢 Total Law Firms: ${totalFirms}`);
  console.log(`📊 Pro-rated Firms: ${proRatedCount}`);
  console.log(`💰 Total Amount: £${(totalAmount/100).toFixed(2)}`);
  console.log(`📧 Recipient: <EMAIL>`);
  console.log('═'.repeat(80));
  
  console.log('\n📋 DETAILED BREAKDOWN:');
  console.log('─'.repeat(120));
  console.log('| Law Firm                    | Mandate  | Base     | VAT      | Total    | Pro-rated |');
  console.log('─'.repeat(120));
  
  billingData.forEach(firm => {
    const name = firm.lawFirmName.padEnd(27);
    const mandate = (firm.mandate || 'N/A').padEnd(8);
    const base = `£${(firm.baseAmount/100).toFixed(2)}`.padStart(8);
    const vat = `£${(firm.vatAmount/100).toFixed(2)}`.padStart(8);
    const total = `£${(firm.totalAmount/100).toFixed(2)}`.padStart(8);
    const proRated = (firm.isProRated ? 'Yes' : 'No').padEnd(9);
    
    console.log(`| ${name} | ${mandate} | ${base} | ${vat} | ${total} | ${proRated} |`);
  });
  
  console.log('─'.repeat(120));
  console.log(`| TOTAL                                                    | £${(totalAmount/100).toFixed(2).padStart(8)} |           |`);
  console.log('─'.repeat(120));
}

// Function to simulate duplicate prevention check
function simulateDuplicateCheck(billingMonth) {
  const currentDate = new Date();
  const currentMonth = currentDate.getMonth();
  const currentYear = currentDate.getFullYear();
  
  console.log('\n🔍 DUPLICATE PREVENTION CHECK');
  console.log('─'.repeat(50));
  console.log(`Current Month: ${currentMonth + 1}/${currentYear}`);
  console.log(`Billing Month: ${new Date(billingMonth).getMonth() + 1}/${new Date(billingMonth).getFullYear()}`);
  
  // Simulate checking billing table
  const existingRecords = []; // Empty array simulates no existing records
  
  if (existingRecords.length > 0) {
    console.log('❌ Billing already sent for this month - SKIPPING');
    return false;
  } else {
    console.log('✅ No existing billing records found - PROCEEDING');
    return true;
  }
}

// Function to simulate marking billing as sent
function simulateMarkingAsSent(billingData, billingMonth) {
  const currentDate = new Date();
  const currentMonth = currentDate.getMonth();
  const currentYear = currentDate.getFullYear();
  
  console.log('\n💾 MARKING BILLING AS SENT');
  console.log('─'.repeat(50));
  
  billingData.forEach(firm => {
    const mandate = firm.mandate || `LF_${firm.lf_id}`;
    console.log(`✓ Inserted: mandate=${mandate}, month=${currentMonth}, year=${currentYear}, lf_id=${firm.lf_id}`);
  });
  
  console.log(`✅ ${billingData.length} billing records inserted successfully`);
}

// Main test function
async function runConsolidatedBillingTest() {
  console.log('🚀 CONSOLIDATED BILLING SYSTEM TEST');
  console.log('═'.repeat(80));
  
  const billingMonth = "2024-01-01"; // January 2024
  
  try {
    // Step 1: Check for duplicates
    const canProceed = simulateDuplicateCheck(billingMonth);
    
    if (!canProceed) {
      console.log('\n🛑 Test stopped due to duplicate prevention');
      return;
    }
    
    // Step 2: Generate billing data
    console.log('\n⚙️  GENERATING BILLING DATA');
    console.log('─'.repeat(50));
    const billingData = generateConsolidatedBillingData(mockLawFirms, billingMonth);
    console.log(`✅ Generated billing data for ${billingData.length} law firms`);
    
    // Step 3: Display summary
    displayBillingSummary(billingData, billingMonth);
    
    // Step 4: Simulate email sending
    console.log('\n📧 SIMULATING EMAIL SEND');
    console.log('─'.repeat(50));
    console.log('✅ Consolidated billing email would be sent to: <EMAIL>');
    console.log('📄 Email would contain HTML table with all law firm details');
    console.log('📊 Email would include summary statistics and totals');
    
    // Step 5: Mark as sent
    simulateMarkingAsSent(billingData, billingMonth);
    
    console.log('\n🎉 CONSOLIDATED BILLING TEST COMPLETED SUCCESSFULLY!');
    console.log('═'.repeat(80));
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
  }
}

// Run the test if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runConsolidatedBillingTest().catch(console.error);
}

export { 
  runConsolidatedBillingTest, 
  generateConsolidatedBillingData, 
  displayBillingSummary,
  mockLawFirms 
};
