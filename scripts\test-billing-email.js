#!/usr/bin/env node

/**
 * Test script for billing email functionality
 * This script demonstrates the new billing system that sends emails instead of creating payments
 */

import dotenv from 'dotenv';
import { mailProviderService } from '../app/services/mailProvider.service.js';

dotenv.config();

// Configuration constants (same as in billing service)
const MONTHLY_FEE = 25000; // £250 in pence
const VAT_RATE = 0.20; // 20% VAT rate

// Helper functions (same as in billing service)
const calculateVAT = (amount) => Math.round(amount * VAT_RATE);

const calculateTotalWithVAT = (baseAmount) => baseAmount + calculateVAT(baseAmount);

const calculateProRatedAmount = (lawFirmCreatedDate, billingMonth) => {
  const createdDate = new Date(lawFirmCreatedDate);
  const billingDate = new Date(billingMonth);
  
  const isFirstMonth = (
    createdDate.getFullYear() === billingDate.getFullYear() &&
    createdDate.getMonth() === billingDate.getMonth()
  );
  
  if (!isFirstMonth) {
    return MONTHLY_FEE;
  }
  
  const daysInMonth = new Date(billingDate.getFullYear(), billingDate.getMonth() + 1, 0).getDate();
  const daysUsed = daysInMonth - createdDate.getDate() + 1;
  return Math.round((MONTHLY_FEE * daysUsed) / daysInMonth);
};

// Function to send billing email (same as in billing service)
const sendBillingEmail = async (amount, description, mandate, lawFirmName) => {
  try {
    const emailData = {
      to: "<EMAIL>",
      from: { name: "Propero Billing", email: "<EMAIL>" },
      subject: "Monthly Billing Summary - Test",
      html: `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Monthly Billing Summary</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background-color: #d53131; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background-color: #f9f9f9; }
                .billing-details { background-color: white; padding: 15px; border-radius: 5px; margin: 15px 0; }
                .amount { font-size: 24px; font-weight: bold; color: #d53131; }
                .footer { text-align: center; padding: 20px; color: #666; }
                .test-notice { background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin: 10px 0; border-radius: 5px; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="test-notice">
                    <strong>⚠️ TEST EMAIL</strong> - This is a test of the billing email system
                </div>
                <div class="header">
                    <h1>Monthly Billing Summary</h1>
                </div>
                <div class="content">
                    <h2>Billing Details</h2>
                    <div class="billing-details">
                        <p><strong>Law Firm:</strong> ${lawFirmName}</p>
                        <p><strong>Mandate:</strong> ${mandate || 'Not Available'}</p>
                        <p><strong>Amount:</strong> <span class="amount">£${(amount/100).toFixed(2)}</span></p>
                        <p><strong>Description:</strong> ${description}</p>
                        <p><strong>Date:</strong> ${new Date().toLocaleDateString('en-GB')}</p>
                    </div>
                </div>
                <div class="footer">
                    <p>This is a test email from the Propero billing system.</p>
                </div>
            </div>
        </body>
        </html>
      `
    };

    const result = await mailProviderService.sendMail(emailData);
    console.log(`✅ Test billing email sent successfully: ${result.messageId}`);
    return result;
  } catch (error) {
    console.error('❌ Failed to send test billing email:', error);
    throw error;
  }
};

// Test scenarios
const testScenarios = [
  {
    name: "Full Month Billing",
    lawFirmName: "Alpha Law Firm Ltd",
    mandate: "MD123456",
    createdDate: "2024-01-01", // Created on 1st
    billingMonth: "2024-01-01" // Billing for January
  },
  {
    name: "Pro-rated Billing",
    lawFirmName: "Beta Legal Services",
    mandate: "MD789012",
    createdDate: "2024-01-15", // Created on 15th
    billingMonth: "2024-01-01" // Billing for January
  },
  {
    name: "No Mandate",
    lawFirmName: "Gamma Solicitors",
    mandate: null,
    createdDate: "2023-12-01", // Created in previous month
    billingMonth: "2024-01-01" // Billing for January
  }
];

// Main test function
async function runBillingEmailTest() {
  console.log('🚀 Starting billing email test...\n');

  for (const scenario of testScenarios) {
    console.log(`📧 Testing: ${scenario.name}`);
    
    try {
      // Calculate billing amounts
      const baseAmount = calculateProRatedAmount(scenario.createdDate, scenario.billingMonth);
      const vatAmount = calculateVAT(baseAmount);
      const totalAmount = calculateTotalWithVAT(baseAmount);
      
      // Create description
      const isProRated = baseAmount < MONTHLY_FEE;
      let description = `Monthly subscription for 1/2024`;
      
      if (isProRated) {
        const createdDate = new Date(scenario.createdDate);
        const billingDate = new Date(scenario.billingMonth);
        const daysInMonth = new Date(billingDate.getFullYear(), billingDate.getMonth() + 1, 0).getDate();
        const daysUsed = daysInMonth - createdDate.getDate() + 1;
        description += ` (pro-rated: ${daysUsed}/${daysInMonth} days)`;
      }
      
      description += ` - Base: £${(baseAmount/100).toFixed(2)}, VAT: £${(vatAmount/100).toFixed(2)}, Total: £${(totalAmount/100).toFixed(2)}`;
      
      // Log details
      console.log(`   Law Firm: ${scenario.lawFirmName}`);
      console.log(`   Mandate: ${scenario.mandate || 'Not Available'}`);
      console.log(`   Amount: £${(totalAmount/100).toFixed(2)}`);
      console.log(`   Pro-rated: ${isProRated ? 'Yes' : 'No'}`);
      
      // Send test email (uncomment to actually send)
      // await sendBillingEmail(totalAmount, description, scenario.mandate, scenario.lawFirmName);
      
      console.log(`   ✅ Test completed successfully\n`);
      
    } catch (error) {
      console.error(`   ❌ Test failed: ${error.message}\n`);
    }
  }
  
  console.log('🎉 All billing email tests completed!');
  console.log('\n📝 To actually send test emails, uncomment the sendBillingEmail call in the script.');
}

// Run the test if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runBillingEmailTest().catch(console.error);
}

export { runBillingEmailTest, sendBillingEmail, testScenarios };
