/**
 * Test script to verify SQL optimizations work correctly
 * This script tests the optimized service methods to ensure they return the same data structure
 * and that performance is improved while maintaining functionality.
 */

import { getTemplate } from './app/services/template.service.js';
import { getCase } from './app/services/case.service.js';
import { getQuestionaire } from './app/services/questionaire.service.js';

// Test configuration
const TEST_CONFIG = {
  TEMPLATE_ID: 1, // Replace with actual template ID
  CASE_ID: 1,     // Replace with actual case ID  
  QUESTIONNAIRE_ID: 1 // Replace with actual questionnaire ID
};

/**
 * Test template service optimization
 */
async function testTemplateOptimization() {
  console.log('🧪 Testing Template Service Optimization...');
  
  try {
    const startTime = Date.now();
    const result = await getTemplate(TEST_CONFIG.TEMPLATE_ID);
    const endTime = Date.now();
    
    console.log(`✅ Template retrieved successfully in ${endTime - startTime}ms`);
    console.log(`📊 Data structure: ${result.groups?.length || 0} groups, ${result.conditions?.length || 0} conditions`);
    
    // Verify data structure
    if (result.id && result.name && Array.isArray(result.groups) && Array.isArray(result.conditions)) {
      console.log('✅ Template data structure is correct');
    } else {
      console.log('❌ Template data structure is incorrect');
    }
    
    return { success: true, time: endTime - startTime, result };
  } catch (error) {
    console.log(`❌ Template test failed: ${error.message}`);
    return { success: false, error: error.message };
  }
}

/**
 * Test case service optimization
 */
async function testCaseOptimization() {
  console.log('🧪 Testing Case Service Optimization...');
  
  try {
    const startTime = Date.now();
    const result = await getCase(TEST_CONFIG.CASE_ID);
    const endTime = Date.now();
    
    console.log(`✅ Case retrieved successfully in ${endTime - startTime}ms`);
    console.log(`📊 Data structure: ${result.groups?.length || 0} groups, ${result.answer?.length || 0} answers`);
    
    // Verify data structure
    if (result.id && result.name && Array.isArray(result.groups) && Array.isArray(result.conditions)) {
      console.log('✅ Case data structure is correct');
    } else {
      console.log('❌ Case data structure is incorrect');
    }
    
    return { success: true, time: endTime - startTime, result };
  } catch (error) {
    console.log(`❌ Case test failed: ${error.message}`);
    return { success: false, error: error.message };
  }
}

/**
 * Test questionnaire service optimization
 */
async function testQuestionnaireOptimization() {
  console.log('🧪 Testing Questionnaire Service Optimization...');
  
  try {
    const startTime = Date.now();
    const result = await getQuestionaire(TEST_CONFIG.QUESTIONNAIRE_ID);
    const endTime = Date.now();
    
    console.log(`✅ Questionnaire retrieved successfully in ${endTime - startTime}ms`);
    console.log(`📊 Data structure: ${result.groups?.length || 0} groups, ${result.conditions?.length || 0} conditions`);
    
    // Verify data structure
    if (result.id && result.name && Array.isArray(result.groups) && Array.isArray(result.conditions)) {
      console.log('✅ Questionnaire data structure is correct');
    } else {
      console.log('❌ Questionnaire data structure is incorrect');
    }
    
    return { success: true, time: endTime - startTime, result };
  } catch (error) {
    console.log(`❌ Questionnaire test failed: ${error.message}`);
    return { success: false, error: error.message };
  }
}

/**
 * Run all optimization tests
 */
async function runAllTests() {
  console.log('🚀 Starting SQL Optimization Tests...\n');
  
  const results = {
    template: await testTemplateOptimization(),
    case: await testCaseOptimization(), 
    questionnaire: await testQuestionnaireOptimization()
  };
  
  console.log('\n📋 Test Summary:');
  console.log('================');
  
  const successCount = Object.values(results).filter(r => r.success).length;
  const totalTests = Object.keys(results).length;
  
  console.log(`✅ Passed: ${successCount}/${totalTests}`);
  console.log(`❌ Failed: ${totalTests - successCount}/${totalTests}`);
  
  if (successCount === totalTests) {
    console.log('\n🎉 All optimizations are working correctly!');
    
    // Performance summary
    const avgTime = Object.values(results)
      .filter(r => r.success)
      .reduce((sum, r) => sum + r.time, 0) / successCount;
    
    console.log(`⚡ Average response time: ${avgTime.toFixed(2)}ms`);
  } else {
    console.log('\n⚠️  Some optimizations need attention.');
  }
  
  return results;
}

// Export for use in other test files
export { testTemplateOptimization, testCaseOptimization, testQuestionnaireOptimization, runAllTests };

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests().catch(console.error);
}
