import { mailService, userService } from "#services";
import { catchAsync } from "#utils";

export const sendOTP = catchAsync(async (req, res) => {
  const { email } = req.body;
  let user = await userService.findUser(email);
  if (user.length == 0) {
    return res.status(500).json({
      success: false,
      message: "User not found",
    });
  }
  let check = await mailService.sendOTP(email);
  return res.status(200).json({
    success: true,
    message: "OTP sent",
  });
});

export const sendTempPassword = catchAsync(async (req, res) => {
  const { email, password, first_name } = req.body;
  if (req.user.email != email) {
    return res.status(500).json({
      success: false,
      message: "Unauthorized",
    });
  }

  // Use smart wrapper that automatically tries Graph API first, then falls back to SendGrid
  const result = await mailService.sendTempPasswordSmart(email, password, first_name, req.user);

  if (result.success) {
    return res.status(200).json({
      success: true,
      message: "Password sent",
      provider: result.result?.provider || (result.legacy ? 'sendgrid' : 'unknown')
    });
  } else {
    return res.status(400).json({
      success: false,
      message: "Error sending temp password",
      error: result.error
    });
  }
});

export const verifyOTP = catchAsync(async (req, res) => {
  const { email, otp } = req.body;
  let result = await mailService.verifyOTP(email, otp);
  if (result != null && result[0] && result[0] == otp) {
    await mailService.deleteOTP(email);
    return res.status(200).json({
      success: true,
      message: "OTP verified",
      token: result[1],
    });
  } else {
    return res.status(400).json({
      success: false,
      message: "OTP not verified",
    });
  }
});

export const getMailType = catchAsync(async (req, res) => {
  let mailType = await mailService.getMailType();
  return res.status(200).json({
    success: true,
    message: "Mail type fetched",
    mailType,
  });
});