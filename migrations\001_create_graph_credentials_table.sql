-- Migration: Create graph_credentials table for Microsoft Graph API integration
-- Date: 2025-01-18
-- Description: Adds support for storing Microsoft Graph API credentials per law firm

-- Create the graph_credentials table
CREATE TABLE IF NOT EXISTS graph_credentials (
    id INT AUTO_INCREMENT PRIMARY KEY,
    lf_id INT NOT NULL,
    tenant_id VARCHAR(255) NOT NULL COMMENT 'Microsoft Entra ID tenant ID',
    client_id VARCHAR(255) NOT NULL COMMENT 'Application (client) ID',
    client_secret TEXT NOT NULL COMMENT 'Encrypted client secret',
    sender_email VARCHAR(255) NULL COMMENT 'Optional: specific email address to send from (must exist in tenant)',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'Whether credentials are active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT COMMENT 'User ID who created the credentials',
    updated_by INT COMMENT 'User ID who last updated the credentials',
    
    -- Foreign key constraint
    FOREIGN KEY (lf_id) REFERENCES law_firm(lf_id) ON DELETE CASCADE,
    
    -- Ensure only one set of credentials per law firm
    UNIQUE KEY unique_lf_graph (lf_id),
    
    -- Indexes for performance
    INDEX idx_lf_id (lf_id),
    INDEX idx_active (is_active),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Microsoft Graph API credentials for law firms';

-- Add some sample data for testing (optional - remove in production)
-- INSERT INTO graph_credentials (lf_id, tenant_id, client_id, client_secret, created_by) VALUES
-- (1, 'sample-tenant-id', 'sample-client-id', 'encrypted-sample-secret', 1);

-- Create a view for safe credential access (without secrets)
CREATE OR REPLACE VIEW graph_credentials_safe AS
SELECT
    id,
    lf_id,
    tenant_id,
    client_id,
    sender_email,
    is_active,
    created_at,
    updated_at,
    created_by,
    updated_by
FROM graph_credentials;

-- Grant appropriate permissions (adjust as needed for your setup)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON graph_credentials TO 'propero_app'@'%';
-- GRANT SELECT ON graph_credentials_safe TO 'propero_readonly'@'%';
