CREATE TABLE IF NOT EXISTS graph_credentials (
    id INT AUTO_INCREMENT PRIMARY KEY,
    lf_id INT NOT NULL,
    tenant_id VARCHAR(255) NOT NULL,
    client_id VARCHAR(255) NOT NULL,
    client_secret TEXT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    FOREIGN KEY (lf_id) REFERENCES law_firm(lf_id) ON DELETE CASCADE,
    UNIQUE KEY unique_lf_graph (lf_id),
    INDEX idx_lf_id (lf_id),
    INDEX idx_active (is_active)
);
