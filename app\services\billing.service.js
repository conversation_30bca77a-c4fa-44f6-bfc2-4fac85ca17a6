import * as fs from "fs";
import ErrorHandler from "#middlewares/ErrorClass.js";
import { runQuery } from "#utils";
import pool from "../config/db.js";
import client from "../config/gocardless.js";
import { status } from "#middlewares/billing_status.js";
import schedule from "node-schedule";
import { setTimeout } from "timers/promises";

import {
  getTimeInTimezone,
  convertCountryNameToISOCode,
  getCountsForUsers,
} from "./questionaire.service.js";
import { noti_type } from "#middlewares/noti_type.js";
import { connectedUsers, io, notificationsNamespace } from "../../index.js";
import { mailProviderService } from "./mailProvider.service.js";

// Configuration constants
const MONTHLY_FEE = 25000; // £250 in pence - monthly subscription fee
const VAT_RATE = 0.20; // 20% VAT rate

export const getMandate = async (lf_id) => {
  let con = await pool.getConnection();
  try {
    let mandates = await runQuery(
      con,
      `SELECT mandate FROM law_firm where lf_id = ?`,
      [lf_id]
    );
    if (mandates.length == 0) {
      return null;
    }
    return mandates[0]["mandate"];
  } catch (error) {
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};
export const getAllPayment = async (mandate) => {
  let con = await pool.getConnection();
  let payments = [];
  try {
    if (mandate == null || mandate == "") {
      let mandates = await runQuery(
        con,
        `SELECT Distinct mandate , lf_org_name FROM law_firm where mandate is not null`
      );
      if (mandates.length == 0) {
        return [];
      }
      for (let i = 0; i < mandates.length; i++) {
        let list = await client.payments.list({ mandate: mandates[i].mandate });
        for (let j = 0; j < list.payments.length; j++) {
          list.payments[j].lf_org_name = mandates[i].lf_org_name;
          list.payments[j].status = status[list.payments[j].status];
        }
        payments = payments.concat(list.payments);
      }
      return payments;
    } else {
      let payments = await client.payments.list({ mandate: mandate });
      let mandates = await runQuery(
        con,
        `SELECT lf_org_name FROM law_firm where mandate = ?`,
        [mandate]
      );
      for (let j = 0; j < payments.payments.length; j++) {
        payments.payments[j].lf_org_name = mandates[0].lf_org_name;
        payments.payments[j].status = status[payments.payments[j].status];
      }
      return payments.payments;
    }
  } catch (error) {
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};
export const getHistory = async (
  page,
  size = 10,
  keyword = "",
  lf_id = null,
  startDate,
  endDate
) => {
  let con = await pool.getConnection();
  try {
    console.log(startDate, endDate);
    await con.beginTransaction();
    let current_date = new Date();
    if (!startDate) {
      startDate = new Date();
      startDate.setMonth(startDate.getMonth() - 1);
    }
    if (!endDate) endDate = current_date;
    let history, historyCount;
    if (lf_id == null) {
      var sql = fs.readFileSync("app/sql/getAllHistory.sql").toString();
      history = await runQuery(con, sql, [
        startDate,
        endDate,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        (page - 1) * size,
        size * 1,
      ]);
      var sqlcount = fs
        .readFileSync("app/sql/getAllHistoryCount.sql")
        .toString();
      historyCount = await runQuery(con, sqlcount, [
        startDate,
        endDate,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
      ]);
    } else {
      var sql = fs.readFileSync("app/sql/getAllHistoryKeyword.sql").toString();
      history = await runQuery(con, sql, [
        lf_id,
        startDate,
        endDate,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        (page - 1) * size,
        size * 1,
      ]);
      var sqlcount = fs
        .readFileSync("app/sql/getAllHistoryCountKeyword.sql")
        .toString();
      historyCount = await runQuery(con, sqlcount, [
        lf_id,
        startDate,
        endDate,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
      ]);
    }
    let count = historyCount[0]["COUNT(*)"];
    await con.commit();
    return { history, count };
  } catch (error) {
    await con.rollback();
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};
const randomTimeInMs = Math.random() * 3000;

export const createPayment = async (amount, description, mandate) => {
  let con = await pool.getConnection();
  try {
    const result = await setTimeout(randomTimeInMs, "resolved");
    let now = new Date();
    let check = await runQuery(
      con,
      "SELECT * FROM billing where mandate = ? and month = ? and year = ?",
      [mandate, now.getMonth(), now.getFullYear()]
    );
    if (check.length > 0) {
      return;
    }
    let payment = await client.payments.create({
      amount: amount,
      currency: "GBP",
      links: {
        mandate: mandate,
      },
      description: description,
    });
    await runQuery(
      con,
      "INSERT into billing (mandate, month, year) VALUES(?, ?, ?)",
      [mandate, now.getMonth(), now.getFullYear()]
    );
    let lf = await runQuery(
      con,
      "SELECT lf_org_name FROM law_firm WHERE mandate = ?",
      [mandate]
    );
    let admin = await runQuery(
      con,
      "SELECT u.user_id, ui.country FROM users u JOIN user_info ui ON u.user_id = ui.user_id WHERE role IN (?)",
      [1]
    );

    let month = new Date().getMonth();
    let ad_noti = [];
    let lf_ad_noti = [];
    let ad_msg = `Law firm ${lf[0].lf_org_name}'s payment created.`;

    let ctemp_user = 0;
    let condition_query = (list_user, list, message, noti_type) => {
      for (var { user_id: ad_user_id, country } of list_user) {
        let time = getTimeInTimezone(convertCountryNameToISOCode(country));
        list.push([message, time, 1, ad_user_id, null, noti_type, ctemp_user]);
        ctemp_user++;
      }
    };
    condition_query(admin, ad_noti, ad_msg, noti_type.Admin_payment_success);
    var result_ad = await runQuery(
      con,
      "INSERT INTO notification (message, created_at, status, user_id, source, type) VALUES ?",
      [trimListNoti(ad_noti)]
    );
    await con.commit();
    const ad_id = admin.map((user) => user.user_id);
    const count_ad = await getCountsForUsers(ad_id);

    let pushNoti = (
      list_user,
      count_list,
      list_noti,
      message,
      noti_type,
      result
    ) => {
      for (const { user_id: id } of list_user) {
        if (connectedUsers[id]) {
          const count = count_list[id];
          let time = list_noti.find((n) => n[3] === id)[1];
          let noti_id = list_noti.find((n) => n[3] === id)[6];
          notificationsNamespace
            .to(connectedUsers[id].id)
            .emit("notification", {
              message,
              //   qtn_id: obj.qtn_id,
              type: noti_type,
              time,
              count,
              status: 1,
              noti_id: result.insertId + noti_id,
            });
        }
      }
    };
    pushNoti(
      admin,
      count_ad,
      ad_noti,
      ad_msg,
      noti_type.Admin_payment_success,
      result_ad
    );

    // return payment;
  } catch (error) {
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

const trimListNoti = (noti) => {
  return noti.map((item) => item.slice(0, -1));
};

const getMonthName = (month) => {
  const months = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];
  if (month < 1 || month > 12) {
    return "Invalid month";
  }
  return months[month - 1];
};

// Helper function to calculate pro-rated amount for first month
const calculateProRatedAmount = (lawFirmCreatedDate, billingMonth) => {
  const createdDate = new Date(lawFirmCreatedDate);
  const billingDate = new Date(billingMonth);

  // Check if this is the first month of billing
  const isFirstMonth = (
    createdDate.getFullYear() === billingDate.getFullYear() &&
    createdDate.getMonth() === billingDate.getMonth()
  );

  if (!isFirstMonth) {
    return MONTHLY_FEE; // Full monthly fee for subsequent months
  }

  // Calculate pro-rated amount for first month
  const daysInMonth = new Date(billingDate.getFullYear(), billingDate.getMonth() + 1, 0).getDate();
  const daysUsed = daysInMonth - createdDate.getDate() + 1; // Include the creation day
  const proRatedAmount = Math.round((MONTHLY_FEE * daysUsed) / daysInMonth);

  return proRatedAmount;
};

// Helper function to calculate VAT
const calculateVAT = (amount) => {
  return Math.round(amount * VAT_RATE);
};

// Helper function to calculate total amount including VAT
const calculateTotalWithVAT = (baseAmount) => {
  const vatAmount = calculateVAT(baseAmount);
  return baseAmount + vatAmount;
};

// Function to send consolidated billing email for all law firms
const sendConsolidatedBillingEmail = async (billingData, billingMonth) => {
  try {
    // Calculate totals
    const totalAmount = billingData.reduce((sum, item) => sum + item.totalAmount, 0);
    const totalFirms = billingData.length;

    // Generate table rows for each law firm
    const firmRows = billingData.map(firm => `
      <tr>
        <td style="padding: 10px; border-bottom: 1px solid #ddd;">${firm.lawFirmName}</td>
        <td style="padding: 10px; border-bottom: 1px solid #ddd; text-align: center;">${firm.mandate || 'N/A'}</td>
        <td style="padding: 10px; border-bottom: 1px solid #ddd; text-align: right;">£${(firm.baseAmount/100).toFixed(2)}</td>
        <td style="padding: 10px; border-bottom: 1px solid #ddd; text-align: right;">£${(firm.vatAmount/100).toFixed(2)}</td>
        <td style="padding: 10px; border-bottom: 1px solid #ddd; text-align: right; font-weight: bold;">£${(firm.totalAmount/100).toFixed(2)}</td>
        <td style="padding: 10px; border-bottom: 1px solid #ddd; text-align: center;">${firm.isProRated ? 'Yes' : 'No'}</td>
      </tr>
    `).join('');

    const emailData = {
      to: "<EMAIL>",
    const emailData = {
      to: "<EMAIL>",
      from: { name: "Propero Billing", email: "<EMAIL>" },
      subject: `Monthly Billing Summary - ${new Date(billingMonth).toLocaleDateString('en-GB', { month: 'long', year: 'numeric' })}`,
      html: `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Monthly Billing Summary</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
                .container { max-width: 1000px; margin: 0 auto; padding: 20px; }
                .header { background-color: #d53131; color: white; padding: 20px; text-align: center; border-radius: 5px 5px 0 0; }
                .content { padding: 20px; background-color: #f9f9f9; }
                .summary { background-color: white; padding: 20px; border-radius: 5px; margin: 15px 0; }
                .billing-table { width: 100%; border-collapse: collapse; background-color: white; border-radius: 5px; overflow: hidden; }
                .billing-table th { background-color: #d53131; color: white; padding: 12px; text-align: left; }
                .billing-table td { padding: 10px; border-bottom: 1px solid #ddd; }
                .total-row { background-color: #f8f9fa; font-weight: bold; }
                .footer { text-align: center; padding: 20px; color: #666; }
                .amount { font-size: 18px; font-weight: bold; color: #d53131; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>Monthly Billing Summary</h1>
                    <p>${new Date(billingMonth).toLocaleDateString('en-GB', { month: 'long', year: 'numeric' })}</p>
                </div>
                <div class="content">
                    <div class="summary">
                        <h2>Summary</h2>
                        <p><strong>Total Law Firms:</strong> ${totalFirms}</p>
                        <p><strong>Total Amount:</strong> <span class="amount">£${(totalAmount/100).toFixed(2)}</span></p>
                        <p><strong>Generated:</strong> ${new Date().toLocaleDateString('en-GB')} at ${new Date().toLocaleTimeString('en-GB')}</p>
                    </div>

                    <h2>Billing Details</h2>
                    <table class="billing-table">
                        <thead>
                            <tr>
                                <th>Law Firm</th>
                                <th>Mandate</th>
                                <th>Base Amount</th>
                                <th>VAT (20%)</th>
                                <th>Total</th>
                                <th>Pro-rated</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${firmRows}
                            <tr class="total-row">
                                <td colspan="4" style="text-align: right; padding: 15px;"><strong>TOTAL:</strong></td>
                                <td style="text-align: right; padding: 15px;"><strong>£${(totalAmount/100).toFixed(2)}</strong></td>
                                <td></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="footer">
                    <p>This is an automated billing summary from Propero.</p>
                </div>
            </div>
        </body>
        </html>
      `
    };

    // Send email using the mail provider service
    const result = await mailProviderService.sendMail(emailData);
    console.log(`Consolidated billing email sent successfully: ${result.messageId}`);
    return result;
  } catch (error) {
    console.error('Failed to send consolidated billing email:', error);
    throw error;
  }
};

// const billingJob = schedule.scheduleJob("0 0 0 1-5 * *", async function () {
const billingJob = schedule.scheduleJob("  0 * * * * *", async function () {
  let con = await pool.getConnection();
  try {
    let today = new Date();
    let currentMonth = today.getMonth();
    let currentYear = today.getFullYear();
    let billingMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1); // Previous month for billing period

    // Check if billing has already been sent this month
    let billingCheck = await runQuery(
      con,
      "SELECT * FROM billing WHERE month = ? AND year = ? LIMIT 1",
      [currentMonth, currentYear]
    );

    if (billingCheck.length > 0) {
      console.log(`⚠️  Billing already sent for ${currentMonth + 1}/${currentYear} - Found ${billingCheck.length} existing records`);
      console.log(`   First record: mandate=${billingCheck[0].mandate}, created at: ${billingCheck[0].id}`);
      return;
    }

    console.log(`✅ No existing billing records found for ${currentMonth + 1}/${currentYear} - Proceeding with billing`);

    // Get all law firms with their owner creation dates
    let lawFirms = await runQuery(
      con,
      `SELECT DISTINCT lf.mandate, lf.lf_id, lf.lf_org_name, u.created_at as owner_created_at
       FROM law_firm lf
       JOIN users u ON lf.lf_id = u.lf_id
       WHERE u.is_owner = 1`
    );

    if (lawFirms.length == 0) {
      console.log('No law firms found for billing');
      return;
    }

    // Prepare billing data for all law firms
    const billingData = [];

    for (let i = 0; i < lawFirms.length; i++) {
      // Calculate the base monthly fee (pro-rated for first month)
      const baseAmount = calculateProRatedAmount(lawFirms[i].owner_created_at, billingMonth);

      // Calculate VAT and total
      const vatAmount = calculateVAT(baseAmount);
      const totalAmount = calculateTotalWithVAT(baseAmount);
      const isProRated = baseAmount < MONTHLY_FEE;

      billingData.push({
        lf_id: lawFirms[i].lf_id,
        lawFirmName: lawFirms[i].lf_org_name,
        mandate: lawFirms[i].mandate,
        baseAmount: baseAmount,
        vatAmount: vatAmount,
        totalAmount: totalAmount,
        isProRated: isProRated
      });

      console.log(`Prepared billing for ${lawFirms[i].lf_org_name}: £${(totalAmount/100).toFixed(2)} (Base: £${(baseAmount/100).toFixed(2)} + VAT: £${(vatAmount/100).toFixed(2)})`);
    }

    // Send consolidated billing email
    await sendConsolidatedBillingEmail(billingData, billingMonth);

    // Mark billing as sent for this month by inserting records for each law firm
    console.log(`📝 Inserting ${billingData.length} billing records to prevent duplicates...`);
    for (let firm of billingData) {
      const mandate = firm.mandate || `LF_${firm.lf_id}`;
      await runQuery(
        con,
        "INSERT INTO billing (mandate, month, year) VALUES (?, ?, ?)",
        [mandate, currentMonth, currentYear]
      );
      console.log(`   ✓ Inserted billing record: ${mandate} for ${currentMonth + 1}/${currentYear}`);
    }

    console.log(`✅ Consolidated billing email sent for ${billingData.length} law firms. Total: £${(billingData.reduce((sum, item) => sum + item.totalAmount, 0)/100).toFixed(2)}`);
    console.log(`🔒 ${billingData.length} billing records inserted to prevent duplicate processing`);

  } catch (error) {
    console.log('❌ Billing job error:', error);
  } finally {
    con.destroy();
  }
});
