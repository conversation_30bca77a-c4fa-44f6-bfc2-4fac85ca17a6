import * as fs from "fs";
import ErrorHandler from "#middlewares/ErrorClass.js";
import { runQuery } from "#utils";
import pool from "../config/db.js";
import client from "../config/gocardless.js";
import { status } from "#middlewares/billing_status.js";
import schedule from "node-schedule";
import { setTimeout } from "timers/promises";

import {
  getTimeInTimezone,
  convertCountryNameToISOCode,
  getCountsForUsers,
} from "./questionaire.service.js";
import { noti_type } from "#middlewares/noti_type.js";
import { connectedUsers, io, notificationsNamespace } from "../../index.js";
import { mailProviderService } from "./mailProvider.service.js";

// Configuration constants
const MONTHLY_FEE = 25000; // £250 in pence - monthly subscription fee
const VAT_RATE = 0.20; // 20% VAT rate

export const getMandate = async (lf_id) => {
  let con = await pool.getConnection();
  try {
    let mandates = await runQuery(
      con,
      `SELECT mandate FROM law_firm where lf_id = ?`,
      [lf_id]
    );
    if (mandates.length == 0) {
      return null;
    }
    return mandates[0]["mandate"];
  } catch (error) {
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};
export const getAllPayment = async (mandate) => {
  let con = await pool.getConnection();
  let payments = [];
  try {
    if (mandate == null || mandate == "") {
      let mandates = await runQuery(
        con,
        `SELECT Distinct mandate , lf_org_name FROM law_firm where mandate is not null`
      );
      if (mandates.length == 0) {
        return [];
      }
      for (let i = 0; i < mandates.length; i++) {
        let list = await client.payments.list({ mandate: mandates[i].mandate });
        for (let j = 0; j < list.payments.length; j++) {
          list.payments[j].lf_org_name = mandates[i].lf_org_name;
          list.payments[j].status = status[list.payments[j].status];
        }
        payments = payments.concat(list.payments);
      }
      return payments;
    } else {
      let payments = await client.payments.list({ mandate: mandate });
      let mandates = await runQuery(
        con,
        `SELECT lf_org_name FROM law_firm where mandate = ?`,
        [mandate]
      );
      for (let j = 0; j < payments.payments.length; j++) {
        payments.payments[j].lf_org_name = mandates[0].lf_org_name;
        payments.payments[j].status = status[payments.payments[j].status];
      }
      return payments.payments;
    }
  } catch (error) {
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};
export const getHistory = async (
  page,
  size = 10,
  keyword = "",
  lf_id = null,
  startDate,
  endDate
) => {
  let con = await pool.getConnection();
  try {
    console.log(startDate, endDate);
    await con.beginTransaction();
    let current_date = new Date();
    if (!startDate) {
      startDate = new Date();
      startDate.setMonth(startDate.getMonth() - 1);
    }
    if (!endDate) endDate = current_date;
    let history, historyCount;
    if (lf_id == null) {
      var sql = fs.readFileSync("app/sql/getAllHistory.sql").toString();
      history = await runQuery(con, sql, [
        startDate,
        endDate,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        (page - 1) * size,
        size * 1,
      ]);
      var sqlcount = fs
        .readFileSync("app/sql/getAllHistoryCount.sql")
        .toString();
      historyCount = await runQuery(con, sqlcount, [
        startDate,
        endDate,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
      ]);
    } else {
      var sql = fs.readFileSync("app/sql/getAllHistoryKeyword.sql").toString();
      history = await runQuery(con, sql, [
        lf_id,
        startDate,
        endDate,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        (page - 1) * size,
        size * 1,
      ]);
      var sqlcount = fs
        .readFileSync("app/sql/getAllHistoryCountKeyword.sql")
        .toString();
      historyCount = await runQuery(con, sqlcount, [
        lf_id,
        startDate,
        endDate,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
      ]);
    }
    let count = historyCount[0]["COUNT(*)"];
    await con.commit();
    return { history, count };
  } catch (error) {
    await con.rollback();
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};
const randomTimeInMs = Math.random() * 3000;

export const createPayment = async (amount, description, mandate) => {
  let con = await pool.getConnection();
  try {
    const result = await setTimeout(randomTimeInMs, "resolved");
    let now = new Date();
    let check = await runQuery(
      con,
      "SELECT * FROM billing where mandate = ? and month = ? and year = ?",
      [mandate, now.getMonth(), now.getFullYear()]
    );
    if (check.length > 0) {
      return;
    }
    let payment = await client.payments.create({
      amount: amount,
      currency: "GBP",
      links: {
        mandate: mandate,
      },
      description: description,
    });
    await runQuery(
      con,
      "INSERT into billing (mandate, month, year) VALUES(?, ?, ?)",
      [mandate, now.getMonth(), now.getFullYear()]
    );
    let lf = await runQuery(
      con,
      "SELECT lf_org_name FROM law_firm WHERE mandate = ?",
      [mandate]
    );
    let admin = await runQuery(
      con,
      "SELECT u.user_id, ui.country FROM users u JOIN user_info ui ON u.user_id = ui.user_id WHERE role IN (?)",
      [1]
    );

    let month = new Date().getMonth();
    let ad_noti = [];
    let lf_ad_noti = [];
    let ad_msg = `Law firm ${lf[0].lf_org_name}'s payment created.`;

    let ctemp_user = 0;
    let condition_query = (list_user, list, message, noti_type) => {
      for (var { user_id: ad_user_id, country } of list_user) {
        let time = getTimeInTimezone(convertCountryNameToISOCode(country));
        list.push([message, time, 1, ad_user_id, null, noti_type, ctemp_user]);
        ctemp_user++;
      }
    };
    condition_query(admin, ad_noti, ad_msg, noti_type.Admin_payment_success);
    var result_ad = await runQuery(
      con,
      "INSERT INTO notification (message, created_at, status, user_id, source, type) VALUES ?",
      [trimListNoti(ad_noti)]
    );
    await con.commit();
    const ad_id = admin.map((user) => user.user_id);
    const count_ad = await getCountsForUsers(ad_id);

    let pushNoti = (
      list_user,
      count_list,
      list_noti,
      message,
      noti_type,
      result
    ) => {
      for (const { user_id: id } of list_user) {
        if (connectedUsers[id]) {
          const count = count_list[id];
          let time = list_noti.find((n) => n[3] === id)[1];
          let noti_id = list_noti.find((n) => n[3] === id)[6];
          notificationsNamespace
            .to(connectedUsers[id].id)
            .emit("notification", {
              message,
              //   qtn_id: obj.qtn_id,
              type: noti_type,
              time,
              count,
              status: 1,
              noti_id: result.insertId + noti_id,
            });
        }
      }
    };
    pushNoti(
      admin,
      count_ad,
      ad_noti,
      ad_msg,
      noti_type.Admin_payment_success,
      result_ad
    );

    // return payment;
  } catch (error) {
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

const trimListNoti = (noti) => {
  return noti.map((item) => item.slice(0, -1));
};

const getMonthName = (month) => {
  const months = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];
  if (month < 1 || month > 12) {
    return "Invalid month";
  }
  return months[month - 1];
};

// Helper function to calculate pro-rated amount for first month
const calculateProRatedAmount = (lawFirmCreatedDate, billingMonth) => {
  const createdDate = new Date(lawFirmCreatedDate);
  const billingDate = new Date(billingMonth);

  // Check if this is the first month of billing
  const isFirstMonth = (
    createdDate.getFullYear() === billingDate.getFullYear() &&
    createdDate.getMonth() === billingDate.getMonth()
  );

  if (!isFirstMonth) {
    return MONTHLY_FEE; // Full monthly fee for subsequent months
  }

  // Calculate pro-rated amount for first month
  const daysInMonth = new Date(billingDate.getFullYear(), billingDate.getMonth() + 1, 0).getDate();
  const daysUsed = daysInMonth - createdDate.getDate() + 1; // Include the creation day
  const proRatedAmount = Math.round((MONTHLY_FEE * daysUsed) / daysInMonth);

  return proRatedAmount;
};

// Helper function to calculate VAT
const calculateVAT = (amount) => {
  return Math.round(amount * VAT_RATE);
};

// Helper function to calculate total amount including VAT
const calculateTotalWithVAT = (baseAmount) => {
  const vatAmount = calculateVAT(baseAmount);
  return baseAmount + vatAmount;
};

// Function to send billing email instead of creating payment
const sendBillingEmail = async (amount, description, mandate, lawFirmName) => {
  try {
    const emailData = {
      to: "<EMAIL>",
      from: { name: "Propero Billing", email: "<EMAIL>" },
      subject: "Monthly Billing Summary",
      html: `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Monthly Billing Summary</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background-color: #d53131; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background-color: #f9f9f9; }
                .billing-details { background-color: white; padding: 15px; border-radius: 5px; margin: 15px 0; }
                .amount { font-size: 24px; font-weight: bold; color: #d53131; }
                .footer { text-align: center; padding: 20px; color: #666; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>Monthly Billing Summary</h1>
                </div>
                <div class="content">
                    <h2>Billing Details</h2>
                    <div class="billing-details">
                        <p><strong>Law Firm:</strong> ${lawFirmName}</p>
                        <p><strong>Mandate:</strong> ${mandate || 'Not Available'}</p>
                        <p><strong>Amount:</strong> <span class="amount">£${(amount/100).toFixed(2)}</span></p>
                        <p><strong>Description:</strong> ${description}</p>
                        <p><strong>Date:</strong> ${new Date().toLocaleDateString('en-GB')}</p>
                    </div>
                </div>
                <div class="footer">
                    <p>This is an automated billing notification from Propero.</p>
                </div>
            </div>
        </body>
        </html>
      `
    };

    // Send email using the mail provider service
    const result = await mailProviderService.sendMail(emailData);
    console.log(`Billing email sent successfully: ${result.messageId}`);
    return result;
  } catch (error) {
    console.error('Failed to send billing email:', error);
    throw error;
  }
};

const billingJob = schedule.scheduleJob("0 0 0 1-5 * *", async function () {
  let con = await pool.getConnection();
  0 * * * * *
  try {
    // Get law firms with mandates and their owner creation dates
    let mandates = await runQuery(
      con,
      `SELECT DISTINCT lf.mandate, lf.lf_id, lf.lf_org_name, u.created_at as owner_created_at
       FROM law_firm lf
       JOIN users u ON lf.lf_id = u.lf_id
       WHERE u.is_owner = 1`
    );
    if (mandates.length == 0) {
      return;
    }
    let today = new Date();
    let billingMonth = new Date(today.setMonth(today.getMonth() - 1));

    for (let i = 0; i < mandates.length; i++) {
      // Calculate the base monthly fee (pro-rated for first month)
      const baseAmount = calculateProRatedAmount(mandates[i].owner_created_at, billingMonth);

      // Calculate total amount including VAT
      const totalAmount = calculateTotalWithVAT(baseAmount);

      // Create description with breakdown
      const vatAmount = calculateVAT(baseAmount);
      const isProRated = baseAmount < MONTHLY_FEE;
      let description = `Monthly subscription for ${billingMonth.getMonth() + 1}/${billingMonth.getFullYear()}`;

      if (isProRated) {
        const createdDate = new Date(mandates[i].owner_created_at);
        const daysInMonth = new Date(billingMonth.getFullYear(), billingMonth.getMonth() + 1, 0).getDate();
        const daysUsed = daysInMonth - createdDate.getDate() + 1;
        description += ` (pro-rated: ${daysUsed}/${daysInMonth} days)`;
      }

      description += ` - Base: £${(baseAmount/100).toFixed(2)}, VAT: £${(vatAmount/100).toFixed(2)}, Total: £${(totalAmount/100).toFixed(2)}`;

      // Send billing email instead of creating payment
      await sendBillingEmail(
        totalAmount,
        description,
        mandates[i].mandate,
        mandates[i].lf_org_name
      );

      console.log(`Billing email sent for law firm ${mandates[i].lf_org_name} (${mandates[i].lf_id}): £${(totalAmount/100).toFixed(2)} (Base: £${(baseAmount/100).toFixed(2)} + VAT: £${(vatAmount/100).toFixed(2)})`);
    }
  } catch (error) {
    console.log(error);
  } finally {
    con.destroy();
  }
});
