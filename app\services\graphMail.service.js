import { ConfidentialClientApplication } from "@azure/msal-node";
import { Client } from "@microsoft/microsoft-graph-client";
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from "#middlewares/ErrorClass.js";
import { graphCredentialsService } from "./graphCredentials.service.js";
import dotenv from "dotenv";

dotenv.config();

/**
 * Microsoft Graph Mail Service
 * Handles email sending through Microsoft Graph API using client credentials flow
 */
export class GraphMailService {
  constructor() {
    this.tokenCache = new Map(); // Simple in-memory cache for tokens
  }

  /**
   * Get Microsoft Graph credentials for a law firm with decrypted client secret
   * @param {number} lf_id - Law firm ID
   * @returns {Object} Graph credentials with decrypted client_secret
   */
  async getGraphCredentials(lf_id) {
    try {
      // Use the graphCredentialsService to get credentials with decrypted secret
      const credentials = await graphCredentialsService.getCredentials(lf_id, true);

      if (!credentials) {
        return null;
      }

      return credentials;
    } catch (error) {
      throw ErrorHandler.badRequestError(`Failed to get Graph credentials: ${error.message}`);
    }
  }

  /**
   * Get access token using client credentials flow
   * @param {Object} credentials - Graph API credentials
   * @returns {string} Access token
   */
  async getAccessToken(credentials) {
    const { tenant_id, client_id, client_secret } = credentials;
    const cacheKey = `${tenant_id}_${client_id}`;
    
    // Check cache first
    const cachedToken = this.tokenCache.get(cacheKey);
    if (cachedToken && cachedToken.expiresAt > Date.now()) {
      return cachedToken.token;
    }

    try {
      const clientConfig = {
        auth: {
          clientId: client_id,
          clientSecret: client_secret,
          authority: `https://login.microsoftonline.com/${tenant_id}`
        }
      };

      const cca = new ConfidentialClientApplication(clientConfig);
      
      const clientCredentialRequest = {
        scopes: ['https://graph.microsoft.com/.default'],
        // Ensure we're requesting application permissions, not delegated
        skipCache: false
      };

      const response = await cca.acquireTokenByClientCredential(clientCredentialRequest);
      
      if (!response || !response.accessToken) {
        throw new Error('Failed to acquire access token');
      }

      // Cache the token (expires 5 minutes before actual expiry)
      const expiresAt = Date.now() + ((response.expiresOn.getTime() - Date.now()) - 300000);
      this.tokenCache.set(cacheKey, {
        token: response.accessToken,
        expiresAt: expiresAt
      });

      return response.accessToken;
    } catch (error) {
      throw ErrorHandler.badRequestError(`Failed to get access token: ${error.message}`);
    }
  }

  /**
   * Create Graph client with authentication
   * @param {string} accessToken - Access token
   * @returns {Client} Microsoft Graph client
   */
  createGraphClient(accessToken) {
    return Client.init({
      authProvider: (done) => {
        done(null, accessToken);
      }
    });
  }

  /**
   * Send email using Microsoft Graph API
   * @param {Object} emailData - Email data
   * @param {number} lf_id - Law firm ID
   * @param {string} user_email - Email of the user making the request (lawfirm admin/super admin)
   * @returns {Object} Send result
   */
  async sendMail(emailData, lf_id, user_email = null) {
    try {
      // Get Graph credentials for the law firm
      const credentials = await this.getGraphCredentials(lf_id);
      if (!credentials) {
        throw new Error('Microsoft Graph credentials not configured for this law firm');
      }

      // Check if credentials are active
      if (!credentials.is_active) {
        throw new Error('Microsoft Graph credentials are disabled for this law firm. Please contact your administrator to enable them.');
      }

      // Get access token with explicit scopes for "Send As"
      const accessToken = await this.getAccessToken(credentials);

      // Create Graph client
      const graphClient = this.createGraphClient(accessToken);

      // Prepare email message for "Send As" (not "Send on Behalf")
      const message = {
        message: {
          subject: emailData.subject,
          body: {
            contentType: emailData.contentType || 'HTML',
            content: emailData.html || emailData.text
          },
          toRecipients: this.formatRecipients(emailData.to),
          ccRecipients: emailData.cc ? this.formatRecipients(emailData.cc) : [],
          bccRecipients: emailData.bcc ? this.formatRecipients(emailData.bcc) : []
        },
        // Explicitly set saveToSentItems to ensure it's treated as "Send As"
        saveToSentItems: true
      };

      // Do NOT set the from address - let Graph API use the authenticated user's mailbox
      // This ensures "Send As" behavior instead of "Send on Behalf"

      // For client credentials flow, we need to specify a user mailbox to send from
      // Use the requesting user's email (lawfirm admin/super admin) as the sender
      let senderUserId = null;

      // If user email is provided, use it directly
      if (user_email) {
        try {
          const user = await graphClient.api(`/users/${user_email}`).get();
          if (user && user.id) {
            senderUserId = user.id;
            console.log(`✅ Using requesting user as sender: ${user_email}`);

            // Note: Mailbox verification is not required for sending emails
            console.log(`📬 User found and ready for email sending: ${user_email}`);
          } else {
            throw new Error(`User ${user_email} found but has no ID or mailbox`);
          }
        } catch (error) {
          console.error(`❌ Requesting user ${user_email} not found in tenant:`, error.message);
          throw new Error(`The requesting user ${user_email} does not exist in the Microsoft tenant or does not have a mailbox. Please ensure the user is properly configured in Microsoft 365.`);
        }
      }

      // If no user email provided, we cannot send the email
      if (!senderUserId) {
        throw new Error('No user email provided for sending email. Please ensure the requesting user context is passed correctly.');
      }



      // Send email using the selected user's mailbox (Send As, not Send on Behalf)
      console.log(`📧 Attempting to send email via Graph API using user ID: ${senderUserId}`);
      console.log(`🔐 Authentication context: Client Credentials (Send As mode)`);
      console.log(`📤 Email will be sent FROM the user's mailbox directly`);

      const result = await graphClient
        .api(`/users/${senderUserId}/sendMail`)
        .post(message);

      console.log(`✅ Email sent successfully via Graph API for law firm ${lf_id}`);
      return {
        success: true,
        messageId: result?.id || 'sent',
        provider: 'microsoft-graph',
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error(`❌ Graph API error for law firm ${lf_id}:`, error.message);

      // Provide more specific error messages for common issues
      if (error.message.includes('mailbox is either inactive, soft-deleted, or is hosted on-premise')) {
        throw ErrorHandler.badRequestError(`The selected user mailbox is not available for sending emails. This usually happens with external/guest users or inactive mailboxes. Please ensure the tenant has proper internal users with Exchange Online mailboxes.`);
      } else if (error.message.includes('Insufficient privileges')) {
        throw ErrorHandler.badRequestError(`Insufficient permissions to send email. Please ensure the application has Mail.Send permission and admin consent.`);
      } else if (error.message.includes('does not exist or one of its queried reference-property objects are not present')) {
        throw ErrorHandler.badRequestError(`The user account does not exist in the tenant or does not have a mailbox.`);
      } else {
        throw ErrorHandler.badRequestError(`Failed to send email via Graph API: ${error.message}`);
      }
    }
  }

  /**
   * Format recipients for Graph API
   * @param {string|Array} recipients - Recipients
   * @returns {Array} Formatted recipients
   */
  formatRecipients(recipients) {
    if (!recipients) return [];
    
    const recipientArray = Array.isArray(recipients) ? recipients : [recipients];
    
    return recipientArray.map(recipient => {
      if (typeof recipient === 'string') {
        return {
          emailAddress: {
            address: recipient
          }
        };
      } else if (recipient.email) {
        return {
          emailAddress: {
            address: recipient.email,
            name: recipient.name || recipient.email
          }
        };
      }
      return recipient;
    });
  }

  /**
   * Test Graph API connection
   * @param {number} lf_id - Law firm ID
   * @returns {Object} Test result
   */
  async testConnection(lf_id) {
    try {
      const credentials = await this.getGraphCredentials(lf_id);
      if (!credentials) {
        return {
          success: false,
          message: 'No Graph credentials configured'
        };
      }

      // Check if credentials are active
      if (!credentials.is_active) {
        return {
          success: false,
          message: 'Graph credentials are disabled for this law firm'
        };
      }

      const accessToken = await this.getAccessToken(credentials);
      const graphClient = this.createGraphClient(accessToken);

      // Test by getting user profile
      await graphClient.api('/me').get();

      return {
        success: true,
        message: 'Graph API connection successful'
      };
    } catch (error) {
      return {
        success: false,
        message: `Graph API connection failed: ${error.message}`
      };
    }
  }
}

export const graphMailService = new GraphMailService();
