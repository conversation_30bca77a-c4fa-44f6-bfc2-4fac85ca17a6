import { describe, test, expect, beforeAll, afterAll } from '@jest/globals';

describe('Billing Email System', () => {
  const MONTHLY_FEE = 25000; // £250 in pence
  const VAT_RATE = 0.20; // 20%

  // Helper functions (copied from billing service for testing)
  const calculateVAT = (amount) => Math.round(amount * VAT_RATE);
  
  const calculateTotalWithVAT = (baseAmount) => baseAmount + calculateVAT(baseAmount);
  
  const calculateProRatedAmount = (lawFirmCreatedDate, billingMonth) => {
    const createdDate = new Date(lawFirmCreatedDate);
    const billingDate = new Date(billingMonth);
    
    const isFirstMonth = (
      createdDate.getFullYear() === billingDate.getFullYear() &&
      createdDate.getMonth() === billingDate.getMonth()
    );
    
    if (!isFirstMonth) {
      return MONTHLY_FEE;
    }
    
    const daysInMonth = new Date(billingDate.getFullYear(), billingDate.getMonth() + 1, 0).getDate();
    const daysUsed = daysInMonth - createdDate.getDate() + 1;
    return Math.round((MONTHLY_FEE * daysUsed) / daysInMonth);
  };

  describe('Email Content Generation', () => {
    test('should generate correct billing email content for full month', () => {
      const baseAmount = MONTHLY_FEE;
      const vatAmount = calculateVAT(baseAmount);
      const totalAmount = calculateTotalWithVAT(baseAmount);
      
      const lawFirmName = "Test Law Firm Ltd";
      const mandate = "MD123456";
      const description = `Monthly subscription for 1/2024 - Base: £${(baseAmount/100).toFixed(2)}, VAT: £${(vatAmount/100).toFixed(2)}, Total: £${(totalAmount/100).toFixed(2)}`;
      
      expect(baseAmount).toBe(25000); // £250
      expect(vatAmount).toBe(5000); // £50
      expect(totalAmount).toBe(30000); // £300
      
      // Verify email content structure
      expect(description).toContain('Monthly subscription for 1/2024');
      expect(description).toContain('Base: £250.00');
      expect(description).toContain('VAT: £50.00');
      expect(description).toContain('Total: £300.00');
      
      console.log(`✓ Full month billing: ${description}`);
    });

    test('should generate correct billing email content for pro-rated month', () => {
      const createdDate = '2024-01-15'; // Created on 15th
      const billingMonth = '2024-01-01'; // Billing for January
      
      const baseAmount = calculateProRatedAmount(createdDate, billingMonth);
      const vatAmount = calculateVAT(baseAmount);
      const totalAmount = calculateTotalWithVAT(baseAmount);
      
      const expectedBaseAmount = Math.round((25000 * 17) / 31); // 17 days out of 31
      
      expect(baseAmount).toBe(expectedBaseAmount);
      expect(vatAmount).toBe(Math.round(expectedBaseAmount * 0.20));
      expect(totalAmount).toBe(baseAmount + vatAmount);
      
      const lawFirmName = "Pro-rated Law Firm Ltd";
      const mandate = "MD789012";
      const description = `Monthly subscription for 1/2024 (pro-rated: 17/31 days) - Base: £${(baseAmount/100).toFixed(2)}, VAT: £${(vatAmount/100).toFixed(2)}, Total: £${(totalAmount/100).toFixed(2)}`;
      
      expect(description).toContain('pro-rated: 17/31 days');
      
      console.log(`✓ Pro-rated billing: ${description}`);
    });

    test('should handle law firms without mandates', () => {
      const baseAmount = MONTHLY_FEE;
      const totalAmount = calculateTotalWithVAT(baseAmount);
      
      const lawFirmName = "No Mandate Law Firm";
      const mandate = null;
      
      // Should still generate email content even without mandate
      expect(lawFirmName).toBeTruthy();
      expect(totalAmount).toBe(30000); // £300
      
      console.log(`✓ No mandate billing: Law Firm: ${lawFirmName}, Mandate: ${mandate || 'Not Available'}, Amount: £${(totalAmount/100).toFixed(2)}`);
    });
  });

  describe('Consolidated Email Format', () => {
    test('should send consolidated email to correct address', () => {
      const expectedRecipient = "<EMAIL>";

      // Mock consolidated email data structure
      const emailData = {
        to: expectedRecipient,
        from: { name: "Propero Billing", email: "<EMAIL>" },
        subject: "Monthly Billing Summary - January 2024"
      };

      expect(emailData.to).toBe(expectedRecipient);
      expect(emailData.from.name).toBe("Propero Billing");
      expect(emailData.subject).toContain("Monthly Billing Summary");

      console.log(`✓ Consolidated email configured for: ${emailData.to}`);
    });

    test('should include all required consolidated billing information', () => {
      const consolidatedBillingData = [
        {
          lf_id: 1,
          lawFirmName: "Alpha Law Firm Ltd",
          mandate: "MD123456",
          baseAmount: 25000,
          vatAmount: 5000,
          totalAmount: 30000,
          isProRated: false
        },
        {
          lf_id: 2,
          lawFirmName: "Beta Legal Services",
          mandate: "MD789012",
          baseAmount: 13710, // Pro-rated amount
          vatAmount: 2742,
          totalAmount: 16452,
          isProRated: true
        }
      ];

      const totalAmount = consolidatedBillingData.reduce((sum, item) => sum + item.totalAmount, 0);
      const totalFirms = consolidatedBillingData.length;

      // Verify consolidated data structure
      expect(consolidatedBillingData).toHaveLength(2);
      expect(totalAmount).toBe(46452); // £464.52
      expect(totalFirms).toBe(2);

      // Verify each firm has required fields
      consolidatedBillingData.forEach(firm => {
        expect(firm.lawFirmName).toBeTruthy();
        expect(firm.totalAmount).toBeGreaterThan(0);
        expect(firm.baseAmount).toBeGreaterThan(0);
        expect(firm.vatAmount).toBeGreaterThan(0);
        expect(typeof firm.isProRated).toBe('boolean');
      });

      console.log(`✓ Consolidated billing data: ${totalFirms} firms, Total: £${(totalAmount/100).toFixed(2)}`);
    });
  });

  describe('Consolidated Billing Integration', () => {
    test('should handle consolidated billing for multiple law firms', () => {
      const lawFirms = [
        {
          lf_id: 1,
          lf_org_name: "Alpha Law Firm",
          mandate: "MD001",
          owner_created_at: "2024-01-01" // Full month
        },
        {
          lf_id: 2,
          lf_org_name: "Beta Legal Services",
          mandate: "MD002",
          owner_created_at: "2024-01-15" // Pro-rated
        },
        {
          lf_id: 3,
          lf_org_name: "Gamma Solicitors",
          mandate: null, // No mandate
          owner_created_at: "2023-12-01" // Previous month (full)
        }
      ];

      const billingMonth = "2024-01-01";
      const billingData = [];

      lawFirms.forEach(firm => {
        const baseAmount = calculateProRatedAmount(firm.owner_created_at, billingMonth);
        const vatAmount = calculateVAT(baseAmount);
        const totalAmount = calculateTotalWithVAT(baseAmount);

        billingData.push({
          lf_id: firm.lf_id,
          lawFirmName: firm.lf_org_name,
          mandate: firm.mandate,
          baseAmount: baseAmount,
          vatAmount: vatAmount,
          totalAmount: totalAmount,
          isProRated: baseAmount < MONTHLY_FEE
        });
      });

      // Calculate consolidated totals
      const totalAmount = billingData.reduce((sum, item) => sum + item.totalAmount, 0);
      const totalFirms = billingData.length;
      const proRatedCount = billingData.filter(item => item.isProRated).length;

      expect(billingData).toHaveLength(3);
      expect(totalFirms).toBe(3);
      expect(proRatedCount).toBe(1); // Only the second firm is pro-rated
      expect(totalAmount).toBeGreaterThan(0);

      // Verify individual amounts
      expect(billingData[0].totalAmount).toBe(30000); // Full month
      expect(billingData[1].isProRated).toBe(true); // Pro-rated
      expect(billingData[2].totalAmount).toBe(30000); // Full month (previous month creation)

      console.log('✓ Consolidated billing results:');
      console.log(`   Total firms: ${totalFirms}`);
      console.log(`   Pro-rated firms: ${proRatedCount}`);
      console.log(`   Total amount: £${(totalAmount/100).toFixed(2)}`);

      billingData.forEach((firm, index) => {
        console.log(`   ${index + 1}. ${firm.lawFirmName}: £${(firm.totalAmount/100).toFixed(2)} ${firm.isProRated ? '(pro-rated)' : '(full)'}`);
      });
    });

    test('should track billing to prevent duplicates', () => {
      // Mock billing tracking data
      const currentMonth = new Date().getMonth();
      const currentYear = new Date().getFullYear();

      const billingRecords = [
        { mandate: 'MD001', month: currentMonth, year: currentYear, lf_id: 1 },
        { mandate: 'MD002', month: currentMonth, year: currentYear, lf_id: 2 },
        { mandate: 'LF_3', month: currentMonth, year: currentYear, lf_id: 3 } // No mandate case
      ];

      // Verify tracking structure
      expect(billingRecords).toHaveLength(3);
      billingRecords.forEach(record => {
        expect(record.month).toBe(currentMonth);
        expect(record.year).toBe(currentYear);
        expect(record.lf_id).toBeGreaterThan(0);
        expect(record.mandate).toBeTruthy();
      });

      console.log('✓ Billing tracking records created for duplicate prevention');
      console.log(`   Month: ${currentMonth + 1}/${currentYear}`);
      console.log(`   Records: ${billingRecords.length}`);
    });
  });

  describe('Error Handling', () => {
    test('should handle invalid dates gracefully', () => {
      const invalidDate = "invalid-date";
      
      // Should not throw error, but handle gracefully
      expect(() => {
        const createdDate = new Date(invalidDate);
        const isValidDate = !isNaN(createdDate.getTime());
        expect(isValidDate).toBe(false);
      }).not.toThrow();
      
      console.log('✓ Invalid date handling works correctly');
    });

    test('should handle missing law firm data', () => {
      const incompleteFirm = {
        lf_id: 999,
        // Missing lf_org_name, mandate, owner_created_at
      };
      
      // Should handle missing data gracefully
      const lawFirmName = incompleteFirm.lf_org_name || `Law Firm ${incompleteFirm.lf_id}`;
      const mandate = incompleteFirm.mandate || null;
      
      expect(lawFirmName).toBe("Law Firm 999");
      expect(mandate).toBeNull();
      
      console.log('✓ Missing data handling works correctly');
    });
  });
});
