import { describe, test, expect, beforeAll, afterAll } from '@jest/globals';

describe('Billing Email System', () => {
  const MONTHLY_FEE = 25000; // £250 in pence
  const VAT_RATE = 0.20; // 20%

  // Helper functions (copied from billing service for testing)
  const calculateVAT = (amount) => Math.round(amount * VAT_RATE);
  
  const calculateTotalWithVAT = (baseAmount) => baseAmount + calculateVAT(baseAmount);
  
  const calculateProRatedAmount = (lawFirmCreatedDate, billingMonth) => {
    const createdDate = new Date(lawFirmCreatedDate);
    const billingDate = new Date(billingMonth);
    
    const isFirstMonth = (
      createdDate.getFullYear() === billingDate.getFullYear() &&
      createdDate.getMonth() === billingDate.getMonth()
    );
    
    if (!isFirstMonth) {
      return MONTHLY_FEE;
    }
    
    const daysInMonth = new Date(billingDate.getFullYear(), billingDate.getMonth() + 1, 0).getDate();
    const daysUsed = daysInMonth - createdDate.getDate() + 1;
    return Math.round((MONTHLY_FEE * daysUsed) / daysInMonth);
  };

  describe('Email Content Generation', () => {
    test('should generate correct billing email content for full month', () => {
      const baseAmount = MONTHLY_FEE;
      const vatAmount = calculateVAT(baseAmount);
      const totalAmount = calculateTotalWithVAT(baseAmount);
      
      const lawFirmName = "Test Law Firm Ltd";
      const mandate = "MD123456";
      const description = `Monthly subscription for 1/2024 - Base: £${(baseAmount/100).toFixed(2)}, VAT: £${(vatAmount/100).toFixed(2)}, Total: £${(totalAmount/100).toFixed(2)}`;
      
      expect(baseAmount).toBe(25000); // £250
      expect(vatAmount).toBe(5000); // £50
      expect(totalAmount).toBe(30000); // £300
      
      // Verify email content structure
      expect(description).toContain('Monthly subscription for 1/2024');
      expect(description).toContain('Base: £250.00');
      expect(description).toContain('VAT: £50.00');
      expect(description).toContain('Total: £300.00');
      
      console.log(`✓ Full month billing: ${description}`);
    });

    test('should generate correct billing email content for pro-rated month', () => {
      const createdDate = '2024-01-15'; // Created on 15th
      const billingMonth = '2024-01-01'; // Billing for January
      
      const baseAmount = calculateProRatedAmount(createdDate, billingMonth);
      const vatAmount = calculateVAT(baseAmount);
      const totalAmount = calculateTotalWithVAT(baseAmount);
      
      const expectedBaseAmount = Math.round((25000 * 17) / 31); // 17 days out of 31
      
      expect(baseAmount).toBe(expectedBaseAmount);
      expect(vatAmount).toBe(Math.round(expectedBaseAmount * 0.20));
      expect(totalAmount).toBe(baseAmount + vatAmount);
      
      const lawFirmName = "Pro-rated Law Firm Ltd";
      const mandate = "MD789012";
      const description = `Monthly subscription for 1/2024 (pro-rated: 17/31 days) - Base: £${(baseAmount/100).toFixed(2)}, VAT: £${(vatAmount/100).toFixed(2)}, Total: £${(totalAmount/100).toFixed(2)}`;
      
      expect(description).toContain('pro-rated: 17/31 days');
      
      console.log(`✓ Pro-rated billing: ${description}`);
    });

    test('should handle law firms without mandates', () => {
      const baseAmount = MONTHLY_FEE;
      const totalAmount = calculateTotalWithVAT(baseAmount);
      
      const lawFirmName = "No Mandate Law Firm";
      const mandate = null;
      
      // Should still generate email content even without mandate
      expect(lawFirmName).toBeTruthy();
      expect(totalAmount).toBe(30000); // £300
      
      console.log(`✓ No mandate billing: Law Firm: ${lawFirmName}, Mandate: ${mandate || 'Not Available'}, Amount: £${(totalAmount/100).toFixed(2)}`);
    });
  });

  describe('Email Recipients and Format', () => {
    test('should send to correct email address', () => {
      const expectedRecipient = "<EMAIL>";
      
      // Mock email data structure
      const emailData = {
        to: expectedRecipient,
        from: { name: "Propero Billing", email: "<EMAIL>" },
        subject: "Monthly Billing Summary"
      };
      
      expect(emailData.to).toBe(expectedRecipient);
      expect(emailData.from.name).toBe("Propero Billing");
      expect(emailData.subject).toBe("Monthly Billing Summary");
      
      console.log(`✓ Email configured for: ${emailData.to}`);
    });

    test('should include all required billing information', () => {
      const billingInfo = {
        lawFirmName: "Complete Law Firm Ltd",
        mandate: "MD456789",
        amount: 30000, // £300 in pence
        description: "Monthly subscription for 2/2024 - Base: £250.00, VAT: £50.00, Total: £300.00",
        date: new Date().toLocaleDateString('en-GB')
      };
      
      // Verify all required fields are present
      expect(billingInfo.lawFirmName).toBeTruthy();
      expect(billingInfo.amount).toBeGreaterThan(0);
      expect(billingInfo.description).toContain('Monthly subscription');
      expect(billingInfo.date).toBeTruthy();
      
      console.log(`✓ Complete billing info: ${JSON.stringify(billingInfo, null, 2)}`);
    });
  });

  describe('Integration Scenarios', () => {
    test('should handle multiple law firms billing', () => {
      const lawFirms = [
        {
          lf_id: 1,
          lf_org_name: "Alpha Law Firm",
          mandate: "MD001",
          owner_created_at: "2024-01-01" // Full month
        },
        {
          lf_id: 2,
          lf_org_name: "Beta Legal Services",
          mandate: "MD002", 
          owner_created_at: "2024-01-15" // Pro-rated
        },
        {
          lf_id: 3,
          lf_org_name: "Gamma Solicitors",
          mandate: null, // No mandate
          owner_created_at: "2023-12-01" // Previous month (full)
        }
      ];

      const billingMonth = "2024-01-01";
      const results = [];

      lawFirms.forEach(firm => {
        const baseAmount = calculateProRatedAmount(firm.owner_created_at, billingMonth);
        const totalAmount = calculateTotalWithVAT(baseAmount);
        
        results.push({
          firmName: firm.lf_org_name,
          mandate: firm.mandate,
          amount: totalAmount,
          isProRated: baseAmount < MONTHLY_FEE
        });
      });

      expect(results).toHaveLength(3);
      expect(results[0].amount).toBe(30000); // Full month
      expect(results[1].isProRated).toBe(true); // Pro-rated
      expect(results[2].amount).toBe(30000); // Full month (previous month creation)

      console.log('✓ Multiple law firms billing results:');
      results.forEach((result, index) => {
        console.log(`   ${index + 1}. ${result.firmName}: £${(result.amount/100).toFixed(2)} ${result.isProRated ? '(pro-rated)' : '(full)'}`);
      });
    });
  });

  describe('Error Handling', () => {
    test('should handle invalid dates gracefully', () => {
      const invalidDate = "invalid-date";
      
      // Should not throw error, but handle gracefully
      expect(() => {
        const createdDate = new Date(invalidDate);
        const isValidDate = !isNaN(createdDate.getTime());
        expect(isValidDate).toBe(false);
      }).not.toThrow();
      
      console.log('✓ Invalid date handling works correctly');
    });

    test('should handle missing law firm data', () => {
      const incompleteFirm = {
        lf_id: 999,
        // Missing lf_org_name, mandate, owner_created_at
      };
      
      // Should handle missing data gracefully
      const lawFirmName = incompleteFirm.lf_org_name || `Law Firm ${incompleteFirm.lf_id}`;
      const mandate = incompleteFirm.mandate || null;
      
      expect(lawFirmName).toBe("Law Firm 999");
      expect(mandate).toBeNull();
      
      console.log('✓ Missing data handling works correctly');
    });
  });
});
