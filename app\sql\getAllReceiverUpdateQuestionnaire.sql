(SELECT
	qr.user_id 
	, ui.first_name 
	, u.email 
	, u.role 
FROM questionaires_request qr 
JOIN users u ON qr.user_id = u.user_id 
JOIN user_info ui  ON u.user_id = ui.user_id 
WHERE qr.qtn_id = ?
ORDER BY qr.id DESC 
LIMIT 1)
UNION
SELECT 
	u2.user_id 
	, ui2.first_name 
	, u2.email 
	, u2.role
FROM users u2 
JOIN user_info ui2 ON u2.user_id = ui2.user_id
WHERE (u2.is_owner = 1 or u2.role = 5) AND u2.lf_id = ?;