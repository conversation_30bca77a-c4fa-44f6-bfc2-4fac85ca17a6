import sgMail from "@sendgrid/mail";
import dotenv from "dotenv";

dotenv.config();
export const submitCaseClientMail = async (
    email,
    first_name,
    path,
    bgColor,
    textColor,
    title,
    header,
    body,
    footer,
    logo,
    user
) => {
    if (!logo) {
        logo = "width: 80px; height: 80px;"
    }
    if (!title) {
        title = `Your new case is ready`
    }
    if (!header) {
        header = `                 
            <p class="invitation-text">Notification</p>
        `;
    }
    if (!body) {
        body =
            `<div class="invite-wrapper">
                    <p>
                        Hello ${first_name},
                    </p>
                    <p>
                        Your case has been sent.
                    </p>
                </div>`
    }
    if (!footer) {
        footer = `                    
        <p>
            Best regards,
        </p>
        <p>
            Legal Workflow Limited Team
        </p>`;
    }
    if (!bgColor) {
        bgColor = "rgba(213, 49, 49, 0.71)";
    }
    if (!textColor) {
        textColor = "#fff";
    }
    if (!path) {
        path = "static/images/logo.png";
    }
    for (let key in user) {
        body = body.replaceAll(`\${${key}}`, user[key] != null ? user[key] : "");
        body = body.replaceAll(`{{${key}}}`, user[key] != null ? user[key] : "");
        body = body.replaceAll(`{${key}}`, user[key] != null ? user[key] : "");
        body = body.replaceAll(`[${key}]`, user[key] != null ? user[key] : "");
        header = header.replaceAll(`\${${key}}`, user[key] != null ? user[key] : "");
        header = header.replaceAll(`{{${key}}}`, user[key] != null ? user[key] : "");
        header = header.replaceAll(`{${key}}`, user[key] != null ? user[key] : "");
        header = header.replaceAll(`[${key}]`, user[key] != null ? user[key] : "");
        footer = footer.replaceAll(`\${${key}}`, user[key] != null ? user[key] : "");
        footer = footer.replaceAll(`{{${key}}}`, user[key] != null ? user[key] : "");
        footer = footer.replaceAll(`{${key}}`, user[key] != null ? user[key] : "");
        footer = footer.replaceAll(`[${key}]`, user[key] != null ? user[key] : "");
        title = title.replaceAll(`\${${key}}`, user[key] != null ? user[key] : "");
        title = title.replaceAll(`{{${key}}}`, user[key] != null ? user[key] : "");
        title = title.replaceAll(`{${key}}`, user[key] != null ? user[key] : "");
        title = title.replaceAll(`[${key}]`, user[key] != null ? user[key] : "");
    }
    sgMail.setApiKey(process.env.SENDGRID_API_KEY);
    let msg = {};
    msg = {
        from: { name: "Propero Support", email: "<EMAIL>" },
        to: email,
        subject: `${title}`,
        html: `<!DOCTYPE html>
        <html lang="en">

        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Document</title>
            <style>
                * {
                    box-sizing: border-box;
                }

                body {
                    margin: 0;
                    padding: 0;
                    font-family: 'Inter', sans-serif;
                    background-color: #fff;
                }

                p {
                    margin: 0;
                }

                .card {
                    padding: 24px;
                    width: 460px;
                    border: 1px solid rgba(0, 0, 0, 0.75);
                }

                .card .logo-wrapper {
                    text-align: center;
                    align-items: center;
                    margin-bottom: 20px;
                }
                .card .logo {
                    width: 80px;
                    height: 80px;
                }
                .card .invitation-wrapper {
                    background-color: #F5F5F5;
                    height: 54px;
                    padding: 0 8px;
                }

                .card .invitation-wrapper .invitation-text {
                    line-height: 54px;
                    color: #000;
                    font-weight: 800;
                    font-size: 14px;
                }

                .card .description-wrapper {
                    margin: 8px;
                    color: #94A3B8;
                    font-size: 11px;
                }

                .card .invite-wrapper {
                    color: #667085;
                    font-size: 14px;
                    line-height: 1.5;
                    margin: 0 8px;
                    gap: 4px;
                }

                .card .button-wrapper {
                    margin: 32px 8px;
                }

                .card .button-wrapper .button {
                    background-color: ${bgColor};
                    color: ${textColor};
                    font-size: 14px;
                    height: 40px;
                    border-radius: 16px;
                    cursor: pointer;
                    width: 100%;
                    border: none;
                }

                .card .regards-wrapper {
                    margin: 8px;
                    line-height: 1.5;
                    color: #667085;
                    font-size: 14px;
                }
            </style>
        </head>

        <body>
            <div class="card">
                <div class="logo-wrapper">
                    <img style="${logo}" src="${process.env.IMAGE_URL}/${path}" alt="Company Logo">
                </div>
                <div class="invitation-wrapper">
                    ${header}
                </div>
                <div class="description-wrapper">
                    <p>
                        This is an automatically generated email, please do not reply.
                    </p>
                </div>
                <div class="invite-wrapper">
                    ${body}
                </div>
                <div class="regards-wrapper">
                    ${footer}
                </div>
            </div>

            </div>
        </body>

        </html>`
    };
    try {
        await sgMail.send(msg);
    } catch (error) {
        console.error(error);

        if (error.response) {
            console.error(error.response.body);
        }
    }
};