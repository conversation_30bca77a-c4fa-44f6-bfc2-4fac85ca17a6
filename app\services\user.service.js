import Error<PERSON><PERSON><PERSON> from "#middlewares/ErrorClass.js";
import {
  generateToken,
  runQuery,
  passwordHashing,
  hookMessage,
  generateTempTokenLogin,
} from "#utils";
import * as fs from "fs";
import pool from "../config/db.js";
import { roles } from "#middlewares/roles.js";
import { s3Client } from "../config/s3.js";
import { S3SyncClient } from "s3-sync-client";
import moment from "moment-timezone";
import ct from "countries-and-timezones";
import countryCodes from "country-codes-list";
import jwt from "jsonwebtoken";
import { mailService, smsService } from "#services";
import generator from "generate-password";
import env from "dotenv";
import { checkConnectPMS } from "./case.service.js";

export const getLawfirm = async (page, size = 10, keyword) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    let lawfirm, lawfirmcount;
    if (keyword == null) {
      var sql = fs.readFileSync("app/sql/getLawfirm.sql").toString();
      lawfirm = await runQuery(con, sql, [(page - 1) * size, size * 1]);
      var sqlcount = fs.readFileSync("app/sql/getLawfirmCount.sql").toString();
      lawfirmcount = await runQuery(con, sqlcount);
    } else {
      var sql = fs.readFileSync("app/sql/getLawfirmKeyword.sql").toString();
      lawfirm = await runQuery(con, sql, [
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        (page - 1) * size,
        size * 1,
      ]);
      var sqlcount = fs
        .readFileSync("app/sql/getLawfirmCountKeyword.sql")
        .toString();
      lawfirmcount = await runQuery(con, sqlcount, [
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
      ]);
    }
    let count = lawfirmcount[0]["COUNT(*)"];
    await con.commit();
    return { lawfirm, count };
  } catch (error) {
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};
export const getLawfirmDetails = async (lf_id) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    var sql = fs.readFileSync("app/sql/getLawfirmDetails.sql").toString();
    let lawfirm = await runQuery(con, sql, [lf_id]);
    await con.commit();
    return lawfirm;
  } catch (error) {
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};
export const updateLawFirmStatus = async (status, lf_id) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    var sql = fs.readFileSync("app/sql/updateLawFirmStatus.sql").toString();
    let lawfirm = await runQuery(con, sql, [status, lf_id]);
    if (lawfirm.affectedRows > 0) {
      await con.commit();
      return lawfirm;
    } else {
      throw ErrorHandler.badRequestError("No lawfirm found");
    }
  } catch (error) {
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};
export const updateLawFirmMandate = async (mandate, lf_id) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    var sql = fs.readFileSync("app/sql/updateLawFirmMandate.sql").toString();
    let lawfirm = await runQuery(con, sql, [mandate, lf_id]);
    if (lawfirm.affectedRows > 0) {
      await con.commit();
      return lawfirm;
    } else {
      throw ErrorHandler.badRequestError("No lawfirm found");
    }
  } catch (error) {
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};
export const updateLawFirmPrefix = async (prefix, lf_id) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    var sql = fs.readFileSync("app/sql/updateLawFirmPrefix.sql").toString();
    let lawfirm = await runQuery(con, sql, [prefix, lf_id]);
    if (lawfirm.affectedRows > 0) {
      await con.commit();
      return lawfirm;
    } else {
      throw ErrorHandler.badRequestError("No lawfirm found");
    }
  } catch (error) {
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const updateRole = async (role, user_id) => {
  let con = await pool.getConnection();
  try {
    let user = await runQuery(
      con,
      "UPDATE users SET role = ? WHERE user_id = ?",
      [role, user_id]
    );
    return user;
  } catch (error) {
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const getUserLawfirm = async (lf_id, page, size = 10, keyword) => {
  let con = await pool.getConnection();

  try {
    await con.beginTransaction();

    let userlf, userlfcount;
    if (!keyword) {
      var sql = fs.readFileSync("app/sql/getUserLawfirm.sql").toString();
      userlf = await runQuery(con, sql, [lf_id, (page - 1) * size, size * 1]);
      var sqlcount = fs
        .readFileSync("app/sql/getUserLawfirmCount.sql")
        .toString();
      userlfcount = await runQuery(con, sqlcount, [lf_id]);
    } else {
      var sql = fs.readFileSync("app/sql/getUserLawfirmKeyword.sql").toString();
      userlf = await runQuery(con, sql, [
        lf_id,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        (page - 1) * size,
        size * 1,
      ]);
      var sqlcount = fs
        .readFileSync("app/sql/getUserLawfirmCountKeyword.sql")
        .toString();
      userlfcount = await runQuery(con, sqlcount, [
        lf_id,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
      ]);
    }
    let count = userlfcount[0]["COUNT(*)"];
    await con.commit();
    return { userlf, count };
  } catch (error) {
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};
export const getClientLawfirm = async (lf_id, page, size = 10, keyword) => {
  let con = await pool.getConnection();

  try {
    await con.beginTransaction();

    let clientlf, userlfcount;
    if (keyword == null) {
      var sql = fs.readFileSync("app/sql/getClientLawfirm.sql").toString();
      clientlf = await runQuery(con, sql, [lf_id, (page - 1) * size, size * 1]);
      let sqlcount = fs
        .readFileSync("app/sql/getClientLawfirmCount.sql")
        .toString();
      userlfcount = await runQuery(con, sqlcount, [lf_id]);
    } else {
      var sql = fs
        .readFileSync("app/sql/getClientLawfirmKeyword.sql")
        .toString();
      clientlf = await runQuery(con, sql, [
        lf_id,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        (page - 1) * size,
        size * 1,
      ]);
      let sqlcount = fs
        .readFileSync("app/sql/getClientLawfirmCountKeyword.sql")
        .toString();
      userlfcount = await runQuery(con, sqlcount, [
        lf_id,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
      ]);
    }

    let count = userlfcount[0]["COUNT(*)"];
    await con.commit();
    return { clientlf, count };
  } catch (error) {
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};
export const getListAdmin = async (page, size = 10, keyword) => {
  let con = await pool.getConnection();
  let users, count;
  try {
    await con.beginTransaction();
    if (keyword == null) {
      var sql = fs.readFileSync("app/sql/getListAdmin.sql").toString();
      users = await runQuery(con, sql, [1, (page - 1) * size, size * 1]);
      count = await runQuery(
        con,
        "SELECT COUNT(*) COUNT FROM users WHERE role = ?",
        [1]
      );
      await con.commit();
      return [users, count[0]];
    } else {
      var sql = fs.readFileSync("app/sql/getListAdminKeyword.sql").toString();
      users = await runQuery(con, sql, [
        1,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        (page - 1) * size,
        size * 1,
      ]);
      var sql_count = fs
        .readFileSync("app/sql/getListAdminCountKeyword.sql")
        .toString();
      count = await runQuery(con, sql_count, [
        1,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
      ]);
      return [users, count[0]];
    }
  } catch (error) {
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const deleteUser = async (user_id) => {
  let con = await pool.getConnection();

  try {
    await con.beginTransaction();
    let check_owner = await runQuery(
      con,
      "SELECT is_owner FROM users WHERE user_id = ?",
      [user_id]
    );
    if (check_owner.length == 0)
      throw ErrorHandler.badRequestError("User not found");
    else if (check_owner[0].is_owner == 1)
      throw ErrorHandler.badRequestError("Cannot delete owner");

    var sql = fs.readFileSync("app/sql/deleteUser.sql").toString();
    let user = await runQuery(con, sql, [user_id]);
    await con.commit();
    return user;
  } catch (error) {
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};
export const findUserInLawfirm = async (email, lf_id) => {
  let con = await pool.getConnection();

  try {
    await con.beginTransaction();

    var sql = fs.readFileSync("app/sql/findUserInLawfirm.sql").toString();
    let user = await runQuery(con, sql, [email, lf_id]);
    if (user.length > 0) {
      await con.commit();
      return user;
    } else {
      return [];
    }
  } catch (error) {
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};
export const findUserLawfirm = async (email) => {
  let con = await pool.getConnection();

  try {
    await con.beginTransaction();

    var sql = fs.readFileSync("app/sql/findUserLawfirm.sql").toString();
    let user = await runQuery(con, sql, [email]);
    if (user.length > 0) {
      await con.commit();
      return user;
    } else {
      return [];
    }
  } catch (error) {
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const findUser = async (email) => {
  let con = await pool.getConnection();

  try {
    await con.beginTransaction();

    var sql = fs.readFileSync("app/sql/findUser.sql").toString();
    let user = await runQuery(con, sql, [email]);
    if (user.length > 0) {
      await con.commit();
      return user;
    } else {
      return [];
    }
  } catch (error) {
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

//find user by id
export const findUserById = async (user_id) => {
  let con = await pool.getConnection();

  try {
    await con.beginTransaction();

    var sql = fs.readFileSync("app/sql/findUserById.sql").toString();
    let user = await runQuery(con, sql, [user_id]);
    if (user.length > 0) {
      await con.commit();
      return user;
    } else {
      return [];
    }
  } catch (error) {
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

//add user lawfirm
export const addUser = async (
  org_type,
  org_name,
  email,
  password,
  role,
  status,
  lf_id,
  title,
  first_name,
  middle_name,
  last_name,
  dob = null,
  gender = null,
  home_phone,
  mb_phone,
  wrk_phone,
  adr_1,
  adr_2,
  adr_3,
  state,
  town,
  country,
  post_code,
  user_id,
  pms_id
) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();

    // Check for email uniqueness within the law firm
    let check_email = await runQuery(
      con,
      "SELECT COUNT(*) COUNT FROM users WHERE email = ? AND lf_id = ?",
      [email, lf_id]
    );
    if (check_email[0].COUNT != 0) {
      throw ErrorHandler.badRequestError("A user with this email already exists in this law firm");
    }

    if (pms_id) {
      let check_pms_id = await runQuery(
        con,
        "SELECT COUNT(*) COUNT FROM users WHERE pms_id = ?",
        [pms_id]
      );
      if (check_pms_id[0].COUNT != 0) {
        throw ErrorHandler.badRequestError("PMS ID already exists");
      }
    }
    //start Transaction
    var sqlAddUser = fs.readFileSync("app/sql/addUser.sql").toString();
    let pwd = await passwordHashing(password);
    let addUserResult = await runQuery(con, sqlAddUser, [
      email,
      pwd,
      role,
      status,
      user_id,
      lf_id,
      0,
      pms_id,
    ]);
    //get user id
    let userID = addUserResult.insertId;
    var sqlAddUserInfo = fs.readFileSync("app/sql/addUserInfo.sql").toString();
    let addUserInfoResult = await runQuery(con, sqlAddUserInfo, [
      org_type,
      org_name,
      title,
      first_name,
      middle_name,
      last_name,
      dob,
      gender,
      home_phone,
      mb_phone,
      wrk_phone,
      adr_1,
      adr_2,
      adr_3,
      state,
      town,
      country,
      post_code,
      userID,
    ]);
    // throw new Error("ahihi");
    await con.commit();
    return {
      user_id: addUserResult.insertId,
      addUserResult,
      addUserInfoResult
    };
  } catch (error) {
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const updateUser = async (
  new_email,
  title,
  org_type,
  org_name,
  first_name,
  middle_name,
  last_name,
  dob = null,
  gender = null,
  home_phone,
  mb_phone,
  wrk_phone,
  adr_1,
  adr_2,
  adr_3,
  state,
  town,
  country,
  post_code,
  user_id,
  role,
  pms_id
) => {
  let con = await pool.getConnection();
  if (pms_id) {
    let check_pms_id = await runQuery(
      con,
      "SELECT COUNT(*) COUNT FROM users WHERE pms_id = ?",
      [pms_id]
    );
    if (check_pms_id[0].COUNT != 0) {
      throw ErrorHandler.badRequestError("PMS ID already exists");
    }
  }

  try {
    await con.beginTransaction();
    let user = await runQuery(
      con,
      "UPDATE users SET email = ?, role = ?, pms_id = ? WHERE user_id = ?;",
      [new_email, role, pms_id, user_id]
    );

    var sql = fs.readFileSync("app/sql/updateUser.sql").toString();
    let user_info = await runQuery(con, sql, [
      title,
      org_type,
      org_name,
      first_name,
      middle_name,
      last_name,
      dob,
      gender,
      home_phone,
      mb_phone,
      wrk_phone,
      adr_1,
      adr_2,
      adr_3,
      state,
      town,
      country,
      post_code,
      user_id,
    ]);
    if (user.affectedRows > 0) {
      await con.commit();
      return { user_info };
    } else {
      throw ErrorHandler.badRequestError("No user found");
    }
  } catch (error) {
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const updateUserStatus = async (status, email, user_id) => {
  let con = await pool.getConnection();

  try {
    await con.beginTransaction();

    var updateStatusSql = fs
      .readFileSync("app/sql/updateUserStatus.sql")
      .toString();
    let cur_id = await runQuery(
      con,
      "SELECT user_id FROM users WHERE email = ?",
      [email]
    );
    let updateStatus = await runQuery(con, updateStatusSql, [
      status,
      cur_id[0].user_id,
      user_id,
    ]);

    if (updateStatus.affectedRows > 0) {
      await con.commit();
      return { updateStatus };
    } else {
      throw ErrorHandler.badRequestError("No user found");
    }
  } catch (error) {
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

// try {
//   var sql = fs.readFileSync("app/sql/addUser.sql").toString();
//   let pwd = await passwordHashing(password);
//   let result = await runQuery(sql, [email, pwd]);
//   return result;
// } catch (error) {
//   hookMessage(error.message);
//   throw ErrorHandler.badRequestError(error.message);
// }

export const findLawfirmById = async (lf_id) => {
  let con = await pool.getConnection();

  try {
    await con.beginTransaction();

    var sql = fs.readFileSync("app/sql/findLawfirmById.sql").toString();
    let lawfirm = await runQuery(con, sql, [lf_id]);
    if (lawfirm.length > 0) {
      await con.commit();
      return lawfirm;
    } else {
      return [];
    }
  } catch (error) {
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const findLawfirm = async (lf_name) => {
  let con = await pool.getConnection();

  try {
    await con.beginTransaction();

    var sql = fs.readFileSync("app/sql/findLawfirm.sql").toString();
    let lawfirm = await runQuery(con, sql, [lf_name]);
    if (lawfirm.length > 0) {
      await con.commit();
      return lawfirm;
    } else {
      return [];
    }
  } catch (error) {
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const addUserLawFirm = async (
  email,
  password,
  role,
  status,
  title,
  org_type,
  org_name,
  lf_name,
  first_name,
  middle_name,
  last_name,
  dob = null,
  gender = null,
  home_phone,
  mb_phone,
  wrk_phone,
  adr_1,
  adr_2,
  adr_3,
  state,
  town,
  country,
  post_code,
  user_id
) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    //add law firm

    var sqlAddUserLawFirm = fs
      .readFileSync("app/sql/addLawFirm.sql")
      .toString();
    let addUserLawFirmResult = await runQuery(con, sqlAddUserLawFirm, [
      lf_name,
    ]);
    let lfId = addUserLawFirmResult.insertId;

    //add user
    var sqlAddUser = fs.readFileSync("app/sql/addUser.sql").toString();
    let pwd = await passwordHashing(password);
    let addUserResult = await runQuery(con, sqlAddUser, [
      email,
      pwd,
      role,
      status,
      user_id,
      lfId,
      1,
      null,
    ]);
    //add user info
    let userID = addUserResult.insertId;
    var sqlAddUserInfo = fs.readFileSync("app/sql/addUserInfo.sql").toString();
    let addUserInfoResult = await runQuery(con, sqlAddUserInfo, [
      org_type,
      org_name,
      title,
      first_name,
      middle_name,
      last_name,
      // Date.parse(dob),
      dob,
      gender,
      home_phone,
      mb_phone,
      wrk_phone,
      adr_1,
      adr_2,
      adr_3,
      state,
      town,
      country,
      post_code,
      userID,
    ]);
    await con.commit();
    return {
      user_id: addUserResult.insertId,
      lf_id: addUserLawFirmResult.insertId,
      addUserResult,
      addUserInfoResult,
      addUserLawFirmResult
    };
    // }

    // throw new Error("ahihi");
  } catch (error) {
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const updateLawFirm = async (
  email,
  title,
  lf_name,
  first_name,
  middle_name,
  last_name,
  dob = null,
  gender = null,
  home_phone,
  mb_phone,
  wrk_phone,
  adr_1,
  adr_2,
  adr_3,
  state,
  town,
  country,
  post_code,
  color,
  font,
  lf_id,
  user_id,
  pms_id
) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    var sql = fs.readFileSync("app/sql/updateLawFirm.sql").toString();
    let lawfirm = await runQuery(con, sql, [lf_name, color, font, lf_id]);
    var sql = fs.readFileSync("app/sql/updateUser.sql").toString();
    let user = await runQuery(
      con,
      "UPDATE users SET email = ?, pms_id = ? WHERE user_id = ?;",
      [email, pms_id, user_id]
    );
    let user_info = await runQuery(con, sql, [
      title,
      null,
      null,
      first_name,
      middle_name,
      last_name,
      dob,
      gender,
      home_phone,
      mb_phone,
      wrk_phone,
      adr_1,
      adr_2,
      adr_3,
      state,
      town,
      country,
      post_code,
      user_id,
    ]);
    if (lawfirm.affectedRows > 0 && user.affectedRows > 0) {
      await con.commit();
      return lawfirm;
    } else {
      throw ErrorHandler.badRequestError("No lawfirm found");
    }
  } catch (error) {
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

//-----------------LOGIN-----------------
const getTimezone = (countryId) => {
  const timezones = ct.getTimezonesForCountry(countryId);
  if (timezones.length > 0) {
    return timezones.map((tz) => tz.name);
  } else {
    return ["Timezone not found for this country"];
  }
};

export const displayTimeInTimezone = (countryId) => {
  const timezones = getTimezone(countryId);
  // console.log(`Timezones for ${countryId}:`);
  timezones.forEach((tz) => {
    process.env.TZ = tz;
    console.log(`${tz}: ${moment().tz(tz).format("YYYY-MM-DD HH:mm:ss")}`);
  });
};

export const convertCountryNameToISOCode = (countryName) => {
  const countryData = Object.values(
    countryCodes.customList("countryNameEn", "{countryCode} {countryNameEn}")
  ).find((country) => country.includes(countryName));

  if (countryData) {
    // Extract the country code
    const [countryCode] = countryData.split(" ");
    return countryCode;
  } else {
    return "Country not found";
  }
};

export const getCountry = async (email) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    var sql = fs.readFileSync("app/sql/getCountry.sql").toString();
    let country = await runQuery(con, sql, [email]);
    if (country) {
      return country[0].country;
    }
  } catch (error) {
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

// export const login = async (email, password) => {
//   let con = await pool.getConnection();
//   // let iso = displayTimeInTimezone(convertCountryNameToISOCode("Japan"));
//   try {
//     await con.beginTransaction();

//     var sql = fs.readFileSync("app/sql/getUser.sql").toString();
//     let pwd = await passwordHashing(password);
//     let user = await runQuery(con, sql, [email, pwd]);
//     await con.commit();
//     if (user.length > 0 && !user[0].status) {
//       throw ErrorHandler.badRequestError("User disabled");
//     } else if (user.length > 0) {
//       let access_token = generateToken(
//         user[0].user_id,
//         user[0].email,
//         user[0].role,
//         user[0].lf_id,
//         process.env.ACCESS_TOKEN_SECRET,
//         process.env.ACCESS_TOKEN_EXPIRY_TIME
//       );
//       let refresh_token = generateToken(
//         user[0].user_id,
//         user[0].email,
//         user[0].role,
//         user[0].lf_id,
//         process.env.JWT_SECRET,
//         process.env.JWT_EXPIRY_TIME
//       );
//       await runQuery(
//         con,
//         "INSERT INTO token (refresh_token, access_token, created_at) VALUES (?, ?, NOW())",
//         [refresh_token, access_token]
//       );
//       let lawfirm = await getLawfirmDetails(user[0].lf_id);
//       if (lawfirm.length > 0)
//         return {
//           access_token: access_token,
//           refresh_token: refresh_token,
//           email: user[0].email,
//           role: user[0].role,
//           lf_id: user[0].lf_id,
//           requirePasswordChange: user[0].requirePasswordChange,
//           path: lawfirm[0].path,
//           lf_color: lawfirm[0].lf_color,
//           font: lawfirm[0].font,
//         };
//       else
//         return {
//           access_token: access_token,
//           refresh_token: refresh_token,
//           email: user[0].email,
//           role: user[0].role,
//           lf_id: user[0].lf_id,
//           requirePasswordChange: user[0].requirePasswordChange,
//         };
//     } else {
//       // hookMessage("No user found");
//       throw ErrorHandler.badRequestError(
//         "Please check your email and password again"
//       );
//     }
//   } catch (error) {
//     await con.rollback();
//     throw ErrorHandler.badRequestError(error.message);
//   } finally {
//     con.destroy();
//   }
// };

export const login = async (email, password) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    let pwd = await passwordHashing(password);
    let user = await runQuery(
      con,
      "SELECT user_id, status,lf_id from users where email = ? and password = ? and role <> 4",
      [email, pwd]
    );
    await con.commit();
    if (user.length == 1 && !user[0].status) {
      throw ErrorHandler.badRequestError("User disabled");
    } else if (user.length == 1) {
      let law_firm = await runQuery(
        con,
        "SELECT prefix from law_firm where lf_id = ?",
        [user[0].lf_id]
      );
      let temp_token = generateTempTokenLogin(user[0].user_id);
      return {
        checkUser: user.length,
        token: temp_token,
        prefix: law_firm[0]?.prefix || null,
      };
    } else {
      return 0;
    }
  } catch (error) {
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const refreshToken = async (
  old_refresh_token,
  old_access_token,
  user_id
) => {
  let con = await pool.getConnection();
  let access_token, refresh_token;
  try {
    await con.beginTransaction();
    let ans = await runQuery(
      con,
      "SELECT access_token,refresh_token FROM token WHERE user_id = ?",
      [user_id]
    );
    if (ans.length == 0) {
      throw ErrorHandler.badRequestError("Session has expired");
    } else if (
      ans[0].refresh_token == old_refresh_token &&
      ans[0].access_token == old_access_token
    ) {
      let decoded = jwt.verify(old_refresh_token, process.env.JWT_SECRET);
      access_token = generateToken(
        decoded.user_id,
        decoded.email,
        decoded.role,
        decoded.lf_id,
        process.env.ACCESS_TOKEN_SECRET,
        process.env.ACCESS_TOKEN_EXPIRY_TIME
      );
      refresh_token = generateToken(
        decoded.user_id,
        decoded.email,
        decoded.role,
        decoded.lf_id,
        process.env.JWT_SECRET,
        process.env.JWT_EXPIRY_TIME
      );
      await runQuery(
        con,
        "UPDATE token SET access_token = ?, refresh_token = ? WHERE user_id = ?",
        [access_token, refresh_token, user_id]
      );
      await con.commit();
      return { access_token, refresh_token };
    } else {
      await runQuery(con, "DELETE FROM `token` WHERE user_id = ?", [user_id]);
      await con.commit();
      throw ErrorHandler.badRequestError("Session has expired");
    }
  } catch (error) {
    await con.rollback();
    if (error.name === "TokenExpiredError") {
      throw ErrorHandler.badRequestError("Session has expired");
    } else {
      throw ErrorHandler.badRequestError(error.message);
    }
  } finally {
    con.destroy();
  }
};

/**
 * Login user with email/password for API token authentication
 * Returns user details if credentials are valid
 */
export const loginUser = async (email, password, lf_id = null) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();

    // Hash the password for comparison
    let pwd = await passwordHashing(password);

    // Build query with optional law firm restriction
    let sql = `
      SELECT
        u.user_id, u.email, u.status, u.role, u.lf_id, u.requirePasswordChange,
        ui.first_name, ui.middle_name, ui.last_name, ui.org_type, ui.org_name,
        ui.title, ui.mb_phone, ui.wrk_phone, ui.home_phone
      FROM users u
      JOIN user_info ui ON u.user_id = ui.user_id
      WHERE u.email = ? AND u.password = ?
    `;

    let params = [email, pwd];

    // Add law firm restriction if provided
    if (lf_id !== null) {
      sql += " AND u.lf_id = ?";
      params.push(lf_id);
    }

    let user = await runQuery(con, sql, params);

    await con.commit();

    if (user.length === 0) {
      return {
        success: false,
        message: "Invalid email or password"
      };
    }

    const userData = user[0];

    // Check if user is disabled
    if (!userData.status || userData.status === 0) {
      return {
        success: false,
        message: "User account is disabled"
      };
    }

    // Check if user is in inactive status (status 3 might be pending)
    if (userData.status !== 1 && userData.status !== 3) {
      return {
        success: false,
        message: "User account is not active"
      };
    }

    return {
      success: true,
      user: {
        user_id: userData.user_id,
        email: userData.email,
        role: userData.role,
        lf_id: userData.lf_id,
        status: userData.status,
        first_name: userData.first_name,
        middle_name: userData.middle_name,
        last_name: userData.last_name,
        org_type: userData.org_type,
        org_name: userData.org_name,
        title: userData.title,
        phone: userData.mb_phone || userData.wrk_phone || userData.home_phone,
        requirePasswordChange: userData.requirePasswordChange
      }
    };

  } catch (error) {
    await con.rollback();
    return {
      success: false,
      message: "Login failed: " + error.message
    };
  } finally {
    con.destroy();
  }
};

export const changePassword = async (email, old_password, new_password) => {
  let con = await pool.getConnection();

  try {
    await con.beginTransaction();

    var sql = fs.readFileSync("app/sql/getUser.sql").toString();
    let pwd = await passwordHashing(old_password);
    let user = await runQuery(con, sql, [email, pwd]);
    if (user.length > 0) {
      let new_pwd = await passwordHashing(new_password);
      await runQuery(
        con,
        "UPDATE users SET password = ? , requirePasswordChange = 0, status = 1 WHERE email = ?;",
        [new_pwd, email]
      );
      if (user.status == 3 && user.is_owner == 1) {
        await runQuery(con, "UPDATE law_firm SET status = 1 WHERE lf_id = ?;", [
          user.lf_id,
        ]);
      }
      await con.commit();
    } else {
      // hookMessage("No user found");
      throw ErrorHandler.badRequestError("Incorrect old password");
    }
  } catch (error) {
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};
export const resetPassword = async (email, new_password) => {
  let con = await pool.getConnection();

  try {
    await con.beginTransaction();

    let new_pwd = await passwordHashing(new_password);
    let user = await runQuery(
      con,
      "UPDATE users SET password = ? , requirePasswordChange = 0 WHERE email = ?;",
      [new_pwd, email]
    );
    await con.commit();
  } catch (error) {
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};
export const uploadBanner = async (banner, color, font, lf_id) => {
  let con = await pool.getConnection();

  try {
    await con.beginTransaction();

    if (banner != null) {
      const buffer = Buffer.from(banner, "base64");
      const { sync } = new S3SyncClient({ client: s3Client });
      let path = `static/images/${lf_id}.jpg`;
      fs.writeFileSync(path, buffer);
      await sync("static", process.env.S3_PATH);
      let lawfirm = await runQuery(
        con,
        "UPDATE law_firm SET path = ? WHERE lf_id = ? ",
        [path, lf_id]
      );
      if (lawfirm.affectedRows == 0) {
        throw ErrorHandler.badRequestError("No lawfirm found");
      }
    }
    if (color != null) {
      let lawfirm = await runQuery(
        con,
        "UPDATE law_firm SET lf_color = ? WHERE lf_id = ? ",
        [color, lf_id]
      );
      if (lawfirm.affectedRows == 0) {
        throw ErrorHandler.badRequestError("No lawfirm found");
      }
    }
    if (font != null) {
      let lawfirm = await runQuery(
        con,
        "UPDATE law_firm SET font = ? WHERE lf_id = ? ",
        [font, lf_id]
      );
      if (lawfirm.affectedRows == 0) {
        throw ErrorHandler.badRequestError("No lawfirm found");
      }
    }
    await con.commit();
    return 1;
  } catch (error) {
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const logout = async (user_id) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    let check = await runQuery(
      con,
      "SELECT COUNT(*) COUNT FROM token WHERE user_id = ?",
      [user_id]
    );
    if (check[0].COUNT > 0) {
      let logout = await runQuery(con, "DELETE FROM token WHERE user_id = ?", [
        user_id,
      ]);
      await con.commit();
    }
    return 1;
  } catch (error) {
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const resendInvitation = async (user_id) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    let getInfo = await runQuery(
      con,
      "SELECT * FROM users u JOIN user_info ui on u.user_id = ui.user_id WHERE u.user_id = ?",
      [user_id]
    );
    if (getInfo.length == 0) {
      return 0;
    }
    if (getInfo[0].requirePasswordChange == 0) {
      return 1;
    } else {
      let password = generator.generate({
        length: 15,
        numbers: true,
        uppercase: true,
        lowercase: true,
        symbols: true,
        strict: true,
      });

      let pwd = await passwordHashing(password);
      await runQuery(con, "UPDATE users SET password = ? WHERE user_id = ?", [
        pwd,
        user_id,
      ]);
      await con.commit();
      await mailService.sendTempPassword(
        getInfo[0].email,
        password,
        getInfo[0].first_name
      );
      return 3;
    }
  } catch (error) {
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const getLawfirmSettings = async (prefix) => {
  let con = await pool.getConnection();
  try {
    let settings = await runQuery(
      con,
      "SELECT lf_org_name,lf_color,path,font FROM law_firm WHERE prefix = ?",
      [prefix]
    );
    return settings;
  } catch (error) {
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const saveClientFromPMS = async (
  org_type,
  org_name,
  email,
  password,
  lf_id,
  title,
  first_name,
  middle_name,
  last_name,
  dob = null,
  gender = null,
  home_phone,
  mb_phone,
  wrk_phone,
  adr_1,
  adr_2,
  adr_3,
  state,
  town,
  country,
  post_code,
  pms_id
) => {
  let con = await pool.getConnection();

  try {
    await con.beginTransaction();

    // Check for email uniqueness within the law firm
    let check_email = await runQuery(
      con,
      "SELECT COUNT(*) COUNT FROM users WHERE email = ? AND lf_id = ?",
      [email, lf_id]
    );
    if (check_email[0].COUNT != 0) {
      throw ErrorHandler.badRequestError("A user with this email already exists in this law firm");
    }

    //start Transaction
    var sqlAddUser = fs.readFileSync("app/sql/addUserFromPMS.sql").toString();
    let pwd = await passwordHashing(password);
    let addUserResult = await runQuery(con, sqlAddUser, [
      email,
      pwd,
      4,
      1,
      lf_id,
      0,
      pms_id,
    ]);
    //get user id
    let userID = addUserResult.insertId;
    var sqlAddUserInfo = fs.readFileSync("app/sql/addUserInfo.sql").toString();
    let addUserInfoResult = await runQuery(con, sqlAddUserInfo, [
      org_type,
      org_name,
      title,
      first_name,
      middle_name,
      last_name,
      dob,
      gender,
      home_phone,
      mb_phone,
      wrk_phone,
      adr_1,
      adr_2,
      adr_3,
      state,
      town,
      country,
      post_code,
      userID,
    ]);
    await con.commit();
    return {
      user_id: addUserResult.insertId,
      addUserResult,
      addUserInfoResult
    };
  } catch (error) {
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const createKeyForLawFirm = async (lf_id) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();

    let lf = await runQuery(
      con,
      "SELECT lf_id, `key` FROM law_firm WHERE lf_id = ?",
      [lf_id]
    );

    if (lf.length == 0) {
      throw new Error("No lawfirm found");
    } else if (lf[0].key != null) {
      throw new Error("Key already exists");
    }

    let key = generator.generate({
      length: 15,
      numbers: true,
      uppercase: true,
      lowercase: true,
      strict: true,
    });

    let check_key;
    do {
      check_key = await runQuery(
        con,
        "SELECT COUNT(*) COUNT FROM law_firm WHERE `key` = ?",
        [key]
      );

      if (check_key[0].COUNT > 0) {
        key = generator.generate({
          length: 15,
          numbers: true,
          uppercase: true,
          lowercase: true,
          strict: true,
        });
      }
    } while (check_key[0].COUNT > 0);

    await runQuery(con, "UPDATE law_firm SET `key` = ? WHERE lf_id = ?", [
      key,
      lf_id,
    ]);

    await con.commit();
    return key;
  } catch (error) {
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const regenKeyForLawFirm = async (lf_id) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    let lf = await runQuery(
      con,
      "SELECT lf_id, `key` FROM law_firm WHERE lf_id = ?",
      [lf_id]
    );

    if (lf.length == 0) {
      throw new Error("No lawfirm found");
    }

    let key = generator.generate({
      length: 15,
      numbers: true,
      uppercase: true,
      lowercase: true,
      strict: true,
    });

    let check_key;
    do {
      check_key = await runQuery(
        con,
        "SELECT COUNT(*) COUNT FROM law_firm WHERE `key` = ?",
        [key]
      );

      if (check_key[0].COUNT > 0) {
        key = generator.generate({
          length: 15,
          numbers: true,
          uppercase: true,
          lowercase: true,
          strict: true,
        });
      }
    } while (check_key[0].COUNT > 0);

    await runQuery(con, "UPDATE law_firm SET `key` = ? WHERE lf_id = ?", [
      key,
      lf_id,
    ]);

    await con.commit();
    return key;
  } catch (error) {
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const tokenForAddClientPMS = async (key) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    let data = await runQuery(con, "SELECT * FROM law_firm WHERE `key` = ?", [
      key,
    ]);
    console.log(data);

    if (data.length == 0) {
      return 0;
    }
    let token = jwt.sign(
      { lf_id: data[0].lf_id, key: key },
      process.env.JWT_SECRET,
      {
        expiresIn: "30m",
      }
    );
    await con.commit();
    return token;
  } catch (error) {
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const updateProfileAdmin = async (
  email,
  title,
  first_name,
  middle_name,
  last_name,
  mb_phone,
  country,
  user_id
) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    let check_email = await runQuery(
      con,
      "SELECT COUNT(*) COUNT FROM users WHERE email = ? AND user_id <> ?",
      [email, user_id]
    );
    if (check_email[0].COUNT > 0) {
      throw ErrorHandler.badRequestError("Email already exists");
    }

    let user = await runQuery(
      con,
      "UPDATE users SET email = ? WHERE user_id = ?",
      [email, user_id]
    );
    let user_info = await runQuery(
      con,
      "UPDATE user_info SET title = ?, first_name = ?, middle_name = ?, last_name = ?, mb_phone = ?, country = ? WHERE user_id = ?",
      [title, first_name, middle_name, last_name, mb_phone, country, user_id]
    );

    if (user.affectedRows > 0 && user_info.affectedRows > 0) {
      await con.commit();
      return { user, user_info };
    } else {
      throw ErrorHandler.badRequestError("Error updating profile");
    }
  } catch (error) {
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const updateStatusConnectPMS = async (lf_id) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    if ((await checkConnectPMS(lf_id)) == 1) {
      throw ErrorHandler.badRequestError("Lawfirm already connected to PMS");
    }
    let lawfirm = await runQuery(
      con,
      "UPDATE law_firm SET isConnected = 1 WHERE lf_id = ?",
      [lf_id]
    );
    await con.commit();
    return lawfirm;
  } catch (error) {
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const updateDeletionTime = async (lf_id, time) => {
  let con = await pool.getConnection();
  try {
    console.log(lf_id, time)
    await con.beginTransaction();
    let lawfirm = await runQuery(
      con,
      "UPDATE law_firm SET deleteTime = ? WHERE lf_id = ?",
      [time, lf_id]
    );
    await con.commit();
    return lawfirm;
  } catch (error) {
    await con.rollback();
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
}
export const getDeletionTime = async (lf_id) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    let lawfirm = await runQuery(
      con,
      "SELECT deleteTime FROM law_firm WHERE lf_id = ?",
      [lf_id]
    );
    await con.commit();
    return lawfirm;
  } catch (error) {
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
}