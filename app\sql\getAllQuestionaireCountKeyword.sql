SELECT COUNT(*) 
FROM questionaires q
JOIN `status` s 
ON s.id = q.status
JOIN law_firm l
ON l.lf_id = q.lf_id
JOIN templates t
ON t.id = q.tem_id
WHERE 1=1 
AND q.status in (?)
AND (
q.qtn_name like ?
OR q.id like ?
OR s.name like ?
OR DATE_FORMAT(q.update_at,"%m %d %Y") like ?
OR l.lf_org_name like ?
OR q.price like ? 
OR (DATE_FORMAT(q.created_at,"%m %d %Y") like ? and q.update_at is null)
OR t.tem_name like ?
);
