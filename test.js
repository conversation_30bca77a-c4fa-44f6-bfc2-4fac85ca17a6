const sql = require("mssql/msnodesqlv8");
const config = require("../config.js");
const fs = require('fs');
const path = require('path');
const homeDir = require('os').homedir(); // See: https://www.npmjs.com/package/os
const desktopDir = `${homeDir}/Desktop`;
const sanitizeInput = (input) => input ?? '';
const convertToDateTime = (input) => input ? new Date(input) : null;
const truncateInput = (input, maxLength) => input.length > maxLength ? input.substring(0, maxLength) : input;

const executeUpdateStatements = async (statements) => {
    try {
        // Connect to the database
        await sql.connect(config);

        // Execute each statement
        for (const statement of statements) {
            await sql.query(statement);
        }

        console.log('Update statements executed successfully.');
    } catch (err) {
        console.error('Error executing update statements:', err);
    } finally {
        // Close the database connection
        await sql.close();
    }
};
async function getClientData(name, surn, keyword, filter, page) {
    try {
        if(filter == 'all') {
            const filePath = path.join(__dirname, 'getClientFilterAll.sql');
            const query = fs.readFileSync(filePath, 'utf-8');
            if(keyword == undefined){
                keyword=''
            }
            await sql.connect(config);
            const request = new sql.Request();
            request.input('page', sql.VarChar, page);
            request.input('keyword', sql.VarChar, `%${keyword}%`)
            const result = await request.query(query);
            console.log(result);
            return result.recordset;
        } else if (filter == 'name') {
            const filePath = path.join(__dirname, 'getClientFilterName.sql');
            const query = fs.readFileSync(filePath, 'utf-8');

            if(keyword == undefined){
                keyword=''
            }
            
            await sql.connect(config);
            const request = new sql.Request();
            request.input('name', sql.VarChar, name);
            request.input('page', sql.VarChar, page);
            request.input('surn', sql.VarChar, surn);
            request.input('keyword', sql.VarChar, `%${keyword}%`)
            const result = await request.query(query);
            console.log(result);
            return result.recordset;
        } else if (filter == '7days') {
            const filePath = path.join(__dirname, 'getClientFilter7days.sql');
            const query = fs.readFileSync(filePath, 'utf-8');

            if(keyword == undefined){
                keyword=''
            }
            
            await sql.connect(config);
            const request = new sql.Request();
            request.input('page', sql.VarChar, page);
            request.input('keyword', sql.VarChar, `%${keyword}%`)
            const result = await request.query(query);
            console.log(result);
            return result.recordset;
        } else if (filter == '3months') {
            const filePath = path.join(__dirname, 'getClientFilter3months.sql');
            const query = fs.readFileSync(filePath, 'utf-8');

            if(keyword == undefined){
                keyword=''
            }
            
            await sql.connect(config);
            const request = new sql.Request();
            request.input('page', sql.VarChar, page);
            request.input('keyword', sql.VarChar, `%${keyword}%`)
            const result = await request.query(query);
            console.log(result);
            return result.recordset;
        }

    } catch (err) {
        console.error(err);
        throw err;
    } finally {
        await sql.close();
    }
}

function saveExcel(data, case_id) {
    try{
        const buffer = Buffer.from(data, 'base64');
        const filePath = path.join(desktopDir, 'uploads', `${case_id}.xlsx`);

        let version = 1;
        let filePathWithVersion = filePath;
        const ext = path.extname(filePath);
        const baseName = path.basename(filePath, ext);
        const dirName = path.dirname(filePath);

        while (fs.existsSync(filePathWithVersion)) {
            version += 1;
            filePathWithVersion = path.join(dirName, `${baseName}_${version}${ext}`);
        }
        fs.writeFileSync(filePathWithVersion, buffer)
    }catch(e){
        console.log(e);
    }
}

function saveFile(FileData, CaseName) {
    return new Promise((resolve, reject) => {
        const base64Data = FileData.replace(/^data:([A-Za-z-+/]+);base64,/, '');
        const filePath = path.join(desktopDir, 'uploads', CaseName);

        let version = 1;
        let filePathWithVersion = filePath;
        const ext = path.extname(filePath);
        const baseName = path.basename(filePath, ext);
        const dirName = path.dirname(filePath);

        while (fs.existsSync(filePathWithVersion)) {
            version += 1;
            filePathWithVersion = path.join(dirName, `${baseName}_${version}${ext}`);
        }

        fs.writeFile(filePathWithVersion, base64Data, 'base64', (err) => {
            if (err) {
                reject(err);
            } else {
                resolve();
            }
        });
    });
}
async function getCaseData(case_id){
    const filePath = path.join(__dirname, 'getParties.sql');
    const query = fs.readFileSync(filePath, 'utf-8');
    const filePath2 = path.join(__dirname, 'getCaseName.sql');
    const query2 = fs.readFileSync(filePath2, 'utf-8'); 
    const filePath3 = path.join(__dirname, 'getClient.sql');
    const query3 = fs.readFileSync(filePath3, 'utf-8'); 
    try{
        await sql.connect(config);
        console.log(case_id);
        const request1 = new sql.Request();
        request1.input('caseCode', sql.VarChar, case_id);
        const result1 = await request1.query(query2);
        // console.log(result1)
        const request = new sql.Request();
        request.input('caseCode', sql.VarChar, case_id);
        const result = await request.query(query);
        // console.log(result)
        const request2 = new sql.Request();
        request2.input('caseCode', sql.VarChar, case_id);
        const result2 = await request2.query(query3);
        return {caseName: result1.recordset[0].MTDESC , data:result.recordset, data1:  result2.recordset};
    } catch (err) {
        console.log(err)
        throw err;
    } finally {
        await sql.close();
    }
}async function updateParty(inputs) {
    try{
    let parties = inputs.transformed_data;
    for (let i = 0; i < parties.length; i++) {
        const party = parties[i];
        const partyDetails = addPersonDetails(party);
        console.log(partyDetails)
        const addressParts = [partyDetails.PAADD1, partyDetails.PAADD2, partyDetails.PAADD3, partyDetails.PAADD4, partyDetails.PAADD5].filter(part => part).join(', ') || '';
        const phoneFaxEmailWeb = partyDetails.PAEMAI || partyDetails.PAMOBI || partyDetails.PATEL1 || partyDetails.PATEL2 || '';

        const partyCheckResult = await partyCheck(`${partyDetails.PAFORE} ${partyDetails.PASURN}`,
        partyDetails.JobTitle ? partyDetails.JobTitle: '', 
        partyDetails.Company ? partyDetails.Company: '', 
        addressParts, 
        partyDetails.PAPOST ? partyDetails.PAPOST: '',
        phoneFaxEmailWeb);
        console.log(partyCheckResult)
        if (partyCheckResult.length === 0) {
            await insertParty(partyDetails);
        }
        else {
            partyDetails.PAUNIQ =  partyCheckResult[0].PA_UNIQUE;
            await updatePartyDetails(partyDetails);
        }
    }
    } catch (error) {
        console.log(error);
    }
}
async function partyCheck(partyName, jobTitle, company, addressPart, addressPost, phoneFaxEmailWeb) {
    try {
        await sql.connect(config);
        const request = new sql.Request();

        // Add input parameters
        request.input('PartyName', sql.NVarChar, partyName);
        request.input('JobTitle', sql.NVarChar, jobTitle);
        request.input('Company', sql.NVarChar, company);
        request.input('CategoryID', sql.NVarChar, '');
        request.input('AddressPart', sql.NVarChar, addressPart);
        request.input('AddressPost', sql.NVarChar, addressPost);
        request.input('PhoneFaxEmailWeb', sql.NVarChar, phoneFaxEmailWeb);
        request.input('Notes', sql.NVarChar, '');
        request.input('CaseRole', sql.NVarChar, '');
        request.input('ExpertsOnly', sql.NVarChar, 'N');
        request.input('IncludeInactive', sql.NVarChar, 'Y');
        request.input('PartyType', sql.NVarChar, 'P');

        // Execute the stored procedure
        const result = await request.execute('[dbo].[CUST_PartySearch3]');

        // Return the result
        return result.recordset;
    } catch (error) {
        console.error('Error executing CUST_PartySearch3:', error);
        throw error;
    } finally {
        sql.close();
    }
}
const addPersonDetails = (person) => {
    const { Forename = '', Middlename = '', Surname = '', Title = '' } = person;

    // Create Initials
    const initials = [Forename, ...Middlename.split(' ')]
        .filter(name => name)
        .map(name => name.charAt(0))
        .join(' ');

    // Create FileAs
    const fileAs = `${Surname}, ${Forename}`;

    // Create LetterTitle
    const letterTitle = `${Title} ${initials} ${Surname}`;

    // Create Salutation
    const salutation = `${Title} ${Surname}`;

    return {
        ...person,
        PASURN: person.Surname,
        PAFORE: person.Forename,
        Initials: initials,
        FileAs: fileAs,
        LetterTitle: letterTitle,
        Salutation: salutation,
        PATITL: person.Title,
        PAINIT: initials,
        PALETT: letterTitle,
        PASALU: salutation
    };
};
async function getDataFromPropero(inputs){
    const updateStatements = [];
    for (let i = 0; i < inputs.length; i++) {
        const item = inputs[i];
        console.log(item);
        if (item.type === "non-extensible") {
            const statement = `UPDATE [Libdev].[dbo].[MTL] SET [DATA] = '${item.transformed_data}' WHERE [MTNO] = '${item.mtno}' AND [CODE] = '${item.case_id}';`;
            updateStatements.push(statement);
        }
        if (item.type === "extensible"){
            updateExtensible(item);
        }
        if (item.type === "table"){
            // updateTable(item);
        }
        if (item.type === "party"){
            await updateParty(item);
        }
    }
    console.log(updateStatements)
    executeUpdateStatements(updateStatements)
    // let result1 = await updateClientDetails(inputs)
    return true;
}


async function updatePartyDetails(inputs) {
    try {
        await sql.connect(config);
        const request = new sql.Request();

        // Dynamically build the SQL query
        let updateFields = [];
        let inputParams = [];

        const addField = (field, value, type, maxLength = null) => {
            if (value != null && value !== '') {
                if (maxLength) {
                    value = truncateInput(value, maxLength);
                }
                updateFields.push(`${field} = @${field}`);
                inputParams.push({ name: field, value: value, type: type });
            }
        };

        addField('PASURN', sanitizeInput(inputs.PASURN), sql.NVarChar, 100);
        addField('PAFORE', sanitizeInput(inputs.PAFORE), sql.NVarChar, 100);
        addField('PAFORM', sanitizeInput(inputs.PAFORM), sql.NVarChar, 35);
        addField('PATITL', sanitizeInput(inputs.PATITL), sql.NVarChar, 30);
        addField('PAINIT', sanitizeInput(inputs.PAINIT), sql.NVarChar, 10);
        addField('PALETT', sanitizeInput(inputs.PALETT), sql.NVarChar, 100);
        addField('PACONT', sanitizeInput(inputs.PACONT), sql.NVarChar, 40);
        addField('PASALU', sanitizeInput(inputs.PASALU), sql.NVarChar, 77);
        addField('PAADD1', sanitizeInput(inputs.PAADD1), sql.NVarChar, 200);
        addField('PAADD2', sanitizeInput(inputs.PAADD2), sql.NVarChar, 100);
        addField('PAADD3', sanitizeInput(inputs.PAADD3), sql.NVarChar, 100);
        addField('PAADD4', sanitizeInput(inputs.PAADD4), sql.NVarChar, 100);
        addField('PAADD5', sanitizeInput(inputs.PAADD5), sql.NVarChar, 150);
        addField('PAPOST', sanitizeInput(inputs.PAPOST), sql.NVarChar, 20);
        addField('PATEL1', sanitizeInput(inputs.PATEL1), sql.NVarChar, 100);
        addField('PATEL2', sanitizeInput(inputs.PATEL2), sql.NVarChar, 100);
        addField('PADOBI', convertToDateTime(inputs.PADOBI), sql.DateTime);
        addField('PAEMAI', sanitizeInput(inputs.PAEMAI), sql.NVarChar, 200);
        addField('PAMOBI', sanitizeInput(inputs.PAMOBI), sql.NVarChar, 100);
        addField('PAFAXN', sanitizeInput(inputs.PAFAXN), sql.NVarChar, 100);
        addField('PANINO', sanitizeInput(inputs.PANINO), sql.NVarChar, 15);
        addField('PAMARK', sanitizeInput(inputs.PAMARK), sql.NVarChar, 1);
        addField('PAROFF', sanitizeInput(inputs.PAROFF), sql.NVarChar, 4);
        addField('PACONF', convertToDateTime(inputs.PACONF), sql.DateTime);
        addField('PAADFG', sanitizeInput(inputs.PAADFG), sql.NVarChar, 6);
        addField('PAADESC', sanitizeInput(inputs.PAADESC), sql.NVarChar, 300);
        addField('PACURADD', inputs.PACURADD || 0, sql.Int);
        addField('PAMAIN', inputs.PAMAIN || 0, sql.Int);
        addField('PAJOB', sanitizeInput(inputs.PAJOB), sql.NVarChar, 70);
        addField('PADESC', sanitizeInput(inputs.PADESC), sql.NVarChar, 50);
        addField('PAIMAGEID', inputs.PAIMAGEID || 0, sql.Int);
        addField('PassportNumber', sanitizeInput(inputs.PassportNumber), sql.NVarChar, 50);
        addField('DrivingLicenceNumber', sanitizeInput(inputs.DrivingLicenceNumber), sql.NVarChar, 50);
        addField('IsCommercial', inputs.IsCommercial || false, sql.Bit);
        addField('LaunderingCaseCode', sanitizeInput(inputs.LaunderingCaseCode), sql.NVarChar, 10);
        addField('LinkToMarketing', inputs.LinkToMarketing || false, sql.Bit);
        addField('MarketingCaseCode', sanitizeInput(inputs.MarketingCaseCode), sql.NVarChar, 10);
        addField('Note1', sanitizeInput(inputs.Note1), sql.NVarChar, 70);
        addField('Note2', sanitizeInput(inputs.Note2), sql.NVarChar, 70);
        addField('Note3', sanitizeInput(inputs.Note3), sql.NVarChar, 70);
        addField('Note4', sanitizeInput(inputs.Note4), sql.NVarChar, 70);
        addField('Note5', sanitizeInput(inputs.Note5), sql.NVarChar, 70);
        addField('Note6', sanitizeInput(inputs.Note6), sql.NVarChar, 70);
        addField('IsRepeater', inputs.IsRepeater || false, sql.Bit);
        addField('ROSalutation', sanitizeInput(inputs.ROSalutation), sql.NVarChar, 35);
        addField('ROLetterTitle', sanitizeInput(inputs.ROLetterTitle), sql.NVarChar, 100);
        addField('DateOfDeath', convertToDateTime(inputs.DateOfDeath), sql.DateTime);
        addField('PADEATH', convertToDateTime(inputs.PADEATH), sql.DateTime);
        addField('InformalSalutation', sanitizeInput(inputs.InformalSalutation), sql.NVarChar, 35);
        addField('TradingAs', sanitizeInput(inputs.TradingAs), sql.NVarChar, 100);
        addField('IsDeceased', inputs.IsDeceased || false, sql.Bit);
        addField('WEBSITE', sanitizeInput(inputs.WEBSITE), sql.NVarChar, 100);
        addField('Nationality', sanitizeInput(inputs.Nationality), sql.NVarChar, 100);
        addField('CompanyNumber', sanitizeInput(inputs.CompanyNumber), sql.NVarChar, 60);
        addField('OptOutOfMarketingDate', convertToDateTime(inputs.OptOutOfMarketingDate), sql.DateTime);
        addField('IsInactive', inputs.IsInactive || false, sql.Bit);
        addField('JPLinkID', inputs.JPLinkID || 0, sql.Int);
        addField('JPSalutation', sanitizeInput(inputs.JPSalutation), sql.NVarChar, 35);
        addField('JPLetterTitle', sanitizeInput(inputs.JPLetterTitle), sql.NVarChar, 100);
        addField('SelfServeUserID', sanitizeInput(inputs.SelfServeUserID), sql.NVarChar, 200);
        addField('PPExpiryDate', convertToDateTime(inputs.PPExpiryDate), sql.DateTime);
        addField('PPIssuingCountry', sanitizeInput(inputs.PPIssuingCountry), sql.NVarChar, 3);
        addField('PPMRZLine2', sanitizeInput(inputs.PPMRZLine2), sql.NVarChar, 45);


        const updateQuery = `UPDATE [dbo].[PA] SET ${updateFields.join(', ')} WHERE PAUNIQ = @PAUNIQ`;

        // Add primary key parameter
        request.input('PAUNIQ', sql.Int, inputs.PAUNIQ);

        // Add dynamic input parameters
        inputParams.forEach(param => {
            request.input(param.name, param.type, param.value);
        });

        // Execute the query
        const result = await request.query(updateQuery);
        const request1 = new sql.Request();

        updateFields = [];
        inputParams = [];
        addField('Type', inputs.Type || 0, sql.Int);
        addField('Surname', sanitizeInput(inputs.Surname), sql.NVarChar, 100);
        addField('Forename', sanitizeInput(inputs.Forename), sql.NVarChar, 100);
        addField('Title', sanitizeInput(inputs.Title), sql.NVarChar, 30);
        addField('Initials', sanitizeInput(inputs.Initials), sql.NVarChar, 10);
        addField('LetterTitle', sanitizeInput(inputs.LetterTitle), sql.NVarChar, 100);
        addField('Salutation', sanitizeInput(inputs.Salutation), sql.NVarChar, 35);
        addField('Middlename', sanitizeInput(inputs.Middlename), sql.NVarChar, 100);
        addField('DateOfBirth', convertToDateTime(inputs.DateOfBirth), sql.DateTime);
        addField('NationalInsurance', sanitizeInput(inputs.NationalInsurance), sql.NVarChar, 15);
        addField('Gender', sanitizeInput(inputs.Gender), sql.NVarChar, 1);
        addField('JobTitle', sanitizeInput(inputs.JobTitle), sql.NVarChar, 70);
        addField('JobDescription', sanitizeInput(inputs.JobDescription), sql.NVarChar, 100);
        addField('ImageID', inputs.ImageID || 0, sql.Int);
        addField('ConflictCheckDate', convertToDateTime(inputs.ConflictCheckDate), sql.DateTime);
        addField('Company', sanitizeInput(inputs.Company), sql.NVarChar, 100);
        addField('Department', sanitizeInput(inputs.Department), sql.NVarChar, 50);
        addField('ManagerName', sanitizeInput(inputs.ManagerName), sql.NVarChar, 50);
        addField('AssistantsName', sanitizeInput(inputs.AssistantsName), sql.NVarChar, 50);
        addField('Office', sanitizeInput(inputs.Office), sql.NVarChar, 50);
        addField('Profession', sanitizeInput(inputs.Profession), sql.NVarChar, 50);
        addField('Nickname', sanitizeInput(inputs.Nickname), sql.NVarChar, 30);
        addField('SpousesName', sanitizeInput(inputs.SpousesName), sql.NVarChar, 50);
        addField('FileAs', sanitizeInput(inputs.FileAs), sql.NVarChar, 100);
        addField('Notes', sanitizeInput(inputs.Notes), sql.NVarChar, 8000);
        addField('CatInfoNumb', inputs.CatInfoNumb || 0, sql.Int);
        addField('ForeignName', sanitizeInput(inputs.ForeignName), sql.NVarChar, 250);
        addField('SkypeID', sanitizeInput(inputs.SkypeID), sql.NVarChar, 32);
        addField('SkypePreference', sanitizeInput(inputs.SkypePreference), sql.NVarChar, 10);
        addField('AppCode', sanitizeInput(inputs.AppCode), sql.NVarChar, 10);
        addField('AppActivated', inputs.AppActivated || false, sql.Bit);
        addField('Device1ID', sanitizeInput(inputs.Device1ID), sql.NVarChar, 50);
        addField('Device2ID', sanitizeInput(inputs.Device2ID), sql.NVarChar, 50);
        const updateQuery1 = `UPDATE [dbo].[Contacts] SET ${updateFields.join(', ')} WHERE PAUNIQ = @PAUNIQ`;
        request1.input('PAUNIQ', sql.Int, inputs.PAUNIQ);

        // Add dynamic input parameters
        inputParams.forEach(param => {
            request1.input(param.name, param.type, param.value);
        });

        // Execute the query
        const result1 = await request1.query(updateQuery1);
        let request3 = await new sql.Request().input('country', sql.VarChar, inputs.Country)
        .query(`
          SELECT TOP (1) [CountryID], [CountryName], [CountryCode], [ISOALPHA3], [UNM49]
          FROM [Libdev].[dbo].[ContactCountries]
          WHERE [CountryName] = @country
        `);
        if (request3.recordset.length > 0) {
            CountryID = request3.recordset[0].CountryID;
          }
          else CountryID = -1;
        request3 =await  new sql.Request().input('county', sql.VarChar, inputs.County)
        .query(`
                      SELECT TOP (1) [CountyID], [CountyName], [CountyCode], [CountryCODE]
          FROM [Libdev].[dbo].[ContactCounties]
          WHERE [CountyName] = @county
          `)
          if (request3.recordset.length > 0) {
            CountyID = request3.recordset[0].CountyID;
          }
          else CountyID = -1;
        return inputs.PAUNIQ;
    } catch (error) {
        console.error('Error updating client details:', error);
        throw error;
    } finally {
        sql.close();
    }
}

const insertParty = async (inputs) => {
    try {
        // Connect to the database
        await sql.connect(config);

        let insertFields = [];
        let insertValues = [];
        let inputParams = [];
        const request1 = new sql.Request();
        const result1 = await request1.query("SELECT MAX(PAUNIQ) + 1 AS PAUNIQ FROM PA;");

        const addField = (field, value, type, maxLength = null) => {
            if (value != null && value !== '') {
                if (maxLength) {
                    value = truncateInput(value, maxLength);
                }
                insertFields.push(`[${field}]`);
                insertValues.push(`@${field}`);
                inputParams.push({ name: field, value: value, type: type });
            }
        };

        // Add fields and values
        addField('PAUNIQ', result1.recordset[0].PAUNIQ, sql.Int);
        addField('PASURN', sanitizeInput(inputs.PASURN), sql.NVarChar, 100);
        addField('PAFORE', sanitizeInput(inputs.PAFORE), sql.NVarChar, 100);
        addField('PAFORM', sanitizeInput(inputs.PAFORM), sql.NVarChar, 35);
        addField('PATITL', sanitizeInput(inputs.PATITL), sql.NVarChar, 30);
        addField('PAINIT', sanitizeInput(inputs.PAINIT), sql.NVarChar, 10);
        addField('PALETT', sanitizeInput(inputs.PALETT), sql.NVarChar, 100);
        addField('PACONT', sanitizeInput(inputs.PACONT), sql.NVarChar, 40);
        addField('PASALU', sanitizeInput(inputs.PASALU), sql.NVarChar, 77);
        addField('PAADD1', sanitizeInput(inputs.PAADD1), sql.NVarChar, 200);
        addField('PAADD2', sanitizeInput(inputs.PAADD2), sql.NVarChar, 100);
        addField('PAADD3', sanitizeInput(inputs.PAADD3), sql.NVarChar, 100);
        addField('PAADD4', sanitizeInput(inputs.PAADD4), sql.NVarChar, 100);
        addField('PAADD5', sanitizeInput(inputs.PAADD5), sql.NVarChar, 150);
        addField('PAPOST', sanitizeInput(inputs.PAPOST), sql.NVarChar, 20);
        addField('PATEL1', sanitizeInput(inputs.PATEL1), sql.NVarChar, 100);
        addField('PATEL2', sanitizeInput(inputs.PATEL2), sql.NVarChar, 100);
        addField('PADOBI', convertToDateTime(inputs.PADOBI), sql.DateTime);
        addField('PAEMAI', sanitizeInput(inputs.PAEMAI), sql.NVarChar, 200);
        addField('PAMOBI', sanitizeInput(inputs.PAMOBI), sql.NVarChar, 100);
        addField('PAFAXN', sanitizeInput(inputs.PAFAXN), sql.NVarChar, 100);
        addField('PANINO', sanitizeInput(inputs.PANINO), sql.NVarChar, 15);
        addField('PAMARK', sanitizeInput(inputs.PAMARK), sql.NVarChar, 1);
        addField('PAROFF', sanitizeInput(inputs.PAROFF), sql.NVarChar, 4);
        addField('PACONF', convertToDateTime(inputs.PACONF), sql.DateTime);
        addField('PAADFG', sanitizeInput(inputs.PAADFG), sql.NVarChar, 6);
        addField('PAADESC', sanitizeInput(inputs.PAADESC), sql.NVarChar, 300);
        addField('PACURADD', inputs.PACURADD || 0, sql.Int);
        addField('PAMAIN', inputs.PAMAIN || 0, sql.Int);
        addField('PAJOB', sanitizeInput(inputs.PAJOB), sql.NVarChar, 70);
        addField('PADESC', sanitizeInput(inputs.PADESC), sql.NVarChar, 50);
        addField('PAIMAGEID', inputs.PAIMAGEID || 0, sql.Int);
        addField('PassportNumber', sanitizeInput(inputs.PassportNumber), sql.NVarChar, 50);
        addField('DrivingLicenceNumber', sanitizeInput(inputs.DrivingLicenceNumber), sql.NVarChar, 50);
        addField('IsCommercial', inputs.IsCommercial || false, sql.Bit);
        addField('LaunderingCaseCode', sanitizeInput(inputs.LaunderingCaseCode), sql.NVarChar, 10);
        addField('LinkToMarketing', inputs.LinkToMarketing || false, sql.Bit);
        addField('MarketingCaseCode', sanitizeInput(inputs.MarketingCaseCode), sql.NVarChar, 10);
        addField('Note1', sanitizeInput(inputs.Note1), sql.NVarChar, 70);
        addField('Note2', sanitizeInput(inputs.Note2), sql.NVarChar, 70);
        addField('Note3', sanitizeInput(inputs.Note3), sql.NVarChar, 70);
        addField('Note4', sanitizeInput(inputs.Note4), sql.NVarChar, 70);
        addField('Note5', sanitizeInput(inputs.Note5), sql.NVarChar, 70);
        addField('Note6', sanitizeInput(inputs.Note6), sql.NVarChar, 70);
        addField('IsRepeater', inputs.IsRepeater || false, sql.Bit);
        addField('ROSalutation', sanitizeInput(inputs.ROSalutation), sql.NVarChar, 35);
        addField('ROLetterTitle', sanitizeInput(inputs.ROLetterTitle), sql.NVarChar, 100);
        addField('DateOfDeath', convertToDateTime(inputs.DateOfDeath), sql.DateTime);
        addField('PADEATH', convertToDateTime(inputs.PADEATH), sql.DateTime);
        addField('InformalSalutation', sanitizeInput(inputs.InformalSalutation), sql.NVarChar, 35);
        addField('TradingAs', sanitizeInput(inputs.TradingAs), sql.NVarChar, 100);
        addField('IsDeceased', inputs.IsDeceased || false, sql.Bit);
        addField('WEBSITE', sanitizeInput(inputs.WEBSITE), sql.NVarChar, 100);
        addField('Nationality', sanitizeInput(inputs.Nationality), sql.NVarChar, 100);
        addField('CompanyNumber', sanitizeInput(inputs.CompanyNumber), sql.NVarChar, 60);
        addField('OptOutOfMarketingDate', convertToDateTime(inputs.OptOutOfMarketingDate), sql.DateTime);
        addField('IsInactive', inputs.IsInactive || false, sql.Bit);
        addField('JPLinkID', inputs.JPLinkID || 0, sql.Int);
        addField('JPSalutation', sanitizeInput(inputs.JPSalutation), sql.NVarChar, 35);
        addField('JPLetterTitle', sanitizeInput(inputs.JPLetterTitle), sql.NVarChar, 100);
        addField('SelfServeUserID', sanitizeInput(inputs.SelfServeUserID), sql.NVarChar, 200);
        addField('PPExpiryDate', convertToDateTime(inputs.PPExpiryDate), sql.DateTime);
        addField('PPIssuingCountry', sanitizeInput(inputs.PPIssuingCountry), sql.NVarChar, 3);
        addField('PPMRZLine2', sanitizeInput(inputs.PPMRZLine2), sql.NVarChar, 45);

        const query = `
            INSERT INTO [Libdev].[dbo].[PA] (
                ${insertFields.join(', ')}
            ) OUTPUT INSERTED.* VALUES (
                ${insertValues.join(', ')}
            );
        `;

        // Create a request and add input parameters
        const request = new sql.Request();
        inputParams.forEach(param => {
            request.input(param.name, param.type, param.value);
        });

        // Execute the insert statement and get the inserted row
        const result = await request.query(query);
        insertFields = [];
        insertValues = [];
        inputParams = [];
        // let contactID = await new sql.Request().query(`SELECT MAX(ContactID) +1 as ContactID from Contacts;`)
        // addField('ContactID',contactID.recordset[0].ContactID,sql.Int)
        addField('Type', inputs.Type || 0, sql.Int);
        addField('Surname', sanitizeInput(inputs.Surname), sql.NVarChar, 100);
        addField('Forename', sanitizeInput(inputs.Forename), sql.NVarChar, 100);
        addField('Title', sanitizeInput(inputs.Title), sql.NVarChar, 30);
        addField('Initials', sanitizeInput(inputs.Initials), sql.NVarChar, 10);
        addField('LetterTitle', sanitizeInput(inputs.LetterTitle), sql.NVarChar, 100);
        addField('Salutation', sanitizeInput(inputs.Salutation), sql.NVarChar, 35);
        addField('Middlename', sanitizeInput(inputs.Middlename), sql.NVarChar, 100);
        addField('DateOfBirth', convertToDateTime(inputs.DateOfBirth), sql.DateTime);
        addField('NationalInsurance', sanitizeInput(inputs.NationalInsurance), sql.NVarChar, 15);
        addField('Gender', sanitizeInput(inputs.Gender), sql.NVarChar, 1);
        addField('JobTitle', sanitizeInput(inputs.JobTitle), sql.NVarChar, 70);
        addField('JobDescription', sanitizeInput(inputs.JobDescription), sql.NVarChar, 100);
        addField('ImageID', inputs.ImageID || 0, sql.Int);
        addField('ConflictCheckDate', convertToDateTime(inputs.ConflictCheckDate), sql.DateTime);
        addField('Company', sanitizeInput(inputs.Company), sql.NVarChar, 100);
        addField('Department', sanitizeInput(inputs.Department), sql.NVarChar, 50);
        addField('ManagerName', sanitizeInput(inputs.ManagerName), sql.NVarChar, 50);
        addField('AssistantsName', sanitizeInput(inputs.AssistantsName), sql.NVarChar, 50);
        addField('Office', sanitizeInput(inputs.Office), sql.NVarChar, 50);
        addField('Profession', sanitizeInput(inputs.Profession), sql.NVarChar, 50);
        addField('Nickname', sanitizeInput(inputs.Nickname), sql.NVarChar, 30);
        addField('SpousesName', sanitizeInput(inputs.SpousesName), sql.NVarChar, 50);
        addField('PAUNIQ', result1.recordset[0].PAUNIQ, sql.Int);
        addField('FileAs', sanitizeInput(inputs.FileAs), sql.NVarChar, 100);
        addField('Notes', sanitizeInput(inputs.Notes), sql.NVarChar, 8000);
        addField('CatInfoNumb', inputs.CatInfoNumb || 0, sql.Int);
        addField('ForeignName', sanitizeInput(inputs.ForeignName), sql.NVarChar, 250);
        addField('SkypeID', sanitizeInput(inputs.SkypeID), sql.NVarChar, 32);
        addField('SkypePreference', sanitizeInput(inputs.SkypePreference), sql.NVarChar, 10);
        addField('AppCode', sanitizeInput(inputs.AppCode), sql.NVarChar, 10);
        addField('AppActivated', inputs.AppActivated || false, sql.Bit);
        addField('Device1ID', sanitizeInput(inputs.Device1ID), sql.NVarChar, 50);
        addField('Device2ID', sanitizeInput(inputs.Device2ID), sql.NVarChar, 50);

        const query2 = `
            INSERT INTO [Libdev].[dbo].[Contacts] (
                ${insertFields.join(', ')}
            ) OUTPUT INSERTED.* VALUES (
                ${insertValues.join(', ')}
            );
        `;

        // Create a request and add input parameters
        const request2 = new sql.Request();
        inputParams.forEach(param => {
            request2.input(param.name, param.type, param.value);
        });
        

        // Execute the insert statement and get the inserted row
        const result2 = await request2.query(query2);
        
        let request3 = await new sql.Request().input('country', sql.VarChar, inputs.Country)
        .query(`
          SELECT TOP (1) [CountryID], [CountryName], [CountryCode], [ISOALPHA3], [UNM49]
          FROM [Libdev].[dbo].[ContactCountries]
          WHERE [CountryName] = @country
        `);
        if (request3.recordset.length > 0) {
            CountryID = request3.recordset[0].CountryID;
          }
          else CountryID = -1;
        request3 = await new sql.Request().input('county', sql.VarChar, inputs.County)
        .query(`
                      SELECT TOP (1) [CountyID], [CountyName], [CountyCode], [CountryCODE]
          FROM [Libdev].[dbo].[ContactCounties]
          WHERE [CountyName] = @county
          `)
          if (request3.recordset.length > 0) {
            CountyID = request3.recordset[0].CountyID;
          }
          else CountyID = -1;
        // let addressId = await new sql.Request().query(`SELECT MAX(AddressID) + 1 AS AddressID FROM [Libdev].[dbo].[Addresses];`)
        insertFields = [];
        insertValues = [];
        inputParams = [];
        // addField('AddressID',addressId.recordset[0].AddressID, sql.Int);
        addField('TypeID', inputs.TypeID, sql.Int);
        addField('SAO', inputs.SAO, sql.VarChar, 100);
        addField('PAO', inputs.PAO, sql.VarChar, 100);
        addField('Street', inputs.Street, sql.VarChar, 100);
        addField('Locality', inputs.Locality, sql.VarChar, 100);
        addField('Town', inputs.Town, sql.VarChar, 100);
        addField('CountyID', CountyID, sql.Int);
        addField('PostCode', inputs.PostCode, sql.VarChar, 20);
        addField('CountryID', CountryID, sql.Int);
        addField('CompanyName', inputs.CompanyName, sql.VarChar, 100);
        addField('DX', inputs.DX, sql.VarChar, 30);
        addField('UseDX', inputs.UseDX, sql.VarChar, 1);
        addField('DXX', inputs.DXX, sql.VarChar, 30);
        addField('IsInactive', inputs.IsInactive, sql.Bit);
        const queryAddress = `
            INSERT INTO [Libdev].[dbo].[Addresses] (
                ${insertFields.join(', ')}
            ) OUTPUT INSERTED.* VALUES (
                ${insertValues.join(', ')}
            );
        `;
        const requestAddress = new sql.Request();
        inputParams.forEach(param => {
            requestAddress.input(param.name, param.type, param.value);
        });

        // Execute the insert statement and get the inserted row
        const resultAdress = await requestAddress.query(queryAddress);
        const contactAddressQuery = `
        INSERT INTO [Libdev].[dbo].[ContactAddresses] (
            [ContactID], [AddressID], [MailingAddress], [Displayed], [RegisteredOffice], [ServiceAddress]
        ) VALUES (
            @ContactID, @AddressID, @MailingAddress, @Displayed, @RegisteredOffice, @ServiceAddress
        );
        `;
        const contactAddressRequest = new sql.Request();
        contactAddressRequest.input('ContactID', sql.Int, result2.recordset[0].ContactID);
        contactAddressRequest.input('AddressID', sql.Int, resultAdress.recordset[0].AddressID);
        contactAddressRequest.input('MailingAddress', sql.VarChar, inputs.MailingAddress ? inputs.MailingAddress : 'Y');
        contactAddressRequest.input('Displayed', sql.Int, inputs.Displayed ? inputs.Displayed : 1 );
        contactAddressRequest.input('RegisteredOffice', sql.Bit, inputs.RegisteredOffice ? inputs.RegisteredOffice :0);
        contactAddressRequest.input('ServiceAddress', sql.Bit, inputs.ServiceAddress ? inputs.ServiceAddress : 0);
        await contactAddressRequest.query(contactAddressQuery);
        if (inputs.PAEMAI){
            await new sql.Request().input('ContactID', sql.Int, result2.recordset[0].ContactID)
            .input('ItemValue',sql.VarChar,inputs.PAEMAI)
            .query(`INSERT INTO [Libdev].[dbo].[ContactItems] ([ContactID] ,[DescriptionID],[Note],[ItemValue],[Displayed])
                VALUES (@ContactID,21,'',@ItemValue,1)`)
        }
        if (inputs.PAMOBI){
            await new sql.Request().input('ContactID', sql.Int, result2.recordset[0].ContactID)
            .input('ItemValue',sql.VarChar,inputs.PAMOBI)
            .query(`INSERT INTO [Libdev].[dbo].[ContactItems] ([ContactID] ,[DescriptionID],[Note],[ItemValue],[Displayed])
                VALUES (@ContactID,10,'',@ItemValue,1)`)
        }
        if (inputs.PATEL1){
            await new sql.Request().input('ContactID', sql.Int, result2.recordset[0].ContactID)
            .input('ItemValue',sql.VarChar,inputs.PATEL1)
            .query(`INSERT INTO [Libdev].[dbo].[ContactItems] ([ContactID] ,[DescriptionID],[Note],[ItemValue],[Displayed])
                VALUES (@ContactID,7,'',@ItemValue,1)`)
        }
        if (inputs.PATEL2){
            await new sql.Request().input('ContactID', sql.Int, result2.recordset[0].ContactID)
            .input('ItemValue',sql.VarChar,inputs.PATEL2)
            .query(`INSERT INTO [Libdev].[dbo].[ContactItems] ([ContactID] ,[DescriptionID],[Note],[ItemValue],[Displayed])
                VALUES (@ContactID,8,'',@ItemValue,1)`)
        }
        return result1.recordset[0].PAUNIQ;
    } catch (err) {
        console.error('Error inserting contact:', err);
    } finally {
        // Close the database connection
        await sql.close();
    }
};


async function getPartiesForAssignQtn(case_id, surname, forename){
    try {
        await sql.connect(config);
        const filePath = path.join(__dirname, 'getPartiesForAssignQtn.sql');
        const query = fs.readFileSync(filePath, 'utf-8');
        const request = new sql.Request()
        request.input('caseCode', sql.VarChar, case_id)
        const result = await request.query(query)
        return result.recordset;
    } catch (err) {
        console.log(err)
        throw err;
    } finally {
        await sql.close();
    }
}
async function updateAnswer(inputs){
    console.log(inputs)
    return;
}

module.exports = {
    getClientData,
    saveFile,
    getCaseData,
    saveExcel,
    getPartiesForAssignQtn,
    getDataFromPropero,
    updateAnswer
};
