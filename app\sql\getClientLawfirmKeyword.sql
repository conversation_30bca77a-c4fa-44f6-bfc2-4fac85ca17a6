SELECT 
	u.user_id
	, ui.first_name 
	, ui.last_name 
	, u.email 
	, ui.country 
	, ui.mb_phone AS phone_number
	, u.role
	, u.status 
	, u.pms_id
FROM users u
JOIN user_info ui 
ON u.user_id  = ui.user_id 
JOIN `status` s 
ON s.id = u.status
WHERE u.lf_id = ? 
AND u.role = 4
AND (ui.first_name like ?
OR ui.last_name like ?
OR u.email like ?
OR ui.country like ?
OR u.user_id like ?
OR s.name like ?
OR COALESCE(NULLIF(ui.mb_phone, ''), NULLIF(ui.wrk_phone, ''), NULLIF(ui.home_phone, '')) like ?
)
ORDER BY u.user_id DESC
LIMIT ?, ?;
