-- Migration: Add sender_email column to graph_credentials table
-- Date: 2025-01-18
-- Description: Adds optional sender_email field for specifying which user to send emails from

-- Add sender_email column to existing table
ALTER TABLE graph_credentials 
ADD COLUMN sender_email VARCHAR(255) NULL 
COMMENT 'Optional: specific email address to send from (must exist in tenant)'
AFTER client_secret;

-- Update the safe view to include the new column
DROP VIEW IF EXISTS graph_credentials_safe;

CREATE VIEW graph_credentials_safe AS
SELECT 
    id,
    lf_id,
    tenant_id,
    client_id,
    sender_email,
    is_active,
    created_at,
    updated_at,
    created_by,
    updated_by
FROM graph_credentials;
