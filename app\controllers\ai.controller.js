import { catchAsync } from '#utils';
import OpenAI from 'openai';

const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY // Move API key to environment variable
});

export const getWebPage = catchAsync(async (req, res) => {
    const HTML = `<!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Form Page</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 20px;
                position: relative;
            }
            .top-buttons {
                display: flex;
                justify-content: flex-end;
                gap: 10px;
            }
            .top-buttons button {
                padding: 10px;
                border: none;
                border-radius: 5px;
                cursor: pointer;
            }
            .red-btn { background-color: red; color: white; }
            .blue-btn { background-color: blue; color: white; }
            .dark-btn { background-color: darkcyan; color: white; }
            
            .tabs {
                display: flex;
                gap: 10px;
                margin-top: 10px;
            }
            .tab {
                background-color: red;
                color: white;
                padding: 10px;
                border-radius: 5px;
            }
            .form-container {
                border: 1px solid #ccc;
                padding: 20px;
                margin-top: 10px;
            }
            .question {
                margin-bottom: 10px;
                display: flex;
                align-items: center;
            }
            .options label {
                display: block;
            }
            .tooltip-icon {
                margin-left: 10px;
                cursor: pointer;
                color: blue;
            }
            .overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                visibility: hidden;
            }
            .overlay-content {
                background: white;
                padding: 20px;
                border-radius: 5px;
                text-align: center;
                max-width: 80vw;
                max-height: 80vh;
                box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.3);
                overflow-y: auto;
            }
            .overlay button {
                margin-top: 10px;
                padding: 10px;
                border: none;
                cursor: pointer;
                background: red;
                color: white;
                border-radius: 5px;
            }
            .overlay-content p, .overlay-content h1, .overlay-content h2, 
            .overlay-content h3, .overlay-content h4, .overlay-content h5, 
            .overlay-content ul, .overlay-content ol, .overlay-content li {
                font-family: Arial, sans-serif;
                color: #333;
                text-align: left;
                margin: 10px 0;
            }
            .overlay-content ul, .overlay-content ol {
                padding-left: 20px;
            }
            .overlay-content li {
                margin-bottom: 5px;
                line-height: 1.5;
            }
        </style>
        <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
        <script>
            function showTooltip(question, details) {
                $.ajax({
                    url: 'https://uat-api.propero.co.uk/public/ai/getToolTip',
                    method: 'GET',
                    data: { question: question, casetype: 'will' },
                    success: function(response) {
                        document.getElementById('overlay-text').innerHTML = response;
                        document.getElementById('overlay').style.visibility = 'visible';
                    },
                    error: function(error) {
                        console.error('Error fetching tooltip info:', error);
                    }
                });
            }
            function closeOverlay() {
                document.getElementById('overlay').style.visibility = 'hidden';
            }
        </script>
    </head>
    <body>
        <div class="top-buttons">
            <button class="blue-btn">Take a Tour</button>
            <button class="red-btn">Call me</button>
            <button class="dark-btn">Save and Exit</button>
            <button class="dark-btn">Save</button>
        </div>
        
        <div class="tabs">
            <div class="tab">Client 1</div>
            <div class="tab">Executors</div>
            <div class="tab">Children</div>
            <div class="tab">Client 2</div>
        </div>
        
        <div class="form-container">
            <p><strong>Please confirm your personal details</strong></p>
            <p><a href="#">Click to answer</a></p>
            
            <div class="question1">
                <p><strong>How would you wish your body to be disposed of?</strong></p>
                <span class="tooltip-icon" onclick="showTooltip('How would you wish your body to be disposed of?', 'Choose between Burial or Cremation')">&#9432;</span>
            </div>
            <div class="options">
                <label><input type="radio" name="disposal" value="burial"> Burial</label>
                <label><input type="radio" name="disposal" value="cremation"> Cremation</label>
            </div>
            
            <div class="question2">
                <p><strong>Do you wish to make any cash gifts?</strong></p>
                <span class="tooltip-icon" onclick="showTooltip('Do you wish to make any cash gifts?', 'Choose Yes or No for cash gifts preference')">&#9432;</span>
            </div>
            <div class="options">
                <label><input type="radio" name="cash_gift" value="yes"> Yes</label>
                <label><input type="radio" name="cash_gift" value="no"> No</label>
            </div>
        </div>
        
        <div id="overlay" class="overlay">
            <div class="overlay-content">
                <p id="overlay-text"></p>
                <button onclick="closeOverlay()">Close</button>
            </div>
        </div>
    </body>
    </html>`;

    return res.send(HTML);
});

export const getWebPageLPA = catchAsync(async (req, res) => {
    const HTML = `<!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>LPA Form</title>
        <script>
            function showSection(sectionId) {
                document.querySelectorAll('.section').forEach(section => {
                    section.style.display = 'none';
                });
                document.getElementById(sectionId).style.display = 'block';
            }
            
            async function sendQuestion(question) {
                let storedResponse = sessionStorage.getItem(question);
                if (storedResponse) {
                    showOverlay(storedResponse);
                    return;
                }
                showLoading();
                try {
                    let response = await fetch('https://uat-api.propero.co.uk/public/ai/getToolTip', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ 
                            question: question, 
                            casetype: "LPA", 
                            extra: "do not reference any information from a will case type. Use the guidance notes in Form LP12 in www.gov.uk for advice on questions about lasting powers of attorney.  For example, a question about when attorneys can act could use the content under the heading 'Section 5'." 
                        })
                    });
                    let data = await response.text();
                    sessionStorage.setItem(question, data);
                    showOverlay(data);
                } catch (error) {
                    console.error('Error:', error);
                } finally {
                    hideLoading();
                }
            }
            
            function showOverlay(message) {
                let overlay = document.getElementById('overlay');
                let overlayText = document.getElementById('overlay-text');
                overlayText.innerHTML = message;
                overlay.style.display = 'block';
                overlay.style.visibility = 'visible';
            }
            
            function closeOverlay() {
                document.getElementById('overlay').style.display = 'none';
            }

            function showLoading() {
                document.getElementById('loading').style.display = 'block';
            }
            
            function hideLoading() {
                document.getElementById('loading').style.display = 'none';
            }
        </script>
        <style>
            body {
                font-family: Arial, sans-serif;
            }
            .top-buttons {
                display: flex;
                justify-content: flex-end;
                gap: 10px;
                margin-bottom: 10px;
            }
            .top-buttons button {
                padding: 5px 10px;
                border: none;
                border-radius: 5px;
                color: white;
                cursor: pointer;
            }
            .tour { background-color: blue; }
            .call { background-color: red; }
            .save-exit { background-color: darkgreen; }
            .save { background-color: darkslategray; }
            .tabs {
                display: flex;
                margin-bottom: 10px;
            }
            .tab {
                padding: 10px;
                cursor: pointer;
                margin-right: 5px;
                border-radius: 5px;
                color: white;
            }
            .tab.completed { background-color: green; }
            .tab.in-progress { background-color: orange; }
            .tab.not-started { background-color: red; }
            .section {
                display: none;
                padding: 10px;
                border: 1px solid #ccc;
                border-radius: 5px;
            }
            .info-icon {
                display: inline-block;
                width: 20px;
                height: 20px;
                border-radius: 50%;
                background-color: #007BFF;
                color: white;
                text-align: center;
                font-weight: bold;
                cursor: pointer;
                line-height: 20px;
                margin-left: 10px;
            }
            .overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                visibility: hidden;
            }
            .overlay-content {
                background: white;
                padding: 20px;
                border-radius: 5px;
                text-align: center;
                max-width: 80vw;
                max-height: 80vh;
                box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.3);
                overflow-y: auto;
                margin: auto;
                margin-top: 5em;
            }
            .overlay button {
                margin-top: 10px;
                padding: 10px;
                border: none;
                cursor: pointer;
                background: red;
                color: white;
                border-radius: 5px;
            }
            .overlay-content p, .overlay-content h1, .overlay-content h2, 
            .overlay-content h3, .overlay-content h4, .overlay-content h5, 
            .overlay-content ul, .overlay-content ol, .overlay-content li {
                font-family: Arial, sans-serif;
                color: #333;
                text-align: left;
                margin: 10px 0;
            }
            .overlay-content ul, .overlay-content ol {
                padding-left: 20px;
            }
            .overlay-content li {
                margin-bottom: 5px;
                line-height: 1.5;
            }
            .section li {
                display: block;
                padding-top: 1em;
            }
            .section input, .section select {
                margin-left: 0.5em;
            }
            #loading {
                display: none;
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0, 0, 0, 0.7);
                color: white;
                padding: 20px;
                border-radius: 5px;
                text-align: center;
            }
        </style>
    </head>
    <body onload="showSection('personal_details');closeOverlay()">
        <h1>Lasting Power of Attorney (LPA) Form</h1>
        <div class="top-buttons">
            <button class="tour">Take a Tour</button>
            <button class="call">Call me</button>
            <button class="save-exit">Save and Exit</button>
            <button class="save">Save</button>
        </div>
        
        <div class="tabs">
            <div class="tab completed" onclick="showSection('personal_details')">Personal Details</div>
            <div class="tab in-progress" onclick="showSection('husband_details')">Your Husband</div>
            <div class="tab not-started" onclick="showSection('lpa_details')">LPA Details</div>
            <div class="tab completed" onclick="showSection('your_attorneys')">Your Attorneys</div>
            <div class="tab completed" onclick="showSection('husband_attorneys')">Husband's Attorneys</div>
            <div class="tab not-started" onclick="showSection('certificate_provider')">Certificate Provider</div>
            <div class="tab not-started" onclick="showSection('husband_certificate_provider')">Husband's Certificate Provider</div>
        </div>
        
        <div id="personal_details" class="section">
            <h3>Personal Details</h3>
            <label>Are you making LPAs for just yourself or for you and someone else?</label>
            <span class="info-icon" onclick="sendQuestion('Are you making LPAs for just yourself or for you and someone else?')">i</span>
            <input type="text">
        </div>

        <div id="husband_details" class="section">
            <h3>Your Husband</h3>
            <label>Please provide your husband's contact details</label>
            <span class="info-icon" onclick="sendQuestion('Please provide your husbands contact details')">i</span>
            <input type="text">
        </div>
    
        <div id="lpa_details" class="section">
            <h3>LPA Details</h3>
            <ul>
                <li>
                    <label>What type of LPAs do you wish to make?</label>
                    <span class="info-icon" onclick="sendQuestion('What type of LPAs do you wish to make?')">i</span>
                    <input type="text">
                </li>
                <li>
                    <label>How are attorneys to make decisions?</label>
                    <span class="info-icon" onclick="sendQuestion('How are attorneys to make decisions?')">i</span>
                    <input type="text">
                </li>
                <li>
                    <label>When do you want attorneys to be able to make decisions?</label>
                    <span class="info-icon" onclick="sendQuestion('When do you want attorneys to be able to make decisions?')">i</span>
                    <input type="text">
                </li>    
            </ul>
        </div>

        <div id="your_attorneys" class="section">
            <h3>Your Attorneys</h3>
            <ul>
                <li>
                    <label>Do you wish to appoint your husband as one of your attorneys?</label>
                    <span class="info-icon" onclick="sendQuestion('Do you wish to appoint your husband as one of your attorneys?')">i</span>
                    <select>
                        <option>Yes</option>
                        <option>No</option>
                    </select>
                </li>
                <li>
                    <label>Please add any other attorneys you wish to appoint</label>
                    <span class="info-icon" onclick="sendQuestion('Please add any other attorneys you wish to appoint')">i</span>
                    <input type="text">
                </li>
                <li>
                    <label>When do you want attorneys to be able to make decisions?</label>
                    <span class="info-icon" onclick="sendQuestion('When do you want attorneys to be able to make decisions?')">i</span>
                    <input type="text">
                </li>    
                <li>
                    <label>Please provide details of your replacement attorneys</label>
                    <span class="info-icon" onclick="sendQuestion('Please provide details of your replacement attorneys')">i</span>
                    <input type="text">
                </li>  
            </ul>
        </div>

        <div id="husband_attorneys" class="section">
            <h3>Husband's Attorneys</h3>
            <ul>
                <li>
                    <label>Are you to be appointed as attorney for your husband?</label>
                    <span class="info-icon" onclick="sendQuestion('Are you to be appointed as attorney for your husband?')">i</span>
                    <select>
                        <option>Yes</option>
                        <option>No</option>
                    </select>
                </li>
                <li>
                    <label>Who are to be your husband's other attorneys?</label>
                    <span class="info-icon" onclick="sendQuestion('Who are to be your husband's other attorneys?')">i</span>
                    <input type="text">
                </li>
                <li>
                    <label>Does your husband wish to appoint replacement attorneys?</label>
                    <span class="info-icon" onclick="sendQuestion('Does your husband wish to appoint replacement attorneys?')">i</span>
                    <input type="text">
                </li>    
                <li>
                    <label>Who are to be appointed your husband's replacement attorneys?</label>
                    <span class="info-icon" onclick="sendQuestion('Who are to be appointed your husband's replacement attorneys?')">i</span>
                    <input type="text">
                </li>  
            </ul>
        </div>

        <div id="certificate_provider" class="section">
            <h3>Certificate Provider</h3>
            <label>Who is to be your certificate provider?</label>
            <span class="info-icon" onclick="sendQuestion('Who is to be your certificate provider?')">i</span>
            <input type="text">
        </div>

        <div id="husband_certificate_provider" class="section">
            <h3>Husband's Certificate Provider</h3>
            <label>Who is to be your husband's certificate provider?</label>
            <span class="info-icon" onclick="sendQuestion('Who is to be your husband's certificate provider?')">i</span>
            <input type="text">
        </div>

        <div id="loading">Loading...</div>        
        <div id="overlay" class="overlay">
            <div class="overlay-content">
                <p id="overlay-text"></p>
                <button onclick="closeOverlay()">Close</button>
            </div>
        </div>
    </body>
    </html>`;

    return res.send(HTML);
});

export const getToolTip = async (req, res, next) => {
    //console.log(req.body)
    let question = req.body.question || req.query.question || "test"
    let casetype = req.body.casetype || req.query.casetype || "test"
    let extra = req.body.extra || req.query.extra || ""
    let responseText = ''
    try {
        const inputText = `<role>You will act as a solicitor in England and Wales.</role>  
<resources>  
<primary>Look first at LPA Guide or gov.uk for resources and information.</primary>  
<secondary>If more resources are needed, refer to respected law firm websites in England and Wales.</secondary>  
</resources>  
<capabilities>You will be able to answer any type of questions about the subject matter of this questionnaire, which is a <casetype>${casetype}</casetype>. </capabilities>  
<advice>Provide professional but friendly responses. Mention any cons of the advice given, such as how selecting one option may make a specific process redundant.</advice>  
<format>Encode answers using only HTML tags such as &lt;p&gt;, &lt;h2&gt;-&lt;h5&gt;, &lt;ul&gt;, &lt;ol&gt;, &lt;li&gt;. Please also provide some spacing by the &lt;BR&gt; tags.</format>
<restrictions>Do not ask if the user needs any additional help or any other questions.</restrictions>  
<clarification>Can you give me more information about what this question is asking, so that I can understand it better? Please also explain any terminology that a non-lawyer of average intelligence might not be familiar with: <question>${question}</question>.</clarification>`
        if (casetype == 'LPA') {
            const response = await openai.responses.create({
                model: "gpt-4o-mini",
                input: inputText,
                text: {
                    "format": {
                        "type": "text"
                    }
                },
                reasoning: {},
                tools: [
                    {
                        "type": "file_search",
                        "vector_store_ids": [
                            "vs_68075672abe08191836256fb5c9aa957"
                        ],
                        max_num_results: 10
                    }
                ],
                tool_choice: {
                    "type": "file_search"
                },
                temperature: 0,
                max_output_tokens: 2048,
                top_p: 1,
                store: true,
            });
            responseText = response.output_text
        } else {
            const response = await openai.responses.create({
                model: "gpt-4o-mini",
                input: inputText,
                temperature: 0,
                max_output_tokens: 2048,
                top_p: 1,
                store: true,
            });
            responseText = response.output_text
        }
    } catch (error) {
        console.error('OpenAI API error:', error);
        return res.status(500).json({
            success: false,
            message: 'Error generating tooltip content'
        });
    }
    console.log(responseText)
    res.send(responseText)
}