import express from "express";
import { generateJWT } from "#controllers/base.controller.js";

const router = express.Router();

/**
 * @swagger
 * /public/base/generateJWT:
 *   post:
 *     summary: Generate JWT token
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - user_id
 *               - role
 *             properties:
 *               user_id:
 *                 type: string
 *                 description: User ID for token generation
 *               role:
 *                 type: string
 *                 description: User role for access control
 *     responses:
 *       200:
 *         description: JWT token generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 token:
 *                   type: string
 *                   description: Generated JWT token
 *       400:
 *         description: Invalid user ID or role
 *       500:
 *         description: Error generating JWT token
 */

router.route("/generateJWT").post(generateJWT);

export default router;
