import { catchAsync, generateToken } from "#utils";
import { userService, caseService, smsService, questionaireService } from "#services";
import { roles } from "#middlewares/roles.js";
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from "#middlewares/ErrorClass.js";

/**
 * Firm API Login - Two-layer authentication: API token + user credentials
 * Requires both API token (for firm authentication) and email/password (for user authentication)
 */
export const firmApiLogin = catchAsync(async (req, res) => {
  // This endpoint requires API token authentication
  if (req.user.auth_type !== 'api_token') {
    return res.status(403).json({
      success: false,
      message: "This endpoint requires API token authentication"
    });
  }

  const { email, password } = req.body;

  // Validate required fields
  if (!email || !password) {
    return res.status(400).json({
      success: false,
      message: "Email and password are required"
    });
  }

  try {
    // Authenticate the user with email/password within the API token's law firm
    const loginResult = await userService.loginUser(
      email.toLowerCase(),
      password,
      req.user.lf_id // Restrict to the API token's law firm
    );

    if (!loginResult.success) {
      return res.status(401).json({
        success: false,
        message: loginResult.message || "Invalid email or password"
      });
    }

    // Verify the user belongs to the same law firm as the API token
    if (loginResult.user.lf_id !== req.user.lf_id) {
      return res.status(403).json({
        success: false,
        message: "User does not belong to the authorized law firm"
      });
    }

    // Verify the user ID matches the API token's user ID
    if (loginResult.user.user_id !== req.user.user_id) {
      return res.status(403).json({
        success: false,
        message: "User ID does not match the API token user"
      });
    }

    // Generate a standard JWT token for the authenticated user (not the API token user)
    const jwtToken = generateToken(
      loginResult.user.user_id,
      loginResult.user.email,
      loginResult.user.role,
      loginResult.user.lf_id,
      process.env.ACCESS_TOKEN_SECRET,
      "24h"
    );

    return res.status(200).json({
      success: true,
      message: "Login successful",
      data: {
        access_token: jwtToken,
        user: {
          user_id: loginResult.user.user_id,
          email: loginResult.user.email,
          role: loginResult.user.role,
          lf_id: loginResult.user.lf_id,
          first_name: loginResult.user.first_name,
          last_name: loginResult.user.last_name
        }
      }
    });

  } catch (error) {
    return res.status(500).json({
      success: false,
      message: "Login failed: " + error.message
    });
  }
});

/**
 * Add User to Law Firm via API
 */
export const firmApiAddUser = catchAsync(async (req, res) => {
  const {
    org_type,
    org_name,
    email,
    role,
    status = 1,
    title,
    first_name,
    middle_name,
    last_name,
    dob,
    gender,
    home_phone,
    mb_phone,
    wrk_phone,
    adr_1,
    adr_2,
    adr_3,
    state,
    town,
    country,
    post_code,
    pms_id
  } = req.body;

  // Validate required fields
  if (!email || !first_name || !last_name || !role) {
    return res.status(400).json({
      success: false,
      message: "email, first_name, last_name, and role are required"
    });
  }

  // Validate role - only allow certain roles to be created via API
  const allowedRoles = [roles.LawfirmAdmin, roles.LawfirmUser, roles.Client];
  if (!allowedRoles.includes(role)) {
    return res.status(400).json({
      success: false,
      message: "Invalid role. Only LawfirmAdmin, LawfirmUser and Client roles are allowed"
    });
  }

  try {
    const lowerEmail = email.toLowerCase();

    // Validate that the law firm exists
    const lawFirmCheck = await userService.findLawfirmById(req.user.lf_id);
    if (lawFirmCheck.length === 0) {
      return res.status(400).json({
        success: false,
        message: `Law firm with ID ${req.user.lf_id} does not exist`,
      });
    }

    const result = await userService.addUser(
      org_type,
      org_name,
      lowerEmail,
      null, // password will be generated
      role,
      status,
      req.user.lf_id, // Use law firm from API token
      title,
      first_name,
      middle_name,
      last_name,
      dob,
      gender,
      home_phone,
      mb_phone,
      wrk_phone,
      adr_1,
      adr_2,
      adr_3,
      state,
      town,
      country,
      post_code,
      req.user.user_id, // created_by
      pms_id
    );

    return res.status(201).json({
      success: true,
      message: "User added successfully",
      data: {
        user_id: result.user_id,
        email: lowerEmail,
        role: role,
        lf_id: req.user.lf_id
      }
    });

  } catch (error) {
    return res.status(400).json({
      success: false,
      message: error.message
    });
  }
});

/**
 * Get law firm information for debugging
 */
export const firmApiGetLawFirm = catchAsync(async (req, res) => {
  try {
    const lawFirm = await userService.findLawfirmById(req.user.lf_id);

    if (lawFirm.length === 0) {
      return res.status(404).json({
        success: false,
        message: `Law firm with ID ${req.user.lf_id} not found`
      });
    }

    return res.status(200).json({
      success: true,
      message: "Law firm information",
      data: {
        lf_id: lawFirm[0].lf_id,
        lf_org_name: lawFirm[0].lf_org_name,
        prefix: lawFirm[0].prefix,
        status: lawFirm[0].status
      }
    });

  } catch (error) {
    return res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

/**
 * Create Case via API
 */
export const firmApiCreateCase = catchAsync(async (req, res) => {
  const {
    case_name,
    desc,
    user_id,
    qtn_id,
    case_id_pms,
    token_
  } = req.body;

  // Validate required fields
  if (!case_name || !user_id || !qtn_id) {
    return res.status(400).json({
      success: false,
      message: "case_name, user_id, and qtn_id are required"
    });
  }

  try {
    // First get the questionnaire object
    const questionnaire = await questionaireService.getQuestionaire(qtn_id);

    if (!questionnaire || Object.keys(questionnaire).length === 0) {
      return res.status(404).json({
        success: false,
        message: "Questionnaire not found"
      });
    }

    // Verify questionnaire belongs to the law firm
    if (questionnaire.lf_id !== req.user.lf_id) {
      return res.status(403).json({
        success: false,
        message: "Questionnaire does not belong to your law firm"
      });
    }

    // Check if questionnaire is active
    if (questionnaire.status === 2 || questionnaire.status === 3) {
      return res.status(400).json({
        success: false,
        message: "Questionnaire is not active"
      });
    }

    const case_ = await caseService.createCase(
      case_name,
      desc,
      questionnaire, // Pass the questionnaire object
      qtn_id,
      user_id,
      req.user.user_id, // assigned_by
      case_id_pms,
      token_ || {} // token_ parameter for template replacement
    );

    // Send case email if case was created successfully
    if (case_.check === 1) {
      try {
        const userInfo = await caseService.getInfo(case_.case_id);
        // Use the same approach as normal case creation to send proper case verification email
        await smsService.sendLinkCaseVerify(
          userInfo.first_name,
          userInfo.email,
          userInfo.phone.slice(-4),
          case_.case_id,
          req.user.lf_id,
          userInfo.user_id,
          req.user
        );
      } catch (emailError) {
        // Don't fail the case creation if email fails
        console.error('Failed to send case creation email:', emailError);
      }
    }

    return res.status(201).json({
      success: true,
      message: "Case created successfully",
      data: {
        case_id: case_.case_id,
        check: case_.check
      }
    });

  } catch (error) {
    return res.status(400).json({
      success: false,
      message: error.message
    });
  }
});

/**
 * Update Case Status via API (matches normal implementation)
 */
export const firmApiUpdateCase = catchAsync(async (req, res) => {
  const { id, status } = req.body;

  // Validate required fields
  if (!id) {
    return res.status(400).json({
      success: false,
      message: "id is required"
    });
  }

  try {
    // Verify case belongs to the law firm
    const caseInfo = await caseService.getCase(id);
    if (!caseInfo || caseInfo.lf_id !== req.user.lf_id) {
      return res.status(404).json({
        success: false,
        message: "Case not found or access denied"
      });
    }

    // Update case status (matches normal implementation)
    if (status !== undefined) {
      await caseService.updateCaseStatus(status, id, req.user.user_id);
    }

    // If other fields need to be updated, use the updateCase function
    const updateData = { ...req.body };
    delete updateData.id; // Remove id from update data
    delete updateData.status; // Status is handled separately

    if (Object.keys(updateData).length > 0) {
      await caseService.updateCase(id, updateData, req.user.user_id);
    }

    return res.status(200).json({
      success: true,
      message: "Case updated successfully"
    });

  } catch (error) {
    return res.status(400).json({
      success: false,
      message: error.message
    });
  }
});

/**
 * Export Case Data via API (matches normal implementation)
 */
export const firmApiExportCaseData = catchAsync(async (req, res) => {
  const { case_id, format = 'json' } = req.body;

  // Validate required fields
  if (!case_id) {
    return res.status(400).json({
      success: false,
      message: "case_id is required"
    });
  }

  try {
    // Verify case belongs to the law firm
    const caseInfo = await caseService.getCase(case_id);
    if (!caseInfo || caseInfo.lf_id !== req.user.lf_id) {
      return res.status(404).json({
        success: false,
        message: "Case not found or access denied"
      });
    }

    // Export data in requested format
    if (format === 'json') {
      // Use the same function as normal implementation
      const result = await caseService.exportDataToPMS1(caseInfo);
      return res.status(200).json({
        success: true,
        message: "Case data exported successfully",
        data: result
      });
    } else if (format === 'csv') {
      const csvData = await caseService.exportCaseToCSV(case_id);
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="case_${case_id}.csv"`);
      return res.send(csvData);
    } else if (format === 'excel') {
      // Add Excel export functionality (matches normal implementation)
      const { check = 1 } = req.body;
      let excelBuffer;

      if (check == 1) {
        excelBuffer = await caseService.generateSpreadsheet(case_id);
      } else {
        excelBuffer = await caseService.generateSpreadsheet1(case_id);
      }

      return res.status(200).json({
        success: true,
        message: "Excel spreadsheet generated successfully",
        data: excelBuffer
      });
    } else {
      return res.status(400).json({
        success: false,
        message: "Unsupported format. Use 'json', 'csv', or 'excel'"
      });
    }

  } catch (error) {
    return res.status(400).json({
      success: false,
      message: error.message
    });
  }
});

/**
 * Get Specific Case via API
 */
export const firmApiGetCase = catchAsync(async (req, res) => {
  const { case_id } = req.body;

  // Validate required fields
  if (!case_id) {
    return res.status(400).json({
      success: false,
      message: "case_id is required"
    });
  }

  try {
    // Get case details
    const caseInfo = await caseService.getCase(case_id);

    if (!caseInfo) {
      return res.status(404).json({
        success: false,
        message: "Case not found"
      });
    }

    // Verify case belongs to the law firm
    if (caseInfo.lf_id !== req.user.lf_id) {
      return res.status(403).json({
        success: false,
        message: "Case does not belong to your law firm"
      });
    }

    // Get additional user info for the case
    const userInfo = await caseService.getInfo(case_id);

    return res.status(200).json({
      success: true,
      message: "Case details retrieved successfully",
      data: {
        case: caseInfo,
        user: userInfo
      }
    });

  } catch (error) {
    return res.status(400).json({
      success: false,
      message: error.message
    });
  }
});

/**
 * Get Specific Questionnaire via API
 */
export const firmApiGetQuestionnaire = catchAsync(async (req, res) => {
  const { qtn_id } = req.body;

  // Validate required fields
  if (!qtn_id) {
    return res.status(400).json({
      success: false,
      message: "qtn_id is required"
    });
  }

  try {
    // Get questionnaire details
    const questionnaireInfo = await questionaireService.getQuestionaire(qtn_id);

    // Check if questionnaire exists
    if (!questionnaireInfo) {
      return res.status(404).json({
        success: false,
        message: "Questionnaire not found"
      });
    }

    // The service returns an object directly, not an array
    const questionnaire = questionnaireInfo;

    // Verify questionnaire belongs to the law firm
    if (questionnaire.lf_id !== req.user.lf_id) {
      return res.status(403).json({
        success: false,
        message: "Questionnaire does not belong to your law firm"
      });
    }

    return res.status(200).json({
      success: true,
      message: "Questionnaire details retrieved successfully",
      data: questionnaire
    });

  } catch (error) {
    return res.status(400).json({
      success: false,
      message: error.message
    });
  }
});

/**
 * Get Cases for Law Firm via API
 */
export const firmApiGetCases = catchAsync(async (req, res) => {
  const { page = 1, size = 10, status, user_id } = req.query;

  try {
    const cases = await caseService.getCasesByLawFirm(
      req.user.lf_id,
      parseInt(page),
      parseInt(size),
      status,
      user_id
    );

    return res.status(200).json({
      success: true,
      message: "Cases retrieved successfully",
      data: cases
    });

  } catch (error) {
    return res.status(400).json({
      success: false,
      message: error.message
    });
  }
});
