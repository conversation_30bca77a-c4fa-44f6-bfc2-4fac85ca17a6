import express from "express";
import {
  findUser,
  getUser,
  deleteUser,
  addUserLawFirm,
  addUser,
  updateUser,
  getLawfirm,
  updateLawfirm,
  getUserLawfirm,
  findUserById,
  getLawFirmDetails,
  getLawFirmDetailsWithGraph,
  updateLawFirmStatus,
  updateLawFirmMandate,
  updateLawFirmPrefix,
  updateUserStatus,
  getClientLawfirm,
  changePassword,
  resetPassword,
  uploadBanner,
  logout,
  getListAdmin,
  resendInvitation,
  saveClientFromPMS,
  createKeyForLawFirm,
  regenKeyForLawFirm,
  updateProfileAdmin,
  updateConnectPMS,
  updateRole,
  updateDeletionTime,
  getDeletionTime
} from "#controllers/user.controller.js";

const router = express.Router();

/**
 * @swagger
 * components:
 *   securitySchemes:
 *     bearerAuth:
 *       type: http
 *       scheme: bearer
 *       bearerFormat: JWT
 *   schemas:
 *     User:
 *       type: object
 *       properties:
 *         user_id:
 *           type: string
 *         email:
 *           type: string
 *         status:
 *           type: integer
 *         role:
 *           type: string
 *         lf_id:
 *           type: string
 *         title:
 *           type: string
 *         first_name:
 *           type: string
 *         last_name:
 *           type: string
 *         mb_phone:
 *           type: string
 */

/**
 * @swagger
 * tags:
 *   - name: Users
 *     description: User management endpoints
 *   - name: Law Firms
 *     description: Law firm management endpoints
 *   - name: Authentication
 *     description: Authentication related endpoints
 */

/**
 * @swagger
 * /private/user/findUser:
 *   get:
 *     summary: Find user by email
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: email
 *         schema:
 *           type: string
 *         required: true
 *         description: User's email address
 *     responses:
 *       200:
 *         description: User found successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/User'
 *       500:
 *         description: Error finding user or permission denied
 */
router.route("/findUser").get(findUser);

/**
 * @swagger
 * /private/user/getUser:
 *   get:
 *     summary: Get current user information
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User information retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/User'
 */
router.route("/getUser").get(getUser);

/**
 * @swagger
 * /private/user/deleteUser:
 *   post:
 *     summary: Delete a user
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               user_id:
 *                 type: string
 *     responses:
 *       200:
 *         description: User deleted successfully
 *       500:
 *         description: Error deleting user or permission denied
 */
router.route("/deleteUser").post(deleteUser);

/**
 * @swagger
 * /private/user/updateUser:
 *   post:
 *     summary: Update user information
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               new_email:
 *                 type: string
 *               title:
 *                 type: string
 *               first_name:
 *                 type: string
 *               last_name:
 *                 type: string
 *               mb_phone:
 *                 type: string
 *     responses:
 *       200:
 *         description: User updated successfully
 *       500:
 *         description: Error updating user or validation failed
 */
router.route("/updateUser").post(updateUser);

// Law Firm Routes
/**
 * @swagger
 * /private/user/addLFUser:
 *   post:
 *     summary: Add a user to a law firm
 *     tags: [Law Firms]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *               role:
 *                 type: string
 *               lf_name:
 *                 type: string
 *     responses:
 *       200:
 *         description: User added to law firm successfully
 *       500:
 *         description: Error adding user or permission denied
 */
router.route("/addLFUser").post(addUserLawFirm);

/**
 * @swagger
 * /private/user/updateRole:
 *   post:
 *     summary: Update user role
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - user_id
 *               - role
 *             properties:
 *               user_id:
 *                 type: string
 *               role:
 *                 type: string
 *                 description: New role for the user
 *     responses:
 *       200:
 *         description: User role updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/User'
 *       500:
 *         description: Error updating user role
 */
router.route("/updateRole").post(updateRole);

/**
 * @swagger
 * /private/user/getLawfirm:
 *   get:
 *     summary: Get law firm information
 *     tags: [Law Firms]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Law firm information retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     lf_id:
 *                       type: string
 *                     name:
 *                       type: string
 *                     status:
 *                       type: integer
 *                     mandate:
 *                       type: string
 *                     prefix:
 *                       type: string
 *       500:
 *         description: Error retrieving law firm information
 */
router.route("/getLawfirm").get(getLawfirm);

/**
 * @swagger
 * /private/user/getUserLawfirm:
 *   get:
 *     summary: Get law firm information for current user
 *     tags: [Law Firms]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User's law firm information retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     lf_id:
 *                       type: string
 *                     name:
 *                       type: string
 *                     role:
 *                       type: string
 *                     status:
 *                       type: integer
 *       500:
 *         description: Error retrieving law firm information
 */
router.route("/getUserLawfirm").get(getUserLawfirm);

/**
 * @swagger
 * /private/user/addUser:
 *   post:
 *     summary: Add a new user
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - role
 *               - lf_id
 *             properties:
 *               email:
 *                 type: string
 *               role:
 *                 type: string
 *               lf_id:
 *                 type: string
 *               title:
 *                 type: string
 *               first_name:
 *                 type: string
 *               last_name:
 *                 type: string
 *               mb_phone:
 *                 type: string
 *     responses:
 *       200:
 *         description: User added successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/User'
 *       500:
 *         description: Error adding user or validation failed
 */
router.route("/addUser").post(addUser);

/**
 * @swagger
 * /private/user/updateLawfirm:
 *   post:
 *     summary: Update law firm information
 *     tags: [Law Firms]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - lf_id
 *             properties:
 *               lf_id:
 *                 type: string
 *               name:
 *                 type: string
 *               status:
 *                 type: integer
 *               mandate:
 *                 type: string
 *               prefix:
 *                 type: string
 *     responses:
 *       200:
 *         description: Law firm updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     lf_id:
 *                       type: string
 *                     name:
 *                       type: string
 *                     status:
 *                       type: integer
 *                     mandate:
 *                       type: string
 *                     prefix:
 *                       type: string
 *       500:
 *         description: Error updating law firm
 */
router.route("/updateLawfirm").post(updateLawfirm);
/**
 * @swagger
 * /private/user/findUserById:
 *   get:
 *     summary: Find user by ID
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: user_id
 *         schema:
 *           type: string
 *         required: true
 *         description: User ID to search for
 *     responses:
 *       200:
 *         description: User found successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/User'
 *       500:
 *         description: Error finding user or permission denied
 */
router.route("/findUserById").get(findUserById);
/**
 * @swagger
 * /private/user/getLawfirmDetails:
 *   post:
 *     summary: Get detailed law firm information
 *     tags: [Law Firms]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - lf_id
 *             properties:
 *               lf_id:
 *                 type: string
 *     responses:
 *       200:
 *         description: Law firm details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     lf_id:
 *                       type: string
 *                     name:
 *                       type: string
 *                     status:
 *                       type: integer
 *                     mandate:
 *                       type: string
 *                     prefix:
 *                       type: string
 *                     created_at:
 *                       type: string
 *                     updated_at:
 *                       type: string
 *       500:
 *         description: Error retrieving law firm details
 */
router.route("/getLawfirmDetails").post(getLawFirmDetails);

/**
 * @swagger
 * /private/user/getLawfirmDetailsWithGraph:
 *   post:
 *     summary: Get detailed law firm information with Graph credentials status
 *     tags: [Law Firms]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - lf_id
 *             properties:
 *               lf_id:
 *                 type: integer
 *                 description: Law firm ID
 *     responses:
 *       200:
 *         description: Law firm details with Graph credentials status retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     lf_id:
 *                       type: integer
 *                     lf_org_name:
 *                       type: string
 *                     graphCredentials:
 *                       type: object
 *                       nullable: true
 *                       properties:
 *                         id:
 *                           type: integer
 *                         tenant_id:
 *                           type: string
 *                         client_id:
 *                           type: string
 *                         is_active:
 *                           type: boolean
 *                     mailProvider:
 *                       type: object
 *                       properties:
 *                         primaryProvider:
 *                           type: string
 *                         fallbackProvider:
 *                           type: string
 *                         graphConfigured:
 *                           type: boolean
 *       500:
 *         description: Error retrieving law firm details or permission denied
 */
router.route("/getLawfirmDetailsWithGraph").post(getLawFirmDetailsWithGraph);

/**
 * @swagger
 * /private/user/updateLawFirmStatus:
 *   post:
 *     summary: Update law firm status
 *     tags: [Law Firms]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - lf_id
 *               - status
 *             properties:
 *               lf_id:
 *                 type: string
 *               status:
 *                 type: integer
 *                 description: New status code for the law firm
 *     responses:
 *       200:
 *         description: Law firm status updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     lf_id:
 *                       type: string
 *                     status:
 *                       type: integer
 *       500:
 *         description: Error updating law firm status
 */
router.route("/updateLawFirmStatus").post(updateLawFirmStatus);
/**
 * @swagger
 * /private/user/updateLawFirmMandate:
 *   post:
 *     summary: Update law firm mandate
 *     tags: [Law Firms]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - lf_id
 *               - mandate
 *             properties:
 *               lf_id:
 *                 type: string
 *               mandate:
 *                 type: string
 *                 description: New mandate for the law firm
 *     responses:
 *       200:
 *         description: Law firm mandate updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     lf_id:
 *                       type: string
 *                     mandate:
 *                       type: string
 *       500:
 *         description: Error updating law firm mandate
 */
router.route("/updateLawFirmMandate").post(updateLawFirmMandate);
/**
 * @swagger
 * /private/user/updateLawFirmPrefix:
 *   post:
 *     summary: Update law firm prefix
 *     tags: [Law Firms]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - lf_id
 *               - prefix
 *             properties:
 *               lf_id:
 *                 type: string
 *               prefix:
 *                 type: string
 *                 description: New prefix for the law firm
 *     responses:
 *       200:
 *         description: Law firm prefix updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     lf_id:
 *                       type: string
 *                     prefix:
 *                       type: string
 *       500:
 *         description: Error updating law firm prefix
 */
router.route("/updateLawFirmPrefix").post(updateLawFirmPrefix);
/**
 * @swagger
 * /private/user/updateUserStatus:
 *   post:
 *     summary: Update user status
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - user_id
 *               - status
 *             properties:
 *               user_id:
 *                 type: string
 *               status:
 *                 type: integer
 *                 description: New status code for the user
 *     responses:
 *       200:
 *         description: User status updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/User'
 *       500:
 *         description: Error updating user status
 */
router.route("/updateUserStatus").post(updateUserStatus);
/**
 * @swagger
 * /private/user/getClientLawfirm:
 *   get:
 *     summary: Get client law firm information
 *     tags: [Law Firms]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Client law firm information retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     lf_id:
 *                       type: string
 *                     name:
 *                       type: string
 *                     status:
 *                       type: integer
 *                     mandate:
 *                       type: string
 *                     prefix:
 *                       type: string
 *       500:
 *         description: Error retrieving client law firm information
 */
router.route("/getClientLawfirm").get(getClientLawfirm);
/**
 * @swagger
 * /private/user/changePassword:
 *   post:
 *     summary: Change user password
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - current_password
 *               - new_password
 *             properties:
 *               current_password:
 *                 type: string
 *                 description: User's current password
 *               new_password:
 *                 type: string
 *                 description: New password to set
 *     responses:
 *       200:
 *         description: Password changed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *       400:
 *         description: Invalid current password or weak new password
 *       500:
 *         description: Error changing password
 */
router.route("/changePassword").post(changePassword);
/**
 * @swagger
 * /private/user/resetPassword:
 *   post:
 *     summary: Reset user password
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - new_password
 *             properties:
 *               email:
 *                 type: string
 *                 description: User's email address
 *               new_password:
 *                 type: string
 *                 description: New password to set
 *     responses:
 *       200:
 *         description: Password reset successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *       400:
 *         description: Invalid email or weak new password
 *       500:
 *         description: Error resetting password
 */
router.route("/resetPassword").post(resetPassword);
/**
 * @swagger
 * /private/user/uploadBanner:
 *   post:
 *     summary: Upload a banner image
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               banner:
 *                 type: string
 *                 format: binary
 *                 description: Banner image file to upload
 *     responses:
 *       200:
 *         description: Banner uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 url:
 *                   type: string
 *                   description: URL of the uploaded banner
 *       400:
 *         description: Invalid file type or size
 *       500:
 *         description: Error uploading banner
 */
router.route("/uploadBanner").post(uploadBanner);
/**
 * @swagger
 * /private/user/logout:
 *   post:
 *     summary: Log out current user
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User logged out successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *       500:
 *         description: Error logging out
 */
router.route("/logout").post(logout);
/**
 * @swagger
 * /private/user/getListAdmin:
 *   get:
 *     summary: Get list of admin users
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of admin users retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/User'
 *       500:
 *         description: Error retrieving admin list
 */
router.route("/getListAdmin").get(getListAdmin);
/**
 * @swagger
 * /private/user/resendInvitation:
 *   post:
 *     summary: Resend invitation email to user
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *             properties:
 *               email:
 *                 type: string
 *     responses:
 *       200:
 *         description: Invitation resent successfully
 */
router.route("/resendInvitation").post(resendInvitation);

/**
 * @swagger
 * /private/user/saveClientFromPMS:
 *   post:
 *     summary: Save client information from PMS
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - client_data
 *             properties:
 *               client_data:
 *                 type: object
 *                 properties:
 *                   email:
 *                     type: string
 *                   first_name:
 *                     type: string
 *                   last_name:
 *                     type: string
 *     responses:
 *       200:
 *         description: Client saved successfully from PMS
 */
router.route("/saveClientFromPMS").post(saveClientFromPMS);

/**
 * @swagger
 * /private/user/createKeyForLawFirm:
 *   post:
 *     summary: Create API key for law firm
 *     tags: [Law Firms]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - lf_id
 *             properties:
 *               lf_id:
 *                 type: string
 *     responses:
 *       200:
 *         description: API key created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 api_key:
 *                   type: string
 */
router.route("/createKeyForLawFirm").post(createKeyForLawFirm);

/**
 * @swagger
 * /private/user/regenKeyForLawFirm:
 *   post:
 *     summary: Regenerate API key for law firm
 *     tags: [Law Firms]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - lf_id
 *             properties:
 *               lf_id:
 *                 type: string
 *     responses:
 *       200:
 *         description: API key regenerated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 api_key:
 *                   type: string
 */
router.route("/regenKeyForLawFirm").post(regenKeyForLawFirm);

/**
 * @swagger
 * /private/user/updateProfileAdmin:
 *   post:
 *     summary: Update admin profile
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               first_name:
 *                 type: string
 *               last_name:
 *                 type: string
 *               email:
 *                 type: string
 *     responses:
 *       200:
 *         description: Admin profile updated successfully
 */
router.route("/updateProfileAdmin").post(updateProfileAdmin);

/**
 * @swagger
 * /private/user/updateConnectPMS:
 *   post:
 *     summary: Update PMS connection settings
 *     tags: [Law Firms]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - lf_id
 *               - pms_settings
 *             properties:
 *               lf_id:
 *                 type: string
 *               pms_settings:
 *                 type: object
 *     responses:
 *       200:
 *         description: PMS connection settings updated successfully
 */
router.route("/updateConnectPMS").post(updateConnectPMS);

router.route("/updateDeletionTime").post(updateDeletionTime);
router.route("/getDeletionTime").post(getDeletionTime);
export default router;
