-- Migration: Database Optimization - Add Indexes for Better Performance
-- Date: 2025-01-26
-- Description: Creates indexes on frequently queried columns to optimize getTemplate, getQuestionnaire, and getCases functions
-- Note: Uses MySQL-compatible syntax without IF NOT EXISTS (for broader compatibility)

-- ============================================================================
-- IMPORTANT: MySQL Compatibility Notes
-- ============================================================================
-- This script is compatible with MySQL 5.7+ and MariaDB 10.2+
-- If you get syntax errors, your MySQL version may not support some features
-- In that case, run the commands manually and ignore errors for non-existing indexes

-- ============================================================================
-- CRITICAL PERFORMANCE INDEXES (Core optimization)
-- ============================================================================

-- ============================================================================
-- CREATE NEW INDEXES
-- ============================================================================

-- ============================================================================
-- TEMPLATES TABLE INDEXES
-- ============================================================================

-- Primary lookup index for templates (usually already exists as PRIMARY KEY)
-- CREATE INDEX idx_templates_id ON templates(id);

-- Status-based queries for template filtering
CREATE INDEX idx_templates_status ON templates(status);

-- Combined index for template listing with status and ordering
CREATE INDEX idx_templates_status_created ON templates(status, created_at DESC);

-- ============================================================================
-- GROUPS TABLE INDEXES
-- ============================================================================

-- Foreign key index for template relationships (most important for getTemplate optimization)
CREATE INDEX idx_groups_tem_id ON `groups`(tem_id);

-- Combined index for template groups with ordering
CREATE INDEX idx_groups_tem_id_id ON `groups`(tem_id, id);

-- ============================================================================
-- QUESTIONS TABLE INDEXES
-- ============================================================================

-- Foreign key index for group relationships (critical for N+1 query elimination)
CREATE INDEX idx_questions_gr_id ON questions(gr_id);

-- Combined index for group questions with ordering
CREATE INDEX idx_questions_gr_id_id ON questions(gr_id, id);

-- Index for parent_answer relationships (conditional question logic)
CREATE INDEX idx_questions_parent_answer ON questions(parent_answer);

-- ============================================================================
-- ANSWER TABLE INDEXES
-- ============================================================================

-- Foreign key index for question relationships (critical for N+1 query elimination)
CREATE INDEX idx_answer_ques_id ON answer(ques_id);

-- ============================================================================
-- ANSWERS TABLE INDEXES
-- ============================================================================

-- Foreign key index for answer relationships (critical for N+1 query elimination)
CREATE INDEX idx_answers_ques_id ON answers(ques_id);

-- ============================================================================
-- QUESTIONAIRES TABLE INDEXES
-- ============================================================================

-- Primary lookup index (usually already exists as PRIMARY KEY)
-- CREATE INDEX idx_questionaires_id ON questionaires(id);

-- Law firm filtering (very important for multi-tenant queries)
CREATE INDEX idx_questionaires_lf_id ON questionaires(lf_id);

-- Template relationship index
CREATE INDEX idx_questionaires_tem_id ON questionaires(tem_id);

-- Status-based filtering
CREATE INDEX idx_questionaires_status ON questionaires(status);

-- Combined index for law firm questionnaires with status and ordering
CREATE INDEX idx_questionaires_lf_status_created ON questionaires(lf_id, status, created_at DESC);

-- Combined index for template-based questionnaires
CREATE INDEX idx_questionaires_tem_lf_status ON questionaires(tem_id, lf_id, status);

-- ============================================================================
-- CASES TABLE INDEXES
-- ============================================================================

-- Law firm filtering (critical for getCases optimization)
CREATE INDEX idx_cases_lf_id ON cases(lf_id);

-- User filtering within law firm
CREATE INDEX idx_cases_user_id ON cases(user_id);

-- Status filtering
CREATE INDEX idx_cases_status ON cases(status);

-- Questionnaire relationship
CREATE INDEX idx_cases_qtn_id ON cases(qtn_id);

-- Combined index for law firm cases with status and ordering (most important for getCases)
CREATE INDEX idx_cases_lf_status_created ON cases(lf_id, status, created_at DESC);

-- Combined index for law firm cases with user and ordering
CREATE INDEX idx_cases_lf_user_created ON cases(lf_id, user_id, created_at DESC);

-- Combined index for comprehensive case filtering
CREATE INDEX idx_cases_lf_status_user_created ON cases(lf_id, status, user_id, created_at DESC);

-- ============================================================================
-- USERS TABLE INDEXES
-- ============================================================================

-- Email lookup (authentication) - usually already exists as UNIQUE
-- CREATE INDEX idx_users_email ON users(email);

-- Law firm relationship
CREATE INDEX idx_users_lf_id ON users(lf_id);

-- Role-based filtering
CREATE INDEX idx_users_role ON users(role);

-- Combined index for law firm users
CREATE INDEX idx_users_lf_role_status ON users(lf_id, role, status);

-- ============================================================================
-- USER_INFO TABLE INDEXES
-- ============================================================================

-- Foreign key relationship with users
CREATE INDEX idx_user_info_user_id ON user_info(user_id);

-- ============================================================================
-- LAW_FIRM TABLE INDEXES
-- ============================================================================

-- Status filtering for active law firms
CREATE INDEX idx_law_firm_status ON law_firm(status);

-- ============================================================================
-- STATUS TABLE INDEXES
-- ============================================================================

-- Name lookup for status display
CREATE INDEX idx_status_name ON status(name);

-- ============================================================================
-- CONDITIONS TABLE INDEXES
-- ============================================================================

-- Template relationship for conditions
CREATE INDEX idx_conditions_template_id ON `conditions`(template_id);

-- ============================================================================
-- PERFORMANCE ANALYSIS QUERIES
-- ============================================================================

-- Use these queries to analyze index usage and performance:

-- Check index usage:
-- SHOW INDEX FROM templates;
-- SHOW INDEX FROM questionaires;
-- SHOW INDEX FROM cases;

-- Analyze query performance:
-- EXPLAIN SELECT * FROM templates t LEFT JOIN `groups` g ON g.tem_id = t.id WHERE t.id = 1;
-- EXPLAIN SELECT * FROM questionaires q JOIN law_firm l ON l.lf_id = q.lf_id WHERE q.lf_id = 1;
-- EXPLAIN SELECT * FROM cases c WHERE c.lf_id = 1 ORDER BY c.created_at DESC LIMIT 10;

-- Monitor slow queries:
-- SET GLOBAL slow_query_log = 'ON';
-- SET GLOBAL long_query_time = 1;
-- SHOW VARIABLES LIKE 'slow_query_log%';

-- ============================================================================
-- OPTIONAL TABLE INDEXES (only create if tables exist)
-- ============================================================================

-- QUESTIONAIRES_REQUEST TABLE INDEXES (uncomment if table exists)
-- CREATE INDEX idx_questionaires_request_qtn_id ON questionaires_request(qtn_id);
-- CREATE INDEX idx_questionaires_request_status ON questionaires_request(status);
-- CREATE INDEX idx_questionaires_request_qtn_status ON questionaires_request(qtn_id, status);

-- API_TOKENS TABLE INDEXES (uncomment if table exists)
-- CREATE INDEX idx_api_tokens_token_hash ON api_tokens(token_hash);
-- CREATE INDEX idx_api_tokens_lf_id ON api_tokens(lf_id);
-- CREATE INDEX idx_api_tokens_user_id ON api_tokens(user_id);
-- CREATE INDEX idx_api_tokens_is_active ON api_tokens(is_active);
-- CREATE INDEX idx_api_tokens_lf_active_expires ON api_tokens(lf_id, is_active, expires_at);

-- ============================================================================
-- VERIFY INDEX CREATION
-- ============================================================================

-- Show all indexes for key tables to verify creation
SHOW INDEX FROM templates;
SHOW INDEX FROM `groups`;
SHOW INDEX FROM questions;
SHOW INDEX FROM questionaires;
SHOW INDEX FROM cases;

-- ============================================================================
-- ROLLBACK SCRIPT (if needed)
-- ============================================================================

-- To remove all indexes created by this migration, run:
/*
DROP INDEX idx_templates_status ON templates;
DROP INDEX idx_templates_status_created ON templates;
DROP INDEX idx_groups_tem_id ON `groups`;
DROP INDEX idx_groups_tem_id_id ON `groups`;
DROP INDEX idx_questions_gr_id ON questions;
DROP INDEX idx_questions_gr_id_id ON questions;
DROP INDEX idx_questions_parent_answer ON questions;
DROP INDEX idx_answer_ques_id ON answer;
DROP INDEX idx_answers_ques_id ON answers;
DROP INDEX idx_questionaires_lf_id ON questionaires;
DROP INDEX idx_questionaires_tem_id ON questionaires;
DROP INDEX idx_questionaires_status ON questionaires;
DROP INDEX idx_questionaires_lf_status_created ON questionaires;
DROP INDEX idx_questionaires_tem_lf_status ON questionaires;
DROP INDEX idx_cases_lf_id ON cases;
DROP INDEX idx_cases_user_id ON cases;
DROP INDEX idx_cases_status ON cases;
DROP INDEX idx_cases_qtn_id ON cases;
DROP INDEX idx_cases_lf_status_created ON cases;
DROP INDEX idx_cases_lf_user_created ON cases;
DROP INDEX idx_cases_lf_status_user_created ON cases;
DROP INDEX idx_users_lf_id ON users;
DROP INDEX idx_users_role ON users;
DROP INDEX idx_users_lf_role_status ON users;
DROP INDEX idx_user_info_user_id ON user_info;
DROP INDEX idx_law_firm_status ON law_firm;
DROP INDEX idx_status_name ON status;
DROP INDEX idx_conditions_template_id ON `conditions`;
*/
