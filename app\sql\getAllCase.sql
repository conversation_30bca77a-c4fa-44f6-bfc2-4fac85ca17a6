SELECT 
    c.case_id
    , c.id
    , c.case_name
    , u.email
    , ui.first_name
    , ui.last_name
    , c.status
    , c.created_at
    , c.update_at
    , u. user_id
    , c.qtn_id
    , c.case_id_pms 
    , pi2.first_name party_first_name
    , pi2.last_name party_last_name
    , pi2.country_code 
    , pi2.phone 
    , pi2.email party_email
    , c.source
FROM cases c
LEFT JOIN users u ON c.user_id = u.user_id
LEFT JOIN user_info ui ON u.user_id = ui.user_id
LEFT JOIN party_info pi2 ON c.party_info_id  = pi2.id
WHERE c.status in (?)
AND c.lf_id = ?
ORDER BY c.case_id DESC
LIMIT ?, ?;
