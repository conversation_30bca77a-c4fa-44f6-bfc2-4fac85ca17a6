import express from "express";
import {
  sendOTPLogin,
  verify<PERSON><PERSON><PERSON><PERSON>,
  sendLinkCaseVerify,
} from "#controllers/sms.controller.js";

/**
 * @swagger
 * components:
 *   schemas:
 *     OTPRequest:
 *       type: object
 *       properties:
 *         email:
 *           type: string
 *           description: User's email address
 *         token:
 *           type: string
 *           description: Optional token for authentication
 *     OTPVerifyRequest:
 *       type: object
 *       properties:
 *         email:
 *           type: string
 *           description: User's email address
 *         otp:
 *           type: string
 *           description: OTP code to verify
 *         token:
 *           type: string
 *           description: Optional token for authentication
 *     CaseVerifyRequest:
 *       type: object
 *       required:
 *         - case_id
 *       properties:
 *         case_id:
 *           type: string
 *           description: ID of the case to verify
 */

const smsRoute = express.Router();

/**
 * @swagger
 * /private/sms/sendLinkCaseVerify:
 *   post:
 *     summary: Send case verification link via SMS
 *     tags: [SMS]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CaseVerifyRequest'
 *     responses:
 *       200:
 *         description: Link sent successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 link:
 *                   type: string
 *                   description: Generated verification link
 *       500:
 *         description: Permission denied or server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 */
smsRoute.route("/sendLinkCaseVerify").post(sendLinkCaseVerify);

/**
 * @swagger
 * /private/sms/sendOTP:
 *   post:
 *     summary: Send OTP code via SMS for login
 *     tags: [SMS]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/OTPRequest'
 *     responses:
 *       200:
 *         description: OTP sent successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 result:
 *                   type: object
 *                   description: OTP details (only in development)
 *       500:
 *         description: Error sending OTP
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 */
smsRoute.route("/sendOTP").post(sendOTPLogin);

/**
 * @swagger
 * /private/sms/verifyOTP:
 *   post:
 *     summary: Verify OTP code sent via SMS
 *     tags: [SMS]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/OTPVerifyRequest'
 *     responses:
 *       200:
 *         description: OTP verified successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 result:
 *                   type: object
 *                   description: Verification result
 *       500:
 *         description: Error verifying OTP
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 */
smsRoute.route("/verifyOTP").post(verifyOTPLogin);

export default smsRoute;
