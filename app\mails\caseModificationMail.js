import sgMail from "@sendgrid/mail";
import dotenv from "dotenv";

dotenv.config();

export const caseModificationMail = async (
    email,
    first_name,
    case_id,
    modifications,
    path,
    bgColor,
    textColor,
    title,
    header,
    body,
    footer,
    logo,
    user
) => {
    if (!logo) {
        logo = "width: 80px; height: 80px;"
    }
    if (!title) {
        title = `Your case has been updated`
    }
    if (!header) {
        header = `                 
            <p class="invitation-text">Case Update Notification</p>
        `;
    }
    
    // Generate modifications list
    let modificationsHtml = '';
    if (modifications && modifications.length > 0) {
        modificationsHtml = '<div style="margin: 20px 0;"><h3>The following modifications have been made:</h3><ul>';
        modifications.forEach(mod => {
            modificationsHtml += `
                <li style="margin: 10px 0; padding: 10px; background-color: #f5f5f5; border-left: 4px solid #007bff;">
                    <strong>Question:</strong> ${mod.question}<br>
                    <strong>Your answer:</strong> ${mod.originalAnswer}<br>
                    <strong>Modified answer:</strong> ${mod.newAnswer}
                </li>
            `;
        });
        modificationsHtml += '</ul></div>';
    }
    
    if (!body) {
        body = `                    
        <p>
            Hello ${first_name},
        </p>
        <p>
            Thank you for submitting answers to our questionnaire. We have made the following modifications to your case:
        </p>
        ${modificationsHtml}
        <p>
            If you have any questions about these changes, please don't hesitate to contact us.
        </p>`;
    }
    
    if (!footer) {
        footer = `                    
        <p>
            Best regards,
        </p>
        <p>
            Legal Workflow Limited Team
        </p>`;
    }
    if (!bgColor) {
        bgColor = "rgba(213, 49, 49, 0.71)";
    }
    if (!textColor) {
        textColor = "#fff";
    }
    if (!path) {
        path = "static/images/logo.png";
    }

    sgMail.setApiKey(process.env.SENDGRID_API_KEY);
    let msg = {};
    msg = {
        from: { name: "Propero Support", email: "<EMAIL>" },
        personalizations: [
            {
                to: email,
            },
        ],
        subject: `Your case ${case_id} has been updated`,
        html: ` 
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Case Update</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    margin: 0;
                    padding: 0;
                    background-color: #f4f4f4;
                }
                .container {
                    max-width: 600px;
                    margin: 0 auto;
                    background-color: #ffffff;
                    padding: 20px;
                    border-radius: 10px;
                    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
                }
                .header {
                    text-align: center;
                    background-color: ${bgColor};
                    color: ${textColor};
                    padding: 20px;
                    border-radius: 10px 10px 0 0;
                }
                .logo {
                    ${logo}
                    margin: 0 auto 20px;
                    display: block;
                }
                .invitation-text {
                    font-size: 24px;
                    font-weight: bold;
                    margin: 0;
                }
                .content {
                    padding: 20px;
                    line-height: 1.6;
                }
                .footer {
                    text-align: center;
                    padding: 20px;
                    background-color: #f8f9fa;
                    border-radius: 0 0 10px 10px;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <img src="${process.env.SERVER_URL}/${path}" alt="Logo" class="logo">
                    ${header}
                </div>
                <div class="content">
                    ${body}
                </div>
                <div class="footer">
                    ${footer}
                </div>
            </div>
        </body>
        </html>
        `,
    };
    try {
        await sgMail.send(msg);
        console.log("Case modification email sent successfully");
    } catch (error) {
        console.error("Error sending case modification email:", error);
        throw error;
    }
};
