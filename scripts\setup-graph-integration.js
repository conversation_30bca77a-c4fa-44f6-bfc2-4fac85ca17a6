#!/usr/bin/env node

/**
 * Setup script for Microsoft Graph API integration
 * This script helps administrators set up the Graph API integration
 */

import readline from 'readline';
import { graphCredentialsService } from '../app/services/graphCredentials.service.js';
import { mailProviderService } from '../app/services/mailProvider.service.js';
import pool from '../app/config/db.js';

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function main() {
  console.log('🚀 Microsoft Graph API Integration Setup');
  console.log('=====================================\n');

  try {
    // Check database connection
    console.log('📊 Checking database connection...');
    const connection = await pool.getConnection();
    await connection.ping();
    connection.destroy();
    console.log('✅ Database connection successful\n');

    // Check if graph_credentials table exists
    console.log('🔍 Checking database schema...');
    const con = await pool.getConnection();
    try {
      await con.query('SELECT 1 FROM graph_credentials LIMIT 1');
      console.log('✅ graph_credentials table exists\n');
    } catch (error) {
      console.log('❌ graph_credentials table not found');
      console.log('Please run the migration script first:');
      console.log('mysql -u username -p database_name < migrations/001_create_graph_credentials_table.sql\n');
      process.exit(1);
    } finally {
      con.destroy();
    }

    const action = await question('What would you like to do?\n1. Add Graph credentials for a law firm\n2. Test existing credentials\n3. List all configured law firms\n4. Exit\n\nEnter your choice (1-4): ');

    switch (action.trim()) {
      case '1':
        await addCredentials();
        break;
      case '2':
        await testCredentials();
        break;
      case '3':
        await listCredentials();
        break;
      case '4':
        console.log('👋 Goodbye!');
        break;
      default:
        console.log('❌ Invalid choice');
    }

  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    process.exit(1);
  } finally {
    rl.close();
    process.exit(0);
  }
}

async function addCredentials() {
  console.log('\n📝 Adding Microsoft Graph credentials');
  console.log('=====================================');

  const lf_id = await question('Law firm ID: ');
  const tenant_id = await question('Tenant ID (Directory ID): ');
  const client_id = await question('Client ID (Application ID): ');
  const client_secret = await question('Client Secret: ');

  if (!lf_id || !tenant_id || !client_id || !client_secret) {
    console.log('❌ All fields are required');
    return;
  }

  try {
    const credentials = await graphCredentialsService.addCredentials({
      lf_id: parseInt(lf_id),
      tenant_id: tenant_id.trim(),
      client_id: client_id.trim(),
      client_secret: client_secret.trim(),
      created_by: 1 // System user
    });

    console.log('✅ Credentials added successfully!');
    console.log(`📋 Credential ID: ${credentials.id}`);
    
    // Test the credentials
    const testChoice = await question('\nWould you like to test the credentials now? (y/n): ');
    if (testChoice.toLowerCase() === 'y') {
      await testSpecificCredentials(parseInt(lf_id));
    }

  } catch (error) {
    console.log('❌ Failed to add credentials:', error.message);
  }
}

async function testCredentials() {
  console.log('\n🧪 Testing Microsoft Graph credentials');
  console.log('=====================================');

  const lf_id = await question('Law firm ID to test: ');
  
  if (!lf_id) {
    console.log('❌ Law firm ID is required');
    return;
  }

  await testSpecificCredentials(parseInt(lf_id));
}

async function testSpecificCredentials(lf_id) {
  try {
    console.log(`🔍 Testing credentials for law firm ${lf_id}...`);
    
    const testResults = await mailProviderService.testProviders(lf_id);
    
    console.log('\n📊 Test Results:');
    console.log('================');
    
    console.log(`SendGrid: ${testResults.sendgrid.available ? '✅' : '❌'} ${testResults.sendgrid.message}`);
    console.log(`Graph API: ${testResults.graph.available ? '✅' : '❌'} ${testResults.graph.message}`);
    
    const providerInfo = await mailProviderService.getProviderInfo(lf_id);
    console.log(`\n📧 Primary Provider: ${providerInfo.primaryProvider}`);
    console.log(`🔄 Fallback Provider: ${providerInfo.fallbackProvider}`);
    console.log(`⚙️  Graph Configured: ${providerInfo.graphConfigured ? 'Yes' : 'No'}`);

  } catch (error) {
    console.log('❌ Test failed:', error.message);
  }
}

async function listCredentials() {
  console.log('\n📋 Configured Law Firms');
  console.log('=======================');

  try {
    const credentials = await graphCredentialsService.getAllWithCredentials();
    
    if (credentials.length === 0) {
      console.log('No law firms have Graph credentials configured.');
      return;
    }

    console.log(`Found ${credentials.length} law firm(s) with Graph credentials:\n`);
    
    credentials.forEach((cred, index) => {
      console.log(`${index + 1}. ${cred.lf_org_name} (ID: ${cred.lf_id})`);
      console.log(`   Tenant ID: ${cred.tenant_id}`);
      console.log(`   Client ID: ${cred.client_id}`);
      console.log(`   Status: ${cred.is_active ? 'Active' : 'Inactive'}`);
      console.log(`   Created: ${cred.created_at}`);
      console.log('');
    });

  } catch (error) {
    console.log('❌ Failed to list credentials:', error.message);
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n👋 Setup cancelled by user');
  rl.close();
  process.exit(0);
});

// Run the setup
main().catch(console.error);
