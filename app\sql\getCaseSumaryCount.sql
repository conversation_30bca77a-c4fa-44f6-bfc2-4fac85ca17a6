SELECT COUNT(*) 
FROM (
    SELECT c.user_id, 
           ui.first_name, 
           ui.last_name, 
           u.email, 
           ui.mb_phone AS phone_number, 
           ui.org_type, 
           COUNT(*) AS Cases 
    FROM `cases` c 
    JOIN users u ON u.user_id = c.user_id  
    JOIN user_info ui ON ui.user_id = c.user_id 
    WHERE u.lf_id = ? 
      AND (
          c.user_id LIKE ?
          OR ui.first_name LIKE ?
          OR ui.last_name LIKE ?
          OR u.email LIKE ?
          OR ui.wrk_phone LIKE ?
          OR ui.mb_phone LIKE ?
          OR ui.home_phone LIKE ?
          OR ui.org_type LIKE ?
      )
    GROUP BY c.user_id

    UNION ALL

    SELECT null user_id, 
           pi.first_name, 
           pi.last_name, 
           pi.email, 
           pi.phone AS phone_number, 
           null org_type, 
           COUNT(c2.case_id) AS Cases 
    
    FROM `cases` c2
    JOIN party_info pi ON pi.id = c2.party_info_id 
    WHERE (
        pi.first_name <PERSON>I<PERSON> ?
        OR pi.last_name LIKE ?
        OR pi.email LIKE ?
        OR pi.phone LIKE ?
    )
    GROUP BY pi.email
) AS combined_result
