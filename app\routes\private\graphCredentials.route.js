import express from "express";
import {
  addGraphCredentials,
  getGraphCredentials,
  updateGraphCredentials,
  deleteGraphCredentials,
  getAllGraphCredentials,
  testGraphConnection,
  getProviderInfo
} from "#controllers/graphCredentials.controller.js";

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     GraphCredentials:
 *       type: object
 *       required:
 *         - lf_id
 *         - tenant_id
 *         - client_id
 *         - client_secret
 *       properties:
 *         lf_id:
 *           type: integer
 *           description: Law firm ID
 *         tenant_id:
 *           type: string
 *           description: Microsoft Entra ID tenant ID
 *         client_id:
 *           type: string
 *           description: Application (client) ID
 *         client_secret:
 *           type: string
 *           description: Client secret value
 *         is_active:
 *           type: boolean
 *           description: Whether credentials are active
 */

/**
 * @swagger
 * /private/graph-credentials:
 *   post:
 *     summary: Add Microsoft Graph credentials for a law firm
 *     description: Add Microsoft Graph API credentials for a law firm. Requires Admin or LawfirmSuperAdmin role.
 *     tags: [Graph Credentials]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/GraphCredentials'
 *     responses:
 *       201:
 *         description: Graph credentials added successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *       400:
 *         description: Bad request
 *       403:
 *         description: Permission denied
 */
router.route("/").post(addGraphCredentials);

/**
 * @swagger
 * /private/graph-credentials/all:
 *   get:
 *     summary: Get all law firms with Graph credentials (Admin only)
 *     description: Retrieve all law firms that have Microsoft Graph credentials configured. Admin only.
 *     tags: [Graph Credentials]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: All Graph credentials retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *       403:
 *         description: Permission denied
 */
router.route("/all").get(getAllGraphCredentials);

/**
 * @swagger
 * /private/graph-credentials/{lf_id}:
 *   get:
 *     summary: Get Microsoft Graph credentials for a law firm
 *     description: Retrieve Microsoft Graph credentials for a specific law firm. Excludes client secret.
 *     tags: [Graph Credentials]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: lf_id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Law firm ID
 *     responses:
 *       200:
 *         description: Graph credentials retrieved successfully
 *       404:
 *         description: Credentials not found
 *       403:
 *         description: Permission denied
 *   put:
 *     summary: Update Microsoft Graph credentials for a law firm
 *     description: Update Microsoft Graph credentials for a specific law firm. Requires Admin or LawfirmSuperAdmin role.
 *     tags: [Graph Credentials]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: lf_id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Law firm ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               tenant_id:
 *                 type: string
 *               client_id:
 *                 type: string
 *               client_secret:
 *                 type: string
 *               is_active:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Graph credentials updated successfully
 *       400:
 *         description: Bad request
 *       403:
 *         description: Permission denied
 *   delete:
 *     summary: Delete Microsoft Graph credentials for a law firm
 *     description: Deactivate Microsoft Graph credentials for a specific law firm. Requires Admin or LawfirmSuperAdmin role.
 *     tags: [Graph Credentials]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: lf_id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Law firm ID
 *     responses:
 *       200:
 *         description: Graph credentials deleted successfully
 *       400:
 *         description: Bad request
 *       403:
 *         description: Permission denied
 */
router.route("/:lf_id")
  .get(getGraphCredentials)
  .put(updateGraphCredentials)
  .delete(deleteGraphCredentials);

/**
 * @swagger
 * /private/graph-credentials/{lf_id}/test:
 *   post:
 *     summary: Test Microsoft Graph API connection
 *     description: Test the Microsoft Graph API connection for a law firm's credentials.
 *     tags: [Graph Credentials]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: lf_id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Law firm ID
 *     responses:
 *       200:
 *         description: Connection test completed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     sendgrid:
 *                       type: object
 *                     graph:
 *                       type: object
 *       403:
 *         description: Permission denied
 */
router.route("/:lf_id/test").post(testGraphConnection);

/**
 * @swagger
 * /private/graph-credentials/{lf_id}/provider-info:
 *   get:
 *     summary: Get mail provider information for a law firm
 *     description: Get information about which mail provider will be used for a law firm.
 *     tags: [Graph Credentials]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: lf_id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Law firm ID
 *     responses:
 *       200:
 *         description: Provider info retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     primaryProvider:
 *                       type: string
 *                     fallbackProvider:
 *                       type: string
 *                     graphConfigured:
 *                       type: boolean
 *       403:
 *         description: Permission denied
 */
router.route("/:lf_id/provider-info").get(getProviderInfo);

export default router;
