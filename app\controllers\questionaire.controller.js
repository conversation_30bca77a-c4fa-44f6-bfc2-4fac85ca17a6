import { questionaireService } from "#services";
import { catchAsync } from "#utils";
import { roles } from "#middlewares/roles.js";

export const createQuestionaire = catchAsync(async (req, res) => {
  try {
    const { qtn_name, tem_id, lf_id, price } = req.body;
    let questionaire = await questionaireService.createQuestionaire(
      qtn_name,
      tem_id,
      lf_id,
      price
    );
    return res.status(200).json({
      success: true,
      message: "Questionnaire created",
      questionaire_id: questionaire,
    });
  } catch (error) {
    return res.status(400).json({
      success: false,
      message: error.message,
    });
  }
});

export const getQuestionaire = catchAsync(async (req, res) => {
  const { id } = req.body;
  try {
    const questionaire = await questionaireService.getQuestionaire(id);
    return res.status(200).json({
      success: true,
      message: "Questionnaire fetched",
      questionaire,
    });
  } catch (error) {
    return res.status(400).json({
      success: false,
      message: error.message,
    });
  }
});

export const getAllQuestionaire = catchAsync(async (req, res) => {
  const role = req.user.role;
  if (
    role == roles.LawfirmSuperAdmin ||
    role == roles.LawfirmAdmin ||
    role == roles.LawfirmUser
  ) {
    try {
      const { page, size, keyword, status } = req.body;
      const result = await questionaireService.getAllQuestionaire(
        page,
        size,
        keyword,
        status,
        req.user.lf_id
      );
      return res.status(200).json({
        success: true,
        message: "ok",
        data: result,
      });
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  } else if (role == roles.Admin) {
    try {
      const { page, size, keyword, status, lf_id } = req.body;
      const result = await questionaireService.getAllQuestionaire(
        page,
        size,
        keyword,
        status,
        lf_id
      );
      return res.status(200).json({
        success: true,
        message: "ok",
        data: result,
      });
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

//=============================================================

export const deleteQuestionaire = catchAsync(async (req, res) => {
  const role = req.user.role;
  if (role == roles.Admin) {
    const { id } = req.body;
    try {
      const qtn = await questionaireService.deleteQuestionaire(id);
      return res.status(200).json({
        success: true,
        message: "Questionnaire deleted",
      });
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const updateQuestionaire = catchAsync(async (req, res) => {
  const role = req.user.role;
  if (role == roles.Admin || role == roles.LawfirmSuperAdmin) {
    let check = await questionaireService.getQuestionaire(req.body.id);
    if (check.length == 0) {
      return res.status(400).json({
        success: false,
        message: "Questionnaire does not exist",
      });
    } else {
      try {
        const qtn = await questionaireService.updateQuestionaire(
          req.body,
          req.user.role //lf_id
        );
        return res.status(200).json({
          success: true,
          message: "Questionnaire updated",
        });
      } catch (error) {
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const updateQuestionaireStatus = catchAsync(async (req, res) => {
  const role = req.user.role;
  // if (role) {
  if (role == roles.Admin || role == roles.LawfirmSuperAdmin) {
    let check = await questionaireService.getQuestionaire(req.body.id);
    if (check.length == 0) {
      return res.status(400).json({
        success: false,
        message: "Questionaire does not exist",
      });
    } else {
      try {
        const qtn = await questionaireService.updateQuestionaireStatus(
          req.body,
          req.user.role //lf_id
        );
        return res.status(200).json({
          success: true,
          message: "Questionaire updated",
        });
      } catch (error) {
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const createLink = catchAsync(async (req, res) => {
  const role = req.user.role;
  if (role == roles.Admin || role == roles.LawfirmSuperAdmin) {
    try {
      const questionnaire = await questionaireService.createLink(req.body);
      return res.status(200).json({
        success: true,
        message: "Link created",
        questionnaire,
      });
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const requestUpdateQuestionaire = catchAsync(async (req, res) => {
  const role = req.user.role;
  if (role == roles.LawfirmAdmin || role == roles.LawfirmUser) {
    let check = await questionaireService.getQuestionaire(req.body.qtn_id);
    if (Object.keys(check).length == 0) {
      return res.status(400).json({
        success: false,
        message: "Questionaire does not exist",
      });
    } else {
      if (check.status == 3) {
        return res.status(400).json({
          success: false,
          message: "One request per questionaire only",
        });
      } else if (check.lf_id != req.user.lf_id) {
        return res.status(500).json({
          success: false,
          message: "Permission denied!",
        });
      } else {
        try {
          const qtn = await questionaireService.requestUpdateQuestionaire(
            req.body,
            req.user.user_id,
            req.user.role
          );
          return res.status(200).json({
            success: true,
            message: "Questionaire update requested",
          });
        } catch (error) {
          return res.status(400).json({
            success: false,
            message: error.message,
          });
        }
      }
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const getCompletedRequests = catchAsync(async (req, res) => {
  const { id } = req.body;
  try {
    const requests = await questionaireService.findRequest(id, 0);
    return res.status(200).json({
      success: true,
      message: "Requests fetched",
      requests,
    });
  } catch (error) {
    return res.status(400).json({
      success: false,
      message: error.message,
    });
  }
});
export const modifyQuestion = catchAsync(async (req, res) => {
  const role = req.user.role;
  if (role == roles.Admin || role == roles.LawfirmSuperAdmin) {
    try {
      const questionnaire = await questionaireService.modifyQuestion(req.body);
      return res.status(200).json({
        success: true,
        message: "Question Edited",
        questionnaire,
      });
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});
export const findRequest = catchAsync(async (req, res) => {
  const role = req.user.role;
  if (role != roles.Client) {
    try {
      let check = await questionaireService.getQuestionaire(req.body.qtn_id);
      if (Object.keys(check).length == 0) {
        return res.status(400).json({
          success: false,
          message: "Questionnaire does not exist",
        });
      }
      if (check.lf_id != req.user.lf_id && role != roles.Admin) {
        return res.status(500).json({
          success: false,
          message: "Permission denied!",
        });
      }
      const request = await questionaireService.findRequest(
        req.body.qtn_id,
        req.body.status
      );
      return res.status(200).json({
        success: true,
        message: "Request fetched",
        request,
      });
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const assignClient = catchAsync(async (req, res) => {
  const role = req.user.role;
  if (role != roles.Client) {
    try {
      let check = await questionaireService.getQuestionaire(req.body.qtn_id);
      if (Object.keys(check).length == 0) {
        return res.status(400).json({
          success: false,
          message: "Questionaire does not exist",
        });
      }
      if (check.lf_id != req.user.lf_id && role != roles.Admin) {
        return res.status(500).json({
          success: false,
          message: "Permission denied!",
        });
      }
      const request = await questionaireService.findRequest(
        req.body.qtn_id,
        req.body.status
      );
      return res.status(200).json({
        success: true,
        message: "Request fetched",
        request,
      });
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const retractClient = catchAsync(async (req, res) => {
  const role = req.user.role;
  if (role != roles.Client) {
    try {
      let check = await questionaireService.getQuestionaire(req.body.qtn_id);
      if (Object.keys(check).length == 0) {
        return res.status(400).json({
          success: false,
          message: "Questionaire does not exist",
        });
      }
      if (check.lf_id != req.user.lf_id && role != roles.Admin) {
        return res.status(500).json({
          success: false,
          message: "Permission denied!",
        });
      }
      const request = await questionaireService.findRequest(
        req.body.qtn_id,
        req.body.status
      );
      return res.status(200).json({
        success: true,
        message: "Request fetched",
        request,
      });
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const getAllQuestionaireForPMS = catchAsync(async (req, res) => {
  try {
    const { page, size, keyword, status } = req.body;
    const result = await questionaireService.getAllQuestionaire(
      page,
      size,
      keyword,
      status,
      req.user.lf_id
    );
    return res.status(200).json({
      success: true,
      message: "ok",
      data: result,
    });
  } catch (error) {
    return res.status(400).json({
      success: false,
      message: error.message,
    });
  }
});

export const updateQuestionaireExportConfig = catchAsync(async (req, res) => {
  const role = req.user.role;
  if (role == roles.Admin || role == roles.LawfirmSuperAdmin) {
    const { id } = req.params;
    const { pms_enabled, data_enabled, form_enabled, form_names } = req.body;
    
    const exportConfig = {
      pms_enabled: Boolean(pms_enabled),
      data_enabled: Boolean(data_enabled),
      form_enabled: Boolean(form_enabled),
      form_names: form_names || ''
    };
    
    await questionaireService.updateQuestionaireExportConfig(id, exportConfig);
    
    return res.status(200).json({
      success: true,
      message: "Questionnaire export configuration updated successfully"
    });
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!"
    });
  }
});

export const getQuestionaireExportConfig = catchAsync(async (req, res) => {
  const { id } = req.params;
  const exportConfig = await questionaireService.getQuestionaireExportConfig(id);
  
  return res.status(200).json({
    success: true,
    data: exportConfig
  });
});

export const inheritTemplateExportConfig = catchAsync(async (req, res) => {
  const role = req.user.role;
  if (role == roles.Admin || role == roles.LawfirmSuperAdmin) {
    const { id } = req.params;
    
    // Get template export config for this questionnaire
    const con = await pool.getConnection();
    try {
      const result = await runQuery(con,
        `SELECT t.export_config 
         FROM questionaires q 
         JOIN templates t ON q.tem_id = t.id 
         WHERE q.id = ?`,
        [id]
      );
      
      if (result.length > 0 && result[0].export_config) {
        const templateConfig = typeof result[0].export_config === 'string'
          ? JSON.parse(result[0].export_config)
          : result[0].export_config;
        
        await questionaireService.updateQuestionaireExportConfig(id, templateConfig);
        
        return res.status(200).json({
          success: true,
          message: "Export configuration inherited from template successfully",
          data: templateConfig
        });
      } else {
        return res.status(404).json({
          success: false,
          message: "Template export configuration not found"
        });
      }
    } finally {
      con.destroy();
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!"
    });
  }
});

