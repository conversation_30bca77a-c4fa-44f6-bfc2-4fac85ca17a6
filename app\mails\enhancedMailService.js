import { mailProviderService } from "../services/mailProvider.service.js";
import <PERSON><PERSON>r<PERSON><PERSON><PERSON> from "#middlewares/ErrorClass.js";
import dotenv from "dotenv";

dotenv.config();

/**
 * Enhanced Mail Service
 * Provides all mail functions with Graph API support while maintaining SendGrid fallback
 */
export class EnhancedMailService {
  
  /**
   * Escape HTML content for security
   */
  escapeHtml(unsafe) {
    if (!unsafe) return '';
    return unsafe
      .replace(/&/g, "&amp;")
      .replace(/</g, "&lt;")
      .replace(/>/g, "&gt;")
      .replace(/"/g, "&quot;")
      .replace(/'/g, "&#039;")
      .replace(/\//g, "&#x2F;")
      .replace(/`/g, "&#x60;")
      .replace(/=/g, "&#x3D;");
  }

  /**
   * Generate standard email HTML template
   */
  generateEmailTemplate(options) {
    const {
      logoPath = "static/images/logo.png",
      logoStyle = "width: 80px; height: 80px;",
      bgColor = "rgba(213, 49, 49, 0.71)",
      textColor = "#fff",
      header = "Notification",
      body,
      footer = `<p>Regards,</p><p>Propero Team</p>`,
      buttonText,
      buttonLink
    } = options;

    return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Propero Email</title>
        <style>
            * { box-sizing: border-box; }
            body { margin: 0; padding: 0; font-family: 'Inter', sans-serif; background-color: #fff; }
            p { margin: 0; }
            .card { padding: 24px; width: 460px; border: 1px solid rgba(0, 0, 0, 0.75); }
            .card .logo-wrapper { text-align: center; align-items: center; margin-bottom: 20px; }
            .card .logo-wrapper .logo { max-width: 70px; height: auto; }
            .card .invitation-wrapper { background-color: #F5F5F5; height: 54px; padding: 0 8px; }
            .card .invitation-wrapper .invitation-text { line-height: 54px; color: #000; font-weight: 800; font-size: 14px; }
            .card .description-wrapper { margin: 8px; color: #94A3B8; font-size: 11px; }
            .card .invite-wrapper { color: #667085; font-size: 14px; line-height: 1.5; margin: 0 8px; gap: 4px; }
            .card .button-wrapper { margin: 32px 8px; }
            .card .regards-wrapper { margin: 8px; line-height: 1.5; color: #667085; font-size: 14px; }
            .password-box { background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 12px; margin: 16px 8px; font-family: 'Courier New', monospace; font-size: 16px; font-weight: bold; text-align: center; color: #495057; }
        </style>
    </head>
    <body>
        <div class="card">
            <div class="logo-wrapper">
                <img style="${logoStyle}" src="${process.env.IMAGE_URL}/${logoPath}" alt="Company Logo" class="logo">
            </div>
            <div class="invitation-wrapper">
                <p class="invitation-text">${header}</p>
            </div>
            <div class="description-wrapper">
                <p>This is an automatically generated email, please do not reply.</p>
            </div>
            <div class="invite-wrapper">
                ${body}
            </div>
            ${buttonText && buttonLink ? `
            <div class="button-wrapper">
                <a target="_blank" href="${buttonLink}" style="display: inline-block; padding: 10px 20px; font-size: 14px; color: ${textColor}; background-color: ${bgColor}; text-align: center; text-decoration: none; border-radius: 16px; width: 100%; text-underline-color: ${bgColor};">
                    ${buttonText}
                </a>
            </div>
            ` : ''}
            <div class="regards-wrapper">
                ${footer}
            </div>
        </div>
    </body>
    </html>`;
  }

  /**
   * Send email using the mail provider service
   */
  async sendMail(emailData, lf_id = null, user_email = null, options = {}) {
    try {
      const result = await mailProviderService.sendMail(emailData, lf_id, user_email, options);
      console.log(`Email sent via ${result.provider}:`, result.messageId);
      return result;
    } catch (error) {
      console.error('Enhanced mail service error:', error);
      throw ErrorHandler.badRequestError(`Failed to send email: ${error.message}`);
    }
  }

  /**
   * Enhanced Active Questionnaire Mail
   */
  async sendActiveQuestionnaireMail(email, first_name, qtn_id, lf_id = null, user_email = null) {
    const body = `
      <p>Hello ${this.escapeHtml(first_name)},</p>
      <p>Questionnaire ${this.escapeHtml(qtn_id)} has been activated.</p>
    `;

    const emailData = {
      to: email,
      from: { name: "Propero Support", email: "<EMAIL>" },
      subject: "Questionnaire has been activated",
      html: this.generateEmailTemplate({
        header: "Notification",
        body,
        buttonText: "Go to Questionnaire",
        buttonLink: `${process.env.CLIENT_URL}/questionnaires/${qtn_id}`
      })
    };

    return await this.sendMail(emailData, lf_id, user_email);
  }

  /**
   * Enhanced Update Questionnaire Mail
   */
  async sendUpdateQuestionnaireMail(email, first_name, qtn_name, qtn_id, lf_id = null, user_email = null) {
    const body = `
      <p>Hello ${this.escapeHtml(first_name)},</p>
      <p>Questionnaire "${this.escapeHtml(qtn_name)}" has been updated.</p>
    `;

    const emailData = {
      to: email,
      from: { name: "Propero Support", email: "<EMAIL>" },
      subject: "Questionnaire has been edited",
      html: this.generateEmailTemplate({
        header: "Notification",
        body,
        buttonText: "View Questionnaire",
        buttonLink: `${process.env.CLIENT_URL}/questionnaires/${qtn_id}`
      })
    };

    return await this.sendMail(emailData, lf_id, user_email);
  }

  /**
   * Enhanced Submit Case Mail
   */
  async sendSubmitCaseMail(email, case_id, first_name, lf_id = null, user_email = null) {
    const body = `
      <p>Hello ${this.escapeHtml(first_name)},</p>
      <p>Your case ${this.escapeHtml(case_id)} has been submitted successfully.</p>
    `;

    const emailData = {
      to: email,
      from: { name: "Propero Support", email: "<EMAIL>" },
      subject: "Case has been submitted",
      html: this.generateEmailTemplate({
        header: "Notification",
        body,
        buttonText: "View Case",
        buttonLink: `${process.env.CLIENT_URL}/cases/${case_id}`
      })
    };

    return await this.sendMail(emailData, lf_id, user_email);
  }

  /**
   * Enhanced Case Modification Mail
   */
  async sendCaseModificationMail(email, first_name, case_id, modifications, lf_id = null, user_email = null) {
    // Generate modifications list
    let modificationsHtml = '';
    if (modifications && modifications.length > 0) {
      modificationsHtml = '<div style="margin: 20px 0;"><h3>The following modifications have been made:</h3><ul>';
      modifications.forEach(mod => {
        modificationsHtml += `
          <li style="margin: 10px 0; padding: 10px; background-color: #f5f5f5; border-left: 4px solid #007bff;">
            <strong>Question:</strong> ${this.escapeHtml(mod.question)}<br>
            <strong>Your answer:</strong> ${this.escapeHtml(mod.originalAnswer)}<br>
            <strong>Modified answer:</strong> ${this.escapeHtml(mod.newAnswer)}
          </li>
        `;
      });
      modificationsHtml += '</ul></div>';
    }

    const body = `
      <p>Hello ${this.escapeHtml(first_name)},</p>
      <p>Thank you for submitting answers to our questionnaire. We have made the following modifications to your case:</p>
      ${modificationsHtml}
      <p>If you have any questions about these changes, please don't hesitate to contact us.</p>
    `;

    const emailData = {
      to: email,
      from: { name: "Propero Support", email: "<EMAIL>" },
      subject: `Your case ${case_id} has been updated`,
      html: this.generateEmailTemplate({
        header: "Case Update Notification",
        body
      })
    };

    return await this.sendMail(emailData, lf_id, user_email);
  }

  /**
   * Enhanced Submit Case Client Mail
   */
  async sendSubmitCaseClientMail(email, first_name, customOptions = {}, lf_id = null, user_email = null) {
    const {
      title = "Your case has been sent",
      body: customBody,
      bgColor,
      textColor,
      logo,
      path,
      header,
      footer,
      user
    } = customOptions;

    let body = customBody || `
      <div class="invite-wrapper">
        <p>Hello ${this.escapeHtml(first_name)},</p>
        <p>Your case has been sent.</p>
      </div>
    `;

    // Replace user variables if provided
    if (user) {
      for (let key in user) {
        const value = user[key] != null ? user[key] : "";
        body = body.replaceAll(`\${${key}}`, value);
        body = body.replaceAll(`{{${key}}}`, value);
        body = body.replaceAll(`{${key}}`, value);
        body = body.replaceAll(`[${key}]`, value);
      }
    }

    const emailData = {
      to: email,
      from: { name: "Propero Support", email: "<EMAIL>" },
      subject: title,
      html: this.generateEmailTemplate({
        header: header || "Notification",
        body,
        footer: footer || `<p>Best regards,</p><p>Legal Workflow Limited Team</p>`,
        logoPath: path || "static/images/logo.png",
        logoStyle: logo || "width: 80px; height: 80px;",
        bgColor: bgColor || "rgba(213, 49, 49, 0.71)",
        textColor: textColor || "#fff"
      })
    };

    return await this.sendMail(emailData, lf_id, user_email);
  }

  /**
   * Enhanced Call Me Mail
   */
  async sendCallMeMail(email, first_name, phone_number, best_time, lf_id = null, user_email = null) {
    const body = `
      <p>Hello ${this.escapeHtml(first_name)},</p>
      <p>We have received your callback request.</p>
      <p><strong>Phone Number:</strong> ${this.escapeHtml(phone_number)}</p>
      <p><strong>Best Time to Call:</strong> ${this.escapeHtml(best_time)}</p>
      <p>We will contact you as soon as possible.</p>
    `;

    const emailData = {
      to: email,
      from: { name: "Propero Support", email: "<EMAIL>" },
      subject: "Callback Request",
      html: this.generateEmailTemplate({
        header: "Callback Request",
        body
      })
    };

    return await this.sendMail(emailData, lf_id, user_email);
  }

  /**
   * Enhanced Temp Password Mail
   */
  async sendTempPasswordMail(email, password, first_name, lf_id = null, user_email = null) {
    const body = `
      <p>Hello ${this.escapeHtml(first_name)},</p>
      <p>Your new temporary password has been generated. Please use the password below to log in and change it immediately for security purposes.</p>
      <div class="password-box">${this.escapeHtml(password)}</div>
    `;

    const emailData = {
      to: email,
      from: { name: "Propero Support", email: "<EMAIL>" },
      subject: "Propero new password",
      html: this.generateEmailTemplate({
        header: "New Password",
        body,
        buttonText: "Login to Propero",
        buttonLink: `${process.env.FRONTEND_URL}/login`
      })
    };

    return await this.sendMail(emailData, lf_id, user_email);
  }

  /**
   * Enhanced Case Mail (with full customization support)
   */
  async sendCaseMail(email, link, first_name, customOptions = {}, lf_id = null, user_email = null) {
    const {
      path,
      bgColor,
      textColor,
      title,
      header,
      body: customBody,
      footer,
      logo,
      user
    } = customOptions;

    let title_final = title || "Your new case is ready";
    let header_final = header || `<p class="invitation-text">Invitation</p>`;
    let body = customBody || `
      <p>Hello ${this.escapeHtml(first_name)},</p>
      <p>You are invited to complete a Case. Click the following button to review:</p>
    `;
    let footer_final = footer || `<p>Regards,</p><p>Propero Team</p>`;

    // Replace user variables if provided
    if (user) {
      console.log('🔍 User data for variable replacement:', user);
      for (let key in user) {
        const value = user[key] != null ? user[key] : "";
        console.log(`🔄 Replacing ${key} with: "${value}"`);

        // Replace in each variable individually
        body = body.replaceAll(`\${${key}}`, value);
        body = body.replaceAll(`{{${key}}}`, value);
        body = body.replaceAll(`{${key}}`, value);
        body = body.replaceAll(`[${key}]`, value);

        header_final = header_final.replaceAll(`\${${key}}`, value);
        header_final = header_final.replaceAll(`{{${key}}}`, value);
        header_final = header_final.replaceAll(`{${key}}`, value);
        header_final = header_final.replaceAll(`[${key}]`, value);

        footer_final = footer_final.replaceAll(`\${${key}}`, value);
        footer_final = footer_final.replaceAll(`{{${key}}}`, value);
        footer_final = footer_final.replaceAll(`{${key}}`, value);
        footer_final = footer_final.replaceAll(`[${key}]`, value);

        title_final = title_final.replaceAll(`\${${key}}`, value);
        title_final = title_final.replaceAll(`{{${key}}}`, value);
        title_final = title_final.replaceAll(`{${key}}`, value);
        title_final = title_final.replaceAll(`[${key}]`, value);
      }
      console.log('✅ After replacement - Body:', body);
      console.log('✅ After replacement - Header:', header_final);
    } else {
      console.log('⚠️  No user data provided for variable replacement');
    }

    const emailData = {
      to: email,
      from: { name: "Propero Support", email: "<EMAIL>" },
      subject: title_final,
      html: this.generateEmailTemplate({
        header: header_final,
        body,
        footer: footer_final,
        buttonText: "Go to Questionnaire",
        buttonLink: link,
        logoPath: path || "static/images/logo.png",
        logoStyle: logo || "width: 80px; height: 80px;",
        bgColor: bgColor || "rgba(213, 49, 49, 0.71)",
        textColor: textColor || "#fff"
      })
    };

    return await this.sendMail(emailData, lf_id, user_email);
  }

  /**
   * Enhanced Chaser Mail
   */
  async sendChaserMail(email, link, first_name, customOptions = {}, lf_id = null, user_email = null) {
    const {
      path,
      bgColor,
      textColor,
      title,
      header,
      body: customBody,
      footer,
      logo,
      user
    } = customOptions;

    let title_final = title || "Reminder: Your case needs attention";
    let header_final = header || `<p class="invitation-text">Notification</p>`;
    let body = customBody || `
      <p>Hello ${this.escapeHtml(first_name)},</p>
      <p>You have a case that hasn't been responded to yet. Please click the button below to view it.</p>
    `;
    let footer_final = footer || `<p>Best regards,</p><p>Legal Workflow Limited Team</p>`;

    // Replace user variables if provided
    if (user) {
      for (let key in user) {
        const value = user[key] != null ? user[key] : "";
        // Replace in each variable individually
        body = body.replaceAll(`\${${key}}`, value);
        body = body.replaceAll(`{{${key}}}`, value);
        body = body.replaceAll(`{${key}}`, value);
        body = body.replaceAll(`[${key}]`, value);

        header_final = header_final.replaceAll(`\${${key}}`, value);
        header_final = header_final.replaceAll(`{{${key}}}`, value);
        header_final = header_final.replaceAll(`{${key}}`, value);
        header_final = header_final.replaceAll(`[${key}]`, value);

        footer_final = footer_final.replaceAll(`\${${key}}`, value);
        footer_final = footer_final.replaceAll(`{{${key}}}`, value);
        footer_final = footer_final.replaceAll(`{${key}}`, value);
        footer_final = footer_final.replaceAll(`[${key}]`, value);

        title_final = title_final.replaceAll(`\${${key}}`, value);
        title_final = title_final.replaceAll(`{{${key}}}`, value);
        title_final = title_final.replaceAll(`{${key}}`, value);
        title_final = title_final.replaceAll(`[${key}]`, value);
      }
    }

    const emailData = {
      to: email,
      from: { name: "Propero Support", email: "<EMAIL>" },
      subject: title_final,
      html: this.generateEmailTemplate({
        header: header_final,
        body,
        footer: footer_final,
        buttonText: "View Case",
        buttonLink: link,
        logoPath: path || "static/images/logo.png",
        logoStyle: logo || "width: 80px; height: 80px;",
        bgColor: bgColor || "rgba(213, 49, 49, 0.71)",
        textColor: textColor || "#fff"
      })
    };

    return await this.sendMail(emailData, lf_id, user_email);
  }
}

export const enhancedMailService = new EnhancedMailService();
