import { smsService } from "#services";
import { catchAsync } from "#utils";
import countryCodes from "country-codes-list";
import { userService } from "#services";
import { roles } from "#middlewares/roles.js";
import { caseService } from "#services";
const getCountryCode = (countryName) => {
  const data = countryCodes
    .all()
    .find((country) => country.countryNameEn === countryName);
  if (data) {
    return data.countryCallingCode;
  } else {
    return null; // Country not found
  }
};

export const sendOTP_SMS = catchAsync(async (req, res) => {
  let { token, email } = req.body;
  try {
    if (token != null) {
      const userInfo = await smsService.getInfo(token);
      if (!userInfo) {
        if (process.env.NODE_ENV != "development") {
          return res.status(200).json({
            success: true,
            message: "SMS OTP sent successfully",
          });
        } else {
          return res.status(200).json({
            success: true,
            message: "SMS OTP sent successfully",
            result,
          });
        }
      }
      let country_code = getCountryCode(userInfo.country);
      const result = await smsService.sendOTP_SMS(
        userInfo.email,
        country_code ?? userInfo.country,
        userInfo.phone
      );
      if (!result) {
        return res.status(500).json({
          success: false,
          message: "Error sending OTP!",
        });
      } else {
        if (process.env.NODE_ENV != "development") {
          return res.status(200).json({
            success: true,
            message: "SMS OTP sent successfully",
          });
        } else {
          return res.status(200).json({
            success: true,
            message: "SMS OTP sent successfully",
            result,
          });
        }
      }
    } else {
      email = email.toLowerCase();
      let user = await userService.findUserLawfirm(email);
      if (user.length == 0) {
        return res.status(200).json({
          success: false,
          message: "No user found",
        });
      }
      let country_code = getCountryCode(user[0].country);
      let phone = user[0].mb_phone
        ? user[0].mb_phone
        : user[0].wrk_phone
          ? user[0].wrk_phone
          : user[0].home_phone;
      const result = await smsService.sendOTP_SMS(
        user[0].email,
        country_code,
        phone
      );
      if (!result) {
        return res.status(500).json({
          success: false,
          message: "Error sending OTP!",
        });
      } else {
        if (process.env.NODE_ENV != "development") {
          return res.status(200).json({
            success: true,
            message: "SMS OTP sent successfully",
          });
        } else {
          return res.status(200).json({
            success: true,
            message: "SMS OTP sent successfully",
            result,
          });
        }

      }
    }
  } catch (error) {
    console.log(error);
    return res.status(error.status).json({
      success: false,
      message: "Error sending OTP!",
    });
  }
});

export const verifyOTP_SMS = catchAsync(async (req, res) => {
  let { token, email } = req.body;
  try {
    if (token != null) {
      const userInfo = await smsService.getInfo(token);
      const result = await smsService.verifyOTP_SMS(
        userInfo.email,
        userInfo.phone,
        req.body.otp,
        token
      );
      if (!result) {
        return res.status(500).json({
          success: false,
          message: "Error verifying OTP",
        });
      } else {
        await smsService.deleteOTP(userInfo.email, userInfo.phone);
        return res.status(200).json({
          success: true,
          message: "OTP verified successfully",
          result,
        });
      }
    } else {
      email = email.toLowerCase();
      let user = await userService.findUserLawfirm(email);
      if (user.length == 0) {
        return res.status(500).json({
          success: false,
          message: "User not found",
        });
      }
      let phone = user[0].mb_phone
        ? user[0].mb_phone
        : user[0].wrk_phone
          ? user[0].wrk_phone
          : user[0].home_phone;
      const result = await smsService.verifyOTP_SMS(
        user[0].email,
        phone,
        req.body.otp,
        email
      );
      if (!result) {
        return res.status(500).json({
          success: false,
          message: "Error verifying OTP",
        });
      } else {
        await smsService.deleteOTP(email, phone);
        return res.status(200).json({
          success: true,
          message: "OTP verified successfully",
          result,
        });
      }
    }
  } catch (error) {
    console.log(error);
    return res.status(500).json({
      success: false,
      message: error.message,
    });
  }
});

export const sendLinkCaseVerify = catchAsync(async (req, res) => {
  const role = req.user.role;
  let { case_id } = req.body;
  if (
    role == roles.LawfirmSuperAdmin ||
    role == roles.LawfirmAdmin ||
    role == roles.LawfirmUser
  ) {
    try {
      const userInfo = await smsService.getInfo(case_id);
      if (req.user.lf_id != userInfo.lf_id) {
        return res.status(500).json({
          success: false,
          message: "Permission denied!",
        });
      }
      let result = await smsService.sendLinkCaseVerify(
        userInfo.first_name,
        userInfo.email,
        userInfo.phone.slice(-4),
        case_id,
        userInfo.lf_id,
        userInfo.user_id,
        req.user
      );
      if (!result) {
        return res.status(500).json({
          success: false,
          message: "Error sending email",
        });
      } else {
        await caseService.updateCaseStatus(4, case_id, req.user.user_id);
        return res.status(200).json({
          success: true,
          message: "Send link successfully",
          link: result,
        });
      }
    } catch (error) {
      console.log(error);
      return res.status(500).json({
        success: false,
        message: "Error sending email",
        error: error,
      });
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const sendOTPLogin = catchAsync(async (req, res) => {
  let { email, token } = req.body;
  let user = await userService.findUserLawfirm(email.toLowerCase());
  let country_code = getCountryCode(user[0].country);
  let phone = user[0].wrk_phone
    ? user[0].wrk_phone
    : user[0].mb_phone
      ? user[0].mb_phone
      : user[0].home_phone;
  const result = await smsService.sendOTPLogin(
    user[0].user_id,
    email.toLowerCase(),
    country_code,
    phone,
    token
  );
  if (!result) {
    return res.status(500).json({
      success: false,
      message: "Error sending OTP!",
    });
  } else {
    if (process.env.NODE_ENV != "development") {
      return res.status(200).json({
        success: true,
        message: "SMS OTP sent successfully",
      });
    } else {
      return res.status(200).json({
        success: true,
        message: "SMS OTP sent successfully",
        result,
      });
    }
  }
});

export const verifyOTPLogin = catchAsync(async (req, res) => {
  let { email, otp, token } = req.body;
  let user = await userService.findUserLawfirm(email.toLowerCase());
  let phone = user[0].wrk_phone
    ? user[0].wrk_phone
    : user[0].mb_phone
      ? user[0].mb_phone
      : user[0].home_phone;
  const result = await smsService.verifyOTPLogin(
    user[0].user_id,
    email.toLowerCase(),
    phone,
    otp,
    token
  );
  if (!result) {
    return res.status(500).json({
      success: false,
      message: "Error verifying OTP",
    });
  } else {
    await smsService.deleteOTP(phone);
    return res.status(200).json({
      success: true,
      message: "OTP verified successfully",
      result,
    });
  }
});
