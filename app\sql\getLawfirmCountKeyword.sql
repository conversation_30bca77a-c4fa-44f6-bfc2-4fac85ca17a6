 SELECT
        COUNT(*)
    FROM law_firm l
    JOIN users u ON l.lf_id = u.lf_id
    JOIN user_info ui ON u.user_id = ui.user_id
    JOIN `status` s 
    ON s.id = l.status
    WHERE u.is_owner = 1
    AND (ui.first_name like ?
    OR ui.last_name like ?
    OR u.email like ?
    OR ui.country like ?
    OR s.name like ?
    OR COALESCE(NULLIF(ui.mb_phone, ''), NULLIF(ui.wrk_phone, ''), NULLIF(ui.home_phone, '')) like ?
    OR l.lf_org_name like ?
    OR l.lf_id like ?
    )