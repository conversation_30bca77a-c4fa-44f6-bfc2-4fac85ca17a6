const predefinedMapping = { 
    "1": {
         "1": {
             "mtno": "130000",
             "type": "party",
             "data": {
                 "Forename":"First name",
                 "Surname":"Surname",
                 "PAFORE":"First name",
                 "PASURN":"Surname",
                 "Title":"Title",
                 "PATITL":"Title",
                 "PAADD1": "Address line 1",
                 "PAADD2": "Address line 2",
                 "PAADD3": "Address line 3",
                 "PAADD4": "Locality",
                 "PAADD5": "Town",
                 "PAADD6": "County",
                 "PAPOST": "Postcode"
             }
         },
         "2": {
             "mtno": "130001",
             "type": "table",
         },
         "3": {
             "mtno": "130002",
             "type": "table"
         },
         "4": {
             "mtno": "13003",
             "type": "table",
             "data": {
              "1391": "First name",
              "1393": "Surname",
              "1394": "Address line 1",
              "1395": "Address line 2",
              "1396": "Address line 3",
              "1397": "Locality",
              "1398": "Town",
              "1399": "County",
              "1400": "Postcode",
              "1401": "Amount of legacy"
             }
         }
     },
     "2":{
         "1": {
             "mtno": "130004",
             "type": "table"
         },
         "2": {
             "mtno": "130005",
             "type": "table"
         },
         "3": {
             "mtno": "130006",
             "type": "party",
             "data": {
                 "Forename":"First name",
                 //"Surname":"Surname",
                 "PAFORE":"First name",
                 //"PASURN":"Surname",
                 "Title":"Title",
                 "PATITL":"Title",
                 "PAADD1": "Address line 1",
                 "PAADD2": "Address line 2",
                 "PAADD3": "Address line 3",
                 "PAADD4": "Locality",
                 "PAADD5": "Town",
                 "PAADD6": "County",
                 "PAPOST": "Postcode"
             }
         }
     },
     "3":{
         "1": {
             "mtno": "130007",
             "type": "table"
         },
         "2":{
             "mtno": "130008",
             "type":"party",
             "data": {
                 "Forename":"First name",
                 "Surname":"Surname",
                 "PAFORE":"First name",
                 "PASURN":"Surname",
                 "Title":"Title",
                 "PATITL":"Title",
                 "PAADD1": "Address line 1",
                 "PAADD2": "Address line 2",
                 "PAADD3": "Address line 3",
                 "PAADD4": "Locality",
                 "PAADD5": "Town",
                 "PAADD6": "County",
                 "PAPOST": "Postcode"
             }
         }
     },
     "4":{
         "1": {
             "mtno": "130009",
             "type": "table"
         },
         "2":{
             "mtno": "130010",
             "type":"party",
             "data": {
                 "Forename":"First name",
                 "Surname":"Surname",
                 "PAFORE":"First name",
                 "PASURN":"Surname",
                 "Title":"Title",
                 "PATITL":"Title",
                 "PAADD1": "Address line 1",
                 "PAADD2": "Address line 2",
                 "PAADD3": "Address line 3",
                 "PAADD4": "Locality",
                 "PAADD5": "Town",
                 "PAADD6": "County",
                 "PAPOST": "Postcode"
             }
         }
     },
     "5":{
         "1": {
             "mtno": "130011",
             "type": "party",
             "data": {
                 //"Forename":"First name",
                 "Surname":"Surname",
                 "PAFORE":"First name",
                 "PASURN":"Surname",
                 "Title":"Title",
                 "PATITL":"Title",
                 "PAADD1": "Address line 1",
                 "PAADD2": "Address line 2",
                 "PAADD3": "Address line 3",
                 "PAADD4": "Locality",
                 "PAADD5": "Town",
                 "PAADD6": "County",
                 "PAPOST": "Postcode"
             }
         },
         "2": {
             "mtno": "130012",
             "type": "table"
         },
         "3": {
             "mtno": "130013",
             "type": "table"
         },
         "4": {
             "mtno": "13014",
             "type": "party",
             "data": {
              //"1391": "First name",
              "1393": "Surname",
              "1394": "Address line 1",
              "1395": "Address line 2",
              "1396": "Address line 3",
              "1397": "Locality",
              "1398": "Town",
              "1399": "County",
              "1400": "Postcode",
              "1401": "Amount of legacy"
             }
         }
     }
 };
 
 const transformData = (data) => {
     const result = [];
 
     if (data.group && Array.isArray(data.group)) {
         data.group.forEach(group => {
             if (group.questions && Array.isArray(group.questions)) {
                 group.questions.forEach(questionItem => {
                     const questionText = questionItem.question.text;
                     const mapping = predefinedMapping[group.group_name]?.[questionText];
 
                     if (mapping) {
                         const answers = questionItem.question.answer;
                         let transformedAnswers;
                         if (Array.isArray(answers)) {
                             transformedAnswers = answers.map(answer => {
                                 const transformedAnswer = {};
                                 if (mapping.data) {
                                     for (const key in mapping.data) {
                                         const mapValue = mapping.data[key];
                                         if (answer[mapValue]) {
                                             transformedAnswer[key] = answer[mapValue];
                                         }
                                     }
                                 } else {
                                     Object.assign(transformedAnswer, answer);
                                 }
                                 return transformedAnswer;
                             });
                         } else if (typeof answers === 'object' && answers !== null) {
                             transformedAnswers = {};
                             if (mapping.data) {
                                 for (const key in mapping.data) {
                                     const mapValue = mapping.data[key];
                                     console.log(answers)
                                     console.log(mapping.data)
                                     if (answers[mapValue]) {
                                         transformedAnswers[key] = answers[mapValue];
                                     }
                                 }
                             } else {
                                 Object.assign(transformedAnswers, answers);
                             }
                         } else {
                             transformedAnswers = answers;
                         }
                         result.push({
                             case_id: data.case_id,
                             mtno: mapping.mtno,
                             sequence: mapping.sequence,
                             type: mapping.type || null,
                             ans_type: questionItem.question.ans_type,
                             transformed_data: transformedAnswers
                         });
                     } else {
                         result.push({
                             mtno: null,
                             sequence: null,
                             type: null,
                             ans_type: questionItem.question.ans_type,
                             transformed_data: questionItem.question.answer
                         });
                     }
                 });
             }
         });
     }
 
     return result;
 };
 
 export const code = (inputs) => {
     return { data: transformData(inputs.data) };
 };


let input = {
    "data": {
      "group": [
        {
          "questions": [
            {
              "question": {
                "text": "1",
                "answer": [
                  {
                    "Town": "Herne Bay",
                    "Qname": "Will client contact details 1",
                    "County": "Kent",
                    "Surname": "Red",
                    "Locality": "",
                    "Postcode": "CT6 5LQ",
                    "First name": "Gail",
                    "Occupation": "",
                    "Middle names": "test",
                    "Date of birth": "2025-02-07T15:23:00.269Z",
                    "Email address": "<EMAIL>",
                    "Address line 1": "71 High Street",
                    "Address line 2": "Herne Bay",
                    "Address line 3": "Kent",
                    "Home telephone number": "",
                    "Work telephone number": "",
                    "Mobile telephone number": "",
                    "National insurance number": ""
                  }
                ],
                "ans_type": "10"
              }
            },
            {
              "question": {
                "text": "2",
                "answer": "Burial",
                "ans_type": "6"
              }
            },
            {
              "question": {
                "text": "3",
                "answer": "Yes",
                "ans_type": "6"
              }
            },
            {
              "question": {
                "text": "4",
                "answer": [
                  {
                    "Town": "Birmingham",
                    "Qname": "Cash legatee 1",
                    "County": "West Midlands",
                    "Surname": "Crumb",
                    "Locality": "",
                    "Postcode": "B6 7YU",
                    "First name": "Victor",
                    "Middle names": "",
                    "Address line 1": "Pudding Lane",
                    "Address line 2": "Birmingham",
                    "Address line 3": "",
                    "Amount of legacy": "7500.00"
                  }
                ],
                "ans_type": "10"
              }
            }
          ],
          "group_name": "1"
        },
        {
          "questions": [
            {
              "question": {
                "text": "1",
                "answer": "Yes",
                "ans_type": "6"
              }
            },
            {
              "question": {
                "text": "2",
                "answer": "Yes",
                "ans_type": "6"
              }
            },
            {
              "question": {
                "text": "3",
                "answer": [
                  {
                    "Town": "CHICHESTRE",
                    "Qname": "Will executors 1",
                    "County": "WEST SUSSEX",
                    "Surname": "Walsh",
                    "Postcode": "PO202HT",
                    "First name": "lauren",
                    "Middle names": "",
                    "Address line 1": "87a Cheshire crescent"
                  },
                  {
                    "Town": "T5",
                    "Qname": "Will executors 2",
                    "County": "T6",
                    "Surname": "walsh",
                    "Locality": "t4",
                    "Postcode": "T3",
                    "First name": "terry",
                    "Middle names": "",
                    "Address line 1": "test",
                    "Address line 2": "test2",
                    "Address line 3": "test3"
                  }
                ],
                "ans_type": "10"
              }
            }
          ],
          "group_name": "2"
        },
        {
          "questions": [
            {
              "question": {
                "text": "1",
                "answer": "Yes",
                "ans_type": "6"
              }
            },
            {
              "question": {
                "text": "2",
                "answer": "No",
                "ans_type": "6"
              }
            }
          ],
          "group_name": "3"
        },
        {
          "questions": [
            {
              "question": {
                "text": "1",
                "answer": null,
                "ans_type": "6"
              }
            },
            {
              "question": {
                "text": "2",
                "answer": null,
                "ans_type": "10"
              }
            }
          ],
          "group_name": "4"
        },
        {
          "questions": [
            {
              "question": {
                "text": "1",
                "answer": [
                  {
                    "Town": "t5",
                    "Qname": "Will client personal details 1",
                    "County": "t6",
                    "Surname": "miles",
                    "Locality": "t4",
                    "Postcode": "T7",
                    "First name": "jamie",
                    "Middle names": "",
                    "Address line 1": "t1",
                    "Address line 2": "t2",
                    "Address line 3": "t3"
                  }
                ],
                "ans_type": "10"
              }
            },
            {
              "question": {
                "text": "2",
                "answer": "Burial",
                "ans_type": "6"
              }
            },
            {
              "question": {
                "text": "3",
                "answer": "Yes",
                "ans_type": "6"
              }
            },
            {
              "question": {
                "text": "4",
                "answer": [
                  {
                    "Town": "Birmingham",
                    "Qname": "Cash legatee 1",
                    "County": "West Midlands",
                    "Surname": "Crumb",
                    "Locality": "",
                    "Postcode": "B6 7YU",
                    "First name": "Victor",
                    "Middle names": "",
                    "Address line 1": "Pudding Lane",
                    "Address line 2": "Birmingham",
                    "Address line 3": "",
                    "Amount of legacy": "9000.00"
                  }
                ],
                "ans_type": "10"
              }
            }
          ],
          "group_name": "5"
        }
      ]
    }
  }

console.log(code(input))