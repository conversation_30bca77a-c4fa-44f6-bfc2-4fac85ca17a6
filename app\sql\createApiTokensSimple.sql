-- Simple version without foreign key constraints (in case of reference issues)
CREATE TABLE IF NOT EXISTS api_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    lf_id INT NOT NULL,
    user_id INT NOT NULL,
    token_name VARCHAR(255) NOT NULL,
    token_hash VARCHAR(255) NOT NULL,
    token_prefix VARCHAR(16) NOT NULL,
    permissions JSON DEFAULT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    expires_at TIMESTAMP NULL,
    last_used_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    
    UNIQUE KEY unique_token_hash (token_hash),
    UNIQUE KEY unique_token_prefix (token_prefix),
    KEY idx_lf_id (lf_id),
    KEY idx_user_id (user_id),
    KEY idx_active (is_active),
    KEY idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='API tokens for firm-level access by AdminAPI users';
