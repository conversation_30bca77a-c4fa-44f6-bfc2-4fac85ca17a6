import sgMail from "@sendgrid/mail";
import dotenv from "dotenv";

dotenv.config();
export const chaserMail = async (
    email,
    link,
    first_name,
    path,
    bgColor,
    textColor,
    title,
    header,
    body,
    footer,
    logo,
    user
) => {
    if (!logo) {
        logo = "width: 80px; height: 80px;"
    }
    if (!title) {
        title = `Your new case is ready`
    }
    if (!header) {
        header = `                 
            <p class="invitation-text">Notification</p>
        `;
    }
    if (!body) {
        body = `                    
        <p>
            Hello ${first_name},
        </p>
        <p>
            You have a case that hasn't been responded to yet. Please click the button below to view it.
        </p>`;
    }
    if (!footer) {
        footer = `                    
        <p>
            Best regards,
        </p>
        <p>
            Legal Workflow Limited Team
        </p>`;
    }
    if (!bgColor) {
        bgColor = "rgba(213, 49, 49, 0.71)";
    }
    if (!textColor) {
        textColor = "#fff";
    }
    if (!path) {
        path = "static/images/logo.png";
    }
    for (let key in user) {
        body = body.replaceAll(`\${${key}}`, user[key] != null ? user[key] : "");
        body = body.replaceAll(`{{${key}}}`, user[key] != null ? user[key] : "");
        body = body.replaceAll(`{${key}}`, user[key] != null ? user[key] : "");
        body = body.replaceAll(`[${key}]`, user[key] != null ? user[key] : "");
        header = header.replaceAll(`\${${key}}`, user[key] != null ? user[key] : "");
        header = header.replaceAll(`{{${key}}}`, user[key] != null ? user[key] : "");
        header = header.replaceAll(`{${key}}`, user[key] != null ? user[key] : "");
        header = header.replaceAll(`[${key}]`, user[key] != null ? user[key] : "");
        footer = footer.replaceAll(`\${${key}}`, user[key] != null ? user[key] : "");
        footer = footer.replaceAll(`{{${key}}}`, user[key] != null ? user[key] : "");
        footer = footer.replaceAll(`{${key}}`, user[key] != null ? user[key] : "");
        footer = footer.replaceAll(`[${key}]`, user[key] != null ? user[key] : "");
        title = title.replaceAll(`\${${key}}`, user[key] != null ? user[key] : "");
        title = title.replaceAll(`{{${key}}}`, user[key] != null ? user[key] : "");
        title = title.replaceAll(`{${key}}`, user[key] != null ? user[key] : "");
        title = title.replaceAll(`[${key}]`, user[key] != null ? user[key] : "");
    }
    sgMail.setApiKey(process.env.SENDGRID_API_KEY);
    let msg = {};
    msg = {
        from: { name: "Propero Support", email: "<EMAIL>" },
        to: email,
        subject: `${title}`,
        html: `<!DOCTYPE html>
        <html lang="en">

        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Document</title>
            <style>
                * {
                    box-sizing: border-box;
                }

                body {
                    margin: 0;
                    padding: 0;
                    font-family: 'Inter', sans-serif;
                    background-color: #fff;
                }

                p {
                    margin: 0;
                }

                .card {
                    padding: 24px;
                    width: 460px;
                    border: 1px solid rgba(0, 0, 0, 0.75);
                }
                .card .logo {
                    width: 80px;
                    height: 80px;
                }
                .card .logo-wrapper {
                    text-align: center;
                    align-items: center;
                    margin-bottom: 20px;
                }

                .card .invitation-wrapper {
                    background-color: #F5F5F5;
                    height: 54px;
                    padding: 0 8px;
                }

                .card .invitation-wrapper .invitation-text {
                    line-height: 54px;
                    color: #000;
                    font-weight: 800;
                    font-size: 14px;
                }

                .card .description-wrapper {
                    margin: 8px;
                    color: #94A3B8;
                    font-size: 11px;
                }

                .card .invite-wrapper {
                    color: #667085;
                    font-size: 14px;
                    line-height: 1.5;
                    margin: 0 8px;
                    gap: 4px;
                }

                .card .button-wrapper {
                    margin: 32px 8px;
                }

                .card .button-wrapper .button {
                    background-color: ${bgColor};
                    color: ${textColor};
                    font-size: 14px;
                    height: 40px;
                    border-radius: 16px;
                    cursor: pointer;
                    width: 100%;
                    border: none;
                }

                .card .regards-wrapper {
                    margin: 8px;
                    line-height: 1.5;
                    color: #667085;
                    font-size: 14px;
                }
            </style>
        </head>

        <body>
            <div class="card">
                <div class="logo-wrapper">
                    <img style="${logo}" src="${process.env.IMAGE_URL}/${path}" alt="Company Logo">
                </div>
                <div class="invitation-wrapper">
                    ${header}
                </div>
                <div class="description-wrapper">
                    <p>
                        This is an automatically generated email, please do not reply.
                    </p>
                </div>
                <div class="invite-wrapper">
                    ${body}
                </div>
                <div class="button-wrapper">
                    <a href="${link}" style="display: inline-block; padding: 10px 20px; font-size: 14px; color: ${textColor}; background-color: ${bgColor}; text-align: center; text-decoration: none; border-radius: 16px; width: 100%;text-underline-color: ${bgColor}"> 
                    <!--[if mso]><i style="mso-font-width:200%;mso-text-raise:100%" hidden>&emsp;</i><span style="mso-text-raise:50%;"><![endif]-->Go to Case<!--[if mso]></span><i style="mso-font-width:200%;" hidden>&emsp;&#8203;</i><![endif]-->
                    </a>
                </div>
                <div class="regards-wrapper">
                    ${footer}
                </div>
            </div>

            </div>
        </body>

        </html>`
    };
    try {
        await sgMail.send(msg);
    } catch (error) {
        console.error(error);

        if (error.response) {
            console.error(error.response.body);
        }
    }
};
