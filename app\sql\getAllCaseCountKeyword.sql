SELECT COUNT(*) 
FROM cases c
JOIN `status` s 
ON s.id = c.status
LEFT JOIN users u ON c.user_id = u.user_id
LEFT JOIN party_info pi2 ON c.party_info_id  = pi2.id
WHERE 1=1 
AND c.lf_id = ?
AND c.status in (?)
AND (
c.case_id_pms like ?
OR pi2.first_name like ?
OR pi2.last_name like ?
OR pi2.phone like ?
OR pi2.email like ?
OR c.case_name like ?
OR s.name like ?
OR DATE_FORMAT(c.update_at,"%m %d %Y") like ?
OR (DATE_FORMAT(c.created_at,"%m %d %Y") like ? and c.update_at is null)
);
