import pool from "../config/db.js";
import { runQuery } from "#utils";
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from "#middlewares/ErrorClass.js";
import * as fs from "fs";
import crypto from "crypto";
import dotenv from "dotenv";

dotenv.config();

/**
 * Graph Credentials Service
 * Manages CRUD operations for Microsoft Graph API credentials
 */
export class GraphCredentialsService {
  constructor() {
    // Use environment variable for encryption key
    this.encryptionKey = process.env.GRAPH_ENCRYPTION_KEY;
    this.algorithm = 'aes-256-cbc';

    // Validate encryption key
    if (!this.encryptionKey) {
      if (process.env.NODE_ENV === 'production') {
        throw new Error('GRAPH_ENCRYPTION_KEY environment variable is required in production');
      } else {
        // Use a default key for development/testing only
        console.warn('⚠️  Using default encryption key for development. Set GRAPH_ENCRYPTION_KEY in production!');
        this.encryptionKey = 'dev-key-change-in-production-123456';
      }
    }

    // Ensure key is at least 32 characters for AES-256
    if (this.encryptionKey.length < 32) {
      throw new Error('GRAPH_ENCRYPTION_KEY must be at least 32 characters long');
    }
  }

  /**
   * Encrypt sensitive data using AES-256-CBC with proper IV
   * @param {string} text - Text to encrypt
   * @returns {string} Encrypted text in format: iv:encryptedData
   */
  encrypt(text) {
    if (!text) return text;

    try {
      // Generate a random IV for each encryption
      const iv = crypto.randomBytes(16);

      // Create cipher with key and IV
      const cipher = crypto.createCipheriv(this.algorithm, Buffer.from(this.encryptionKey, 'utf8').subarray(0, 32), iv);

      let encrypted = cipher.update(text, 'utf8', 'hex');
      encrypted += cipher.final('hex');

      // Return IV + encrypted data
      return iv.toString('hex') + ':' + encrypted;
    } catch (error) {
      console.error('Encryption error:', error);
      throw new Error('Failed to encrypt sensitive data');
    }
  }

  /**
   * Decrypt sensitive data using AES-256-CBC
   * @param {string} encryptedText - Encrypted text in format: iv:encryptedData
   * @returns {string} Decrypted text
   */
  decrypt(encryptedText) {
    if (!encryptedText || !encryptedText.includes(':')) return encryptedText;

    try {
      const textParts = encryptedText.split(':');
      const iv = Buffer.from(textParts.shift(), 'hex');
      const encryptedData = textParts.join(':');

      // Create decipher with key and IV
      const decipher = crypto.createDecipheriv(this.algorithm, Buffer.from(this.encryptionKey, 'utf8').subarray(0, 32), iv);

      let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
      decrypted += decipher.final('utf8');

      return decrypted;
    } catch (error) {
      console.error('Decryption error:', error);
      throw new Error('Failed to decrypt sensitive data');
    }
  }

  /**
   * Add Graph credentials for a law firm
   * @param {Object} credentialsData - Credentials data
   * @returns {Object} Created credentials
   */
  async addCredentials(credentialsData) {
    const { lf_id, tenant_id, client_id, client_secret, created_by } = credentialsData;
    
    let con = await pool.getConnection();
    try {
      await con.beginTransaction();

      // Check if credentials already exist for this law firm
      const existing = await runQuery(
        con,
        "SELECT id FROM graph_credentials WHERE lf_id = ?",
        [lf_id]
      );

      if (existing.length > 0) {
        throw ErrorHandler.badRequestError("Graph credentials already exist for this law firm");
      }

      // Encrypt sensitive data
      const encryptedSecret = this.encrypt(client_secret);

      // Insert new credentials
      const sql = fs.readFileSync("app/sql/addGraphCredentials.sql").toString();
      const result = await runQuery(con, sql, [
        lf_id,
        tenant_id,
        client_id,
        encryptedSecret,
        true,
        created_by
      ]);

      await con.commit();

      return {
        id: result.insertId,
        lf_id,
        tenant_id,
        client_id,
        // Don't return the secret
        is_active: true
      };
    } catch (error) {
      await con.rollback();
      throw ErrorHandler.badRequestError(`Failed to add Graph credentials: ${error.message}`);
    } finally {
      con.destroy();
    }
  }

  /**
   * Get Graph credentials for a law firm
   * @param {number} lf_id - Law firm ID
   * @param {boolean} includeSecret - Whether to include decrypted secret
   * @returns {Object} Credentials
   */
  async getCredentials(lf_id, includeSecret = false) {
    let con = await pool.getConnection();
    try {
      const sql = fs.readFileSync("app/sql/getGraphCredentials.sql").toString();
      const result = await runQuery(con, sql, [lf_id]);

      if (result.length === 0) {
        return null;
      }

      const credentials = result[0];
      
      // Decrypt secret if requested
      if (includeSecret) {
        credentials.client_secret = this.decrypt(credentials.client_secret);
      } else {
        // Remove secret from response
        delete credentials.client_secret;
      }

      return credentials;
    } catch (error) {
      throw ErrorHandler.badRequestError(`Failed to get Graph credentials: ${error.message}`);
    } finally {
      con.destroy();
    }
  }

  /**
   * Update Graph credentials for a law firm
   * @param {number} lf_id - Law firm ID
   * @param {Object} updateData - Update data
   * @returns {Object} Update result
   */
  async updateCredentials(lf_id, updateData) {
    const { tenant_id, client_id, client_secret, is_active, updated_by } = updateData;
    
    let con = await pool.getConnection();
    try {
      await con.beginTransaction();

      // Check if credentials exist
      const existing = await runQuery(
        con,
        "SELECT id FROM graph_credentials WHERE lf_id = ?",
        [lf_id]
      );

      if (existing.length === 0) {
        throw ErrorHandler.badRequestError("Graph credentials not found for this law firm");
      }

      // Encrypt secret if provided
      const encryptedSecret = client_secret ? this.encrypt(client_secret) : null;

      // Update credentials
      const sql = fs.readFileSync("app/sql/updateGraphCredentials.sql").toString();
      const result = await runQuery(con, sql, [
        tenant_id,
        client_id,
        encryptedSecret || existing[0].client_secret,
        is_active !== undefined ? is_active : true,
        updated_by,
        lf_id
      ]);

      await con.commit();

      return {
        success: true,
        affectedRows: result.affectedRows
      };
    } catch (error) {
      await con.rollback();
      throw ErrorHandler.badRequestError(`Failed to update Graph credentials: ${error.message}`);
    } finally {
      con.destroy();
    }
  }

  /**
   * Delete (deactivate) Graph credentials for a law firm
   * @param {number} lf_id - Law firm ID
   * @param {number} updated_by - User ID who is deleting
   * @returns {Object} Delete result
   */
  async deleteCredentials(lf_id, updated_by) {
    let con = await pool.getConnection();
    try {
      await con.beginTransaction();

      const sql = fs.readFileSync("app/sql/deleteGraphCredentials.sql").toString();
      const result = await runQuery(con, sql, [updated_by, lf_id]);

      await con.commit();

      return {
        success: true,
        affectedRows: result.affectedRows
      };
    } catch (error) {
      await con.rollback();
      throw ErrorHandler.badRequestError(`Failed to delete Graph credentials: ${error.message}`);
    } finally {
      con.destroy();
    }
  }

  /**
   * Get all law firms with Graph credentials (for admin)
   * @returns {Array} List of law firms with Graph config
   */
  async getAllWithCredentials() {
    let con = await pool.getConnection();
    try {
      const result = await runQuery(
        con,
        `SELECT 
          gc.lf_id,
          gc.tenant_id,
          gc.client_id,
          gc.is_active,
          gc.created_at,
          gc.updated_at,
          lf.lf_org_name
        FROM graph_credentials gc
        JOIN law_firm lf ON gc.lf_id = lf.lf_id
        ORDER BY lf.lf_org_name`,
        []
      );

      return result;
    } catch (error) {
      throw ErrorHandler.badRequestError(`Failed to get Graph credentials list: ${error.message}`);
    } finally {
      con.destroy();
    }
  }
}

export const graphCredentialsService = new GraphCredentialsService();
