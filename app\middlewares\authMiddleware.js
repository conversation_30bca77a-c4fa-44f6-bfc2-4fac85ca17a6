import jwt from 'jsonwebtoken';
import <PERSON>rror<PERSON><PERSON><PERSON> from './ErrorClass.js';
import apiTokenService from '../services/apiToken.service.js';

export const routeProtection = (req, res, next) => {
    // get auth token from header
    const authHeader = req.headers.authorization;
    const authBody = req.body.token;
    let token;

    if (authBody) {
        // get auth token from body
        token = authBody;
    } else if (authHeader) {
        // get auth token from header
        token = authHeader && authHeader.split(' ')[1];
    } else {
        throw ErrorHandler.notFoundError('No token present, authorization denied');
    }
    jwt.verify(token, process.env.ACCESS_TOKEN_SECRET, (err, user) => {
        if (err) {
            throw ErrorHandler.forbidden('Token is invalid');
        }
        req.user = user;
        next();
    });
};

// create role based access control middleware
export const roleBasedAccessControl = (...allowedRoles) => {
    return (req, res, next) => {
        // Convert user role to string for comparison (handles both number and string roles)
        const userRole = String(req.user.role);
        if (!allowedRoles.includes(userRole)) {
            return next(
                ErrorHandler.forbidden(
                    "You don't have previlages to perform this action"
                )
            );
        }
        next();
    };
};


export const authProtection = (req, res, next) => {
    // get auth token from header
    const authHeader = req.headers.authorization;
    const authBody = req.body.token;
    let token;
    if (authBody) {
        // get auth token from body
        token = authBody;
    } else if (authHeader) {
        // get auth token from header
        token = authHeader && authHeader.split(' ')[1];
    } else {
        throw ErrorHandler.notFoundError('No token present, authorization denied');
    }
    jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
        if (err) {
            console.log(err);
            throw ErrorHandler.forbidden('Token is invalid');
        }
        req.user = user;
        next();
    });
};

/**
 * API Token Authentication Middleware
 * Authenticates requests using firm-specific API tokens for AdminAPI users
 */
export const apiTokenAuth = async (req, res, next) => {
    try {
        // Get API token from header
        const authHeader = req.headers.authorization;

        if (!authHeader) {
            throw ErrorHandler.notFoundError('No API token present, authorization denied');
        }

        // Extract token (expecting format: "Bearer propero_xxxxxxxx_...")
        const token = authHeader.split(' ')[1];

        if (!token || !token.startsWith('propero_')) {
            throw ErrorHandler.forbidden('Invalid API token format');
        }

        // Verify token
        const tokenData = await apiTokenService.verifyToken(token);

        if (!tokenData) {
            throw ErrorHandler.forbidden('Invalid or expired API token');
        }

        // Check if law firm is active
        if (tokenData.lf_status !== 1) {
            throw ErrorHandler.forbidden('Law firm is not active');
        }

        // Set user context for downstream middleware/controllers
        req.user = {
            user_id: tokenData.user_id,
            email: tokenData.email,
            role: tokenData.role,
            lf_id: tokenData.lf_id,
            token_id: tokenData.id,
            token_name: tokenData.token_name,
            permissions: tokenData.permissions,
            auth_type: 'api_token'
        };

        req.lawfirm = {
            lf_id: tokenData.lf_id,
            lf_org_name: tokenData.lf_org_name,
            status: tokenData.lf_status
        };

        next();
    } catch (error) {
        if (error instanceof ErrorHandler) {
            return res.status(error.status).json({
                success: false,
                message: error.message
            });
        }

        return res.status(500).json({
            success: false,
            message: 'Internal server error during API token authentication'
        });
    }
};

/**
 * Middleware to ensure user has AdminAPI role
 * Should be used after apiTokenAuth
 */
export const requireAdminAPI = (req, res, next) => {
    // Convert role to string for comparison (handles both number and string roles)
    const userRole = String(req.user.role);
    if (userRole !== '6') {
        return res.status(403).json({
            success: false,
            message: 'AdminAPI role required'
        });
    }
    next();
};
