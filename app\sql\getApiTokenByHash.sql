SELECT 
    at.id,
    at.lf_id,
    at.user_id,
    at.token_name,
    at.token_hash,
    at.token_prefix,
    at.permissions,
    at.is_active,
    at.expires_at,
    at.last_used_at,
    at.created_at,
    u.email,
    u.role,
    lf.lf_org_name,
    lf.status as lf_status
FROM api_tokens at
JOIN users u ON at.user_id = u.user_id
JOIN law_firm lf ON at.lf_id = lf.lf_id
WHERE at.token_hash = ?
AND at.is_active = 1
AND (at.expires_at IS NULL OR at.expires_at > NOW())
AND u.role = '6'
AND u.status IN (1, 3);
