import express from "express";
import baseRoute from "./base.route.js";
import userRoute from "./user.route.js";
import mailRoute from "./mail.route.js";
import smsRoute from "./sms.route.js";
import caseRoute from "./case.route.js";
import aiRoute from "./ai.route.js";
import customMailRoutes from "./customMail.route.js";
const router = express.Router();

router.use("/base", baseRoute);
router.use("/user", userRoute);
router.use("/mail", mailRoute);
router.use("/sms", smsRoute);
router.use("/case", caseRoute);
router.use("/customMail", customMailRoutes)
router.use("/ai", aiRoute);
export default router;
