import mysql from 'mysql2/promise';
import dotenv from "dotenv";

dotenv.config();

const deleteOrphanedAnswerRecords = async () => {
  let connection;

  try {
    console.log('🧹 Starting deletion of orphaned answer records from both tables...\n');

    // Create database connection
    connection = await mysql.createConnection({
      host:  'localhost',
      user: 'root',
      password:  'Nobisoft1234',
      database:'propero'
    });

    console.log('✅ Connected to database');

    await connection.beginTransaction();

    // 1. Find and delete orphaned records from 'answers' table
    console.log('🔍 Finding answers records with invalid ques_id references...');

    const orphanedAnswersRecords = await connection.query(`
      SELECT a.id, a.name, a.ques_id
      FROM answers a
      LEFT JOIN questions q ON a.ques_id = q.id
      WHERE a.ques_id IS NOT NULL AND q.id IS NULL
    `);

    console.log(`Found ${orphanedAnswersRecords[0].length} answers records with invalid ques_id references`);

    if (orphanedAnswersRecords[0].length > 0) {
      console.log('🗑️  Deleting answers records with invalid ques_id references in batches...');
      const batchSize = 1000;
      let deletedCount = 0;

      for (let i = 0; i < orphanedAnswersRecords[0].length; i += batchSize) {
        const batch = orphanedAnswersRecords[0].slice(i, i + batchSize);
        const ids = batch.map(record => `'${record.id}'`).join(',');

        await connection.query(`DELETE FROM answers WHERE id IN (${ids})`);
        deletedCount += batch.length;

        console.log(`   Deleted ${deletedCount}/${orphanedAnswersRecords[0].length} orphaned answers records`);
      }
      console.log(`✅ Deleted ${orphanedAnswersRecords[0].length} orphaned answers records`);
    } else {
      console.log('ℹ️  No orphaned answers records found');
    }

    // 2. Find and delete orphaned records from 'answer' table
    console.log('\n🔍 Finding answer records with invalid ques_id references...');

    const orphanedAnswerRecords = await connection.query(`
      SELECT ans.ans_id, ans.name, ans.ques_id
      FROM answer ans
      LEFT JOIN questions q ON ans.ques_id = q.id
      WHERE ans.ques_id IS NOT NULL AND q.id IS NULL
    `);

    console.log(`Found ${orphanedAnswerRecords[0].length} answer records with invalid ques_id references`);

    if (orphanedAnswerRecords[0].length > 0) {
      console.log('🗑️  Deleting answer records with invalid ques_id references in batches...');
      const batchSize = 1000;
      let deletedCount = 0;

      for (let i = 0; i < orphanedAnswerRecords[0].length; i += batchSize) {
        const batch = orphanedAnswerRecords[0].slice(i, i + batchSize);
        const ids = batch.map(record => `'${record.ans_id}'`).join(',');

        await connection.query(`DELETE FROM answer WHERE ans_id IN (${ids})`);
        deletedCount += batch.length;

        console.log(`   Deleted ${deletedCount}/${orphanedAnswerRecords[0].length} orphaned answer records`);
      }
      console.log(`✅ Deleted ${orphanedAnswerRecords[0].length} orphaned answer records`);
    } else {
      console.log('ℹ️  No orphaned answer records found');
    }

    await connection.commit();
    console.log('\n🎉 Cleanup completed successfully!');

    const totalCleaned = orphanedAnswersRecords[0].length + orphanedAnswerRecords[0].length;
    console.log(`📊 Summary: Cleaned up ${totalCleaned} orphaned records total (${orphanedAnswersRecords[0].length} from answers + ${orphanedAnswerRecords[0].length} from answer)`);
    
  } catch (error) {
    if (connection) {
      await connection.rollback();
    }
    console.error('❌ Error during cleanup:', error.message);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

// Run the cleanup
deleteOrphanedAnswerRecords()
  .then(() => {
    console.log('\n✅ Database cleanup completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Database cleanup failed:', error.message);
    process.exit(1);
  });
