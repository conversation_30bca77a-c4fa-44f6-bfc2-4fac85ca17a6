import express from "express";
import {
  login,
  getLawfirm,
  refreshToken,
  getLawfirmSettings,
  tokenForAddClientPMS,
} from "#controllers/user.controller.js";
import dotenv from "dotenv";
dotenv.config();

const router = express.Router();

// AUTH
/**
 * @swagger
 * /public/user/login:
 *   post:
 *     summary: User login
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *                 description: User's email address
 *               password:
 *                 type: string
 *                 description: User's password
 *     responses:
 *       200:
 *         description: Login successful
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 token:
 *                   type: string
 *                   description: JWT authentication token
 *       401:
 *         description: Invalid credentials
 *       500:
 *         description: Error during login
 */
router.route("/login").post(login);
// router.route("/getAllUsers").get(getAllUsers);

/**
 * @swagger
 * /public/user/getLawfirm:
 *   get:
 *     summary: Get law firm information
 *     tags: [Law Firms]
 *     responses:
 *       200:
 *         description: Law firm information retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     lf_id:
 *                       type: string
 *                     name:
 *                       type: string
 *                     status:
 *                       type: integer
 *                     mandate:
 *                       type: string
 *                     prefix:
 *                       type: string
 *       500:
 *         description: Error retrieving law firm information
 */
router.route("/getLawfirm").get(getLawfirm);
/**
 * @swagger
 * /public/user/refreshToken:
 *   post:
 *     summary: Refresh authentication token
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - refreshToken
 *             properties:
 *               refreshToken:
 *                 type: string
 *                 description: Refresh token to generate new access token
 *     responses:
 *       200:
 *         description: Token refreshed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 token:
 *                   type: string
 *                   description: New JWT authentication token
 *       401:
 *         description: Invalid refresh token
 *       500:
 *         description: Error refreshing token
 */
router.route("/refreshToken").post(refreshToken);

/**
 * @swagger
 * /public/user/getLawfirmSettings:
 *   get:
 *     summary: Get law firm settings
 *     tags: [Law Firms]
 *     responses:
 *       200:
 *         description: Law firm settings retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     settings:
 *                       type: object
 *                       description: Law firm settings object
 *       500:
 *         description: Error retrieving law firm settings
 */
router.route("/getLawfirmSettings").get(getLawfirmSettings);

/**
 * @swagger
 * /public/user/tokenForAddClientPMS:
 *   post:
 *     summary: Generate token for adding client from PMS
 *     tags: [Law Firms]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - lf_id
 *             properties:
 *               lf_id:
 *                 type: string
 *                 description: Law firm ID
 *     responses:
 *       200:
 *         description: Token generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 token:
 *                   type: string
 *                   description: Generated token for PMS integration
 *       500:
 *         description: Error generating token
 */
router.route("/tokenForAddClientPMS").post(tokenForAddClientPMS);
export default router;
