import express from "express";
import {
  getListNoti,
  getNotiById,
  updateStatusNoti,
  updateStatusSeenAllNoti,
  getCountNoti,
  updateStatusMultiNoti,
  deleteNotification,
} from "#controllers/noti.controller.js";

/**
 * @swagger
 * tags:
 *   name: Notifications
 *   description: Notification management endpoints
 */

const notiRoute = express.Router();

/**
 * @swagger
 * /private/noti/getListNoti:
 *   get:
 *     summary: Get paginated list of notifications
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: user_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of user to get notifications for
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: size
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: List of notifications retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Get list noti successfully"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 */
notiRoute.route("/getListNoti").get(getListNoti);

/**
 * @swagger
 * /private/noti/getCountNoti:
 *   get:
 *     summary: Get count of unread notifications
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: user_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of user to get notification count for
 *     responses:
 *       200:
 *         description: Notification count retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Get count noti successfully"
 *                 data:
 *                   type: integer
 *                   example: 5
 */
notiRoute.route("/getCountNoti").get(getCountNoti);

/**
 * @swagger
 * /private/noti/getNotiById:
 *   get:
 *     summary: Get notification by ID
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: noti_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of notification to retrieve
 *     responses:
 *       200:
 *         description: Notification retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Get noti successfully"
 *                 data:
 *                   type: object
 */
notiRoute.route("/getNotiById").get(getNotiById);

/**
 * @swagger
 * /private/noti/updateStatusNoti:
 *   post:
 *     summary: Update notification status
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - noti_id
 *               - status
 *             properties:
 *               noti_id:
 *                 type: string
 *                 description: ID of notification to update
 *               status:
 *                 type: integer
 *                 enum: [1, 2, 3]
 *                 description: New status for notification
 *     responses:
 *       200:
 *         description: Notification status updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Update status seen noti successfully"
 *       500:
 *         description: Invalid status value
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Status invalid"
 */
notiRoute.route("/updateStatusNoti").post(updateStatusNoti);

/**
 * @swagger
 * /private/noti/updateStatusMultiNoti:
 *   post:
 *     summary: Update status for multiple notifications
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - noti_id
 *               - status
 *             properties:
 *               noti_id:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Array of notification IDs to update
 *               status:
 *                 type: integer
 *                 enum: [1, 2, 3]
 *                 description: New status for notifications
 *     responses:
 *       200:
 *         description: Notifications status updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Update status seen noti successfully"
 *       500:
 *         description: Invalid status value
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Status invalid"
 */
notiRoute.route("/updateStatusMultiNoti").post(updateStatusMultiNoti);

/**
 * @swagger
 * /private/noti/updateStatusSeenAllNoti:
 *   post:
 *     summary: Mark all notifications as seen for a user
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - user_id
 *             properties:
 *               user_id:
 *                 type: string
 *                 description: ID of user whose notifications to mark as seen
 *     responses:
 *       200:
 *         description: All notifications marked as seen
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Update status seen all noti successfully"
 */
notiRoute.route("/updateStatusSeenAllNoti").post(updateStatusSeenAllNoti);

/**
 * @swagger
 * /private/noti/deleteNotification:
 *   post:
 *     summary: Delete a notification
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - noti_id
 *             properties:
 *               noti_id:
 *                 type: string
 *                 description: ID of notification to delete
 *     responses:
 *       200:
 *         description: Notification deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Delete noti successfully"
 *       500:
 *         description: Failed to delete notification
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Delete noti failed"
 */
notiRoute.route("/deleteNotification").post(deleteNotification);

export default notiRoute;
