import express from "express";
import {
  createApiToken,
  getApiTokens,
  revokeApiToken,
  getCurrentTokenInfo
} from "#controllers/apiToken.controller.js";
import { roleBasedAccessControl } from "#middlewares/authMiddleware.js";
import { roles } from "#middlewares/roles.js";

const router = express.Router();

// Debug endpoint to check user role
router.get("/debug/user-role", (req, res) => {
  const userRoleString = String(req.user.role);
  return res.json({
    success: true,
    user_role: req.user.role,
    user_role_type: typeof req.user.role,
    user_role_string: userRoleString,
    admin_role: roles.Admin,
    admin_role_type: typeof roles.Admin,
    roles_match_original: req.user.role === roles.Admin,
    roles_match_fixed: userRoleString === roles.Admin,
    allowed_roles: [roles.Admin, roles.LawfirmSuperAdmin],
    user_data: req.user
  });
});

// Debug endpoint to find AdminAPI users
router.get("/debug/admin-api-users", async (req, res) => {
  try {
    const { runQuery } = await import("#utils");
    const pool = (await import("../../config/db.js")).default;

    let con = await pool.getConnection();
    try {
      const users = await runQuery(
        con,
        "SELECT user_id, email, role, lf_id, status FROM users WHERE role = '6' OR role = 6",
        []
      );

      return res.json({
        success: true,
        message: "AdminAPI users found",
        data: users,
        note: "Use one of these user_id values when creating API tokens"
      });
    } finally {
      con.destroy();
    }
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

/**
 * @swagger
 * components:
 *   schemas:
 *     ApiToken:
 *       type: object
 *       required:
 *         - token_name
 *         - user_id
 *       properties:
 *         token_name:
 *           type: string
 *           description: Descriptive name for the token
 *         user_id:
 *           type: integer
 *           description: ID of the AdminAPI user
 *         lf_id:
 *           type: integer
 *           description: Law firm ID (Admin only)
 *         expires_at:
 *           type: string
 *           format: date-time
 *           description: Token expiration date (optional)
 *         permissions:
 *           type: object
 *           description: Token permissions (optional)
 */

/**
 * @swagger
 * /private/api-tokens:
 *   post:
 *     summary: Create a new API token
 *     tags: [API Tokens]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ApiToken'
 *     responses:
 *       201:
 *         description: API token created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                     token:
 *                       type: string
 *                       description: The actual token (only shown once)
 *                     token_name:
 *                       type: string
 *                     prefix:
 *                       type: string
 *                     expires_at:
 *                       type: string
 *                     created_at:
 *                       type: string
 */
router.post("/", 
  roleBasedAccessControl(roles.Admin, roles.LawfirmSuperAdmin),
  createApiToken
);

/**
 * @swagger
 * /private/api-tokens:
 *   get:
 *     summary: Get API tokens for law firm
 *     tags: [API Tokens]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: API tokens retrieved successfully
 */
router.get("/", 
  roleBasedAccessControl(roles.Admin, roles.LawfirmSuperAdmin),
  getApiTokens
);

/**
 * @swagger
 * /private/api-tokens/{lf_id}:
 *   get:
 *     summary: Get API tokens for specific law firm (Admin only)
 *     tags: [API Tokens]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: lf_id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: API tokens retrieved successfully
 */
router.get("/:lf_id", 
  roleBasedAccessControl(roles.Admin),
  getApiTokens
);

/**
 * @swagger
 * /private/api-tokens/{token_id}/revoke:
 *   put:
 *     summary: Revoke an API token
 *     tags: [API Tokens]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: token_id
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               lf_id:
 *                 type: integer
 *                 description: Law firm ID (Admin only)
 *     responses:
 *       200:
 *         description: API token revoked successfully
 */
router.put("/:token_id/revoke", 
  roleBasedAccessControl(roles.Admin, roles.LawfirmSuperAdmin),
  revokeApiToken
);

export default router;
