#!/usr/bin/env node

/**
 * Test script to validate SQL syntax for API tokens table
 */

import mysql from 'mysql2/promise';
import fs from 'fs';
import dotenv from 'dotenv';

dotenv.config();

async function testSqlSyntax() {
  let connection;
  
  try {
    console.log('🧪 Testing SQL syntax for API tokens table...\n');

    // Create database connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'propero'
    });

    console.log('✅ Connected to database');

    // Test the simple version first
    console.log('📋 Testing simple SQL syntax...');
    const simpleSql = fs.readFileSync('app/sql/createApiTokensSimple.sql', 'utf8');
    
    try {
      await connection.execute(simpleSql);
      console.log('✅ Simple SQL syntax is valid');
      
      // Drop the table to test the full version
      await connection.execute('DROP TABLE IF EXISTS api_tokens');
      
    } catch (error) {
      console.log('❌ Simple SQL syntax failed:', error.message);
      return;
    }

    // Test the full version with foreign keys
    console.log('📋 Testing full SQL syntax with foreign keys...');
    const fullSql = fs.readFileSync('app/sql/createApiTokens.sql', 'utf8');
    
    try {
      await connection.execute(fullSql);
      console.log('✅ Full SQL syntax is valid');
      
    } catch (error) {
      console.log('❌ Full SQL syntax failed:', error.message);
      console.log('💡 You can use the simple version without foreign keys');
      return;
    }

    // Test basic operations
    console.log('🧪 Testing basic table operations...');
    
    // Insert test data
    await connection.execute(`
      INSERT INTO api_tokens (lf_id, user_id, token_name, token_hash, token_prefix, created_by) 
      VALUES (1, 1, 'Test Token', 'test_hash_123', 'propero_test', 1)
    `);
    console.log('✅ Insert operation successful');
    
    // Select test data
    const [rows] = await connection.execute('SELECT * FROM api_tokens WHERE token_name = ?', ['Test Token']);
    console.log('✅ Select operation successful, found', rows.length, 'row(s)');
    
    // Clean up
    await connection.execute('DELETE FROM api_tokens WHERE token_name = ?', ['Test Token']);
    console.log('✅ Delete operation successful');

    console.log('\n🎉 All SQL syntax tests passed!');
    console.log('✅ The API tokens table is ready to use');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.log('\n💡 Troubleshooting tips:');
    console.log('1. Make sure your database connection is correct');
    console.log('2. Ensure the law_firm and users tables exist');
    console.log('3. Check if foreign key constraints are supported');
    console.log('4. Use the simple version if foreign keys cause issues');
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run test if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  testSqlSyntax();
}

export { testSqlSyntax };
