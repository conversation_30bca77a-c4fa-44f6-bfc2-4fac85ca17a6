# Monthly Subscription Billing System

## Overview

The billing system implements a monthly subscription model that calculates £250 per month plus 20% VAT for law firms. For new law firms, the first month is pro-rated based on the number of days used. Instead of automatically processing payments, the system sends detailed billing emails to the finance team.

## Implementation

### Configuration

The billing configuration is set in `app/services/billing.service.js`:

```javascript
const MONTHLY_FEE = 25000; // £250 in pence - monthly subscription fee
const VAT_RATE = 0.20; // 20% VAT rate
```

### How It Works

1. **Monthly Cron Job**: Runs on the 1st-5th of every month at midnight (`"0 0 0 1-5 * *"`)
2. **Calculate Base Amount**:
   - For first month: Pro-rated based on days used from law firm creation date
   - For subsequent months: Full £250 monthly fee
3. **Add VAT**: Calculate 20% VAT on the base amount
4. **Send Email**: Send detailed billing <NAME_EMAIL>

### Pro-Rating Logic

For law firms in their first month of billing:
- Calculate days used: `daysInMonth - creationDay + 1`
- Pro-rated amount: `(MONTHLY_FEE × daysUsed) / daysInMonth`

### Example Scenarios

| Creation Date | Billing Month | Days Used | Base Amount | VAT (20%) | Total |
|--------------|---------------|-----------|-------------|-----------|-------|
| 1st Jan | January | 31/31 | £250.00 | £50.00 | £300.00 |
| 15th Jan | January | 17/31 | £137.10 | £27.42 | £164.52 |
| 31st Jan | January | 1/31 | £8.06 | £1.61 | £9.67 |
| 15th Jan | February | 28/28 | £250.00 | £50.00 | £300.00 |

### Email Content

The billing email includes comprehensive information:

**Email Details:**
- **Recipient**: <EMAIL>
- **Subject**: Monthly Billing Summary
- **Content**: HTML formatted email with billing breakdown

**Information Included:**
- Law firm name
- Mandate (if available, otherwise "Not Available")
- Total amount due
- Detailed description with base amount, VAT, and total
- Billing date
- Pro-rating information (if applicable)

**Example Descriptions:**
- **Full month**: `"Monthly subscription for 1/2024 - Base: £250.00, VAT: £50.00, Total: £300.00"`
- **Pro-rated**: `"Monthly subscription for 1/2024 (pro-rated: 17/31 days) - Base: £137.10, VAT: £27.42, Total: £164.52"`

### Code Implementation

The billing logic is implemented in the scheduled job:

```javascript
const billingJob = schedule.scheduleJob("0 0 0 1-5 * *", async function () {
  // Get law firms with mandates and owner creation dates
  let mandates = await runQuery(con,
    `SELECT DISTINCT lf.mandate, lf.lf_id, u.created_at as owner_created_at
     FROM law_firm lf
     JOIN users u ON lf.lf_id = u.lf_id
     WHERE lf.mandate IS NOT NULL AND u.is_owner = 1`);

  for (let i = 0; i < mandates.length; i++) {
    // Calculate base amount (pro-rated for first month)
    const baseAmount = calculateProRatedAmount(mandates[i].owner_created_at, billingMonth);

    // Calculate total with VAT
    const totalAmount = calculateTotalWithVAT(baseAmount);

    // Create payment with detailed description
    await createPayment(totalAmount, description, mandates[i].mandate);
  }
});
```

### Helper Functions

```javascript
// Calculate pro-rated amount for first month
const calculateProRatedAmount = (lawFirmCreatedDate, billingMonth) => {
  const createdDate = new Date(lawFirmCreatedDate);
  const billingDate = new Date(billingMonth);

  const isFirstMonth = (
    createdDate.getFullYear() === billingDate.getFullYear() &&
    createdDate.getMonth() === billingDate.getMonth()
  );

  if (!isFirstMonth) return MONTHLY_FEE;

  const daysInMonth = new Date(billingDate.getFullYear(), billingDate.getMonth() + 1, 0).getDate();
  const daysUsed = daysInMonth - createdDate.getDate() + 1;
  return Math.round((MONTHLY_FEE * daysUsed) / daysInMonth);
};

// Calculate VAT
const calculateVAT = (amount) => Math.round(amount * VAT_RATE);

// Calculate total with VAT
const calculateTotalWithVAT = (baseAmount) => baseAmount + calculateVAT(baseAmount);
```

## Code Location

The billing logic is implemented in:
- **File**: `app/services/billing.service.js`
- **Function**: `billingJob` (cron job)
- **Constants**: `MONTHLY_FEE`, `VAT_RATE`

## Configuration Changes

To modify the billing structure:

1. Update the `MONTHLY_FEE` constant in `app/services/billing.service.js` (amount in pence)
2. Update the `VAT_RATE` constant for different VAT rates (decimal, e.g., 0.20 for 20%)
3. Restart the application for changes to take effect

## Testing

Unit tests are available in `tests/gocardlessMinimumAmount.test.js` that verify:
- Monthly fee calculations with VAT
- Pro-rated billing for first month
- Edge cases and date calculations
- Integration with the billing cron job

## Important Notes

1. **Currency**: All amounts are handled in pence (smallest currency unit)
2. **Frequency**: Billing occurs monthly on the 1st-5th of each month
3. **Pro-rating**: Only applies to the first month of a law firm's subscription
4. **VAT**: Automatically calculated and included in all billing
5. **Reliability**: Cron job runs for 5 days to handle server failures

## Monitoring

Monitor the application logs on the 1st of each month to see:
- Which law firms had minimum charges applied
- The original vs. final amounts
- Any errors in the billing process

## Rollback

To disable the minimum charge feature:
1. Set `MINIMUM_BILLING_AMOUNT = 0` in the billing service
2. Or modify the logic to use `total` instead of `Math.max(total, MINIMUM_BILLING_AMOUNT)`
