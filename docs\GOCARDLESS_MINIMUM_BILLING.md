# GoCardless Minimum Billing Amount

## Overview

The GoCardless integration now includes a minimum billing amount feature that ensures law firms are charged at least £50 per month, even if their actual usage is lower.

## Implementation

### Configuration

The minimum amount is configured as a constant in `app/services/billing.service.js`:

```javascript
const MINIMUM_BILLING_AMOUNT = 5000; // £50 in pence - minimum monthly charge
```

### How It Works

1. **Monthly Cron Job**: Runs on the 1st of every month at midnight (`"0 0 0 1 * *"`)
2. **Calculate Usage**: Sums up all charges from the previous month for each law firm
3. **Apply Minimum**: Uses `Math.max(total, MINIMUM_BILLING_AMOUNT)` to ensure minimum charge
4. **Create Payment**: Processes the payment through GoCardless with the final amount

### Example Scenarios

| Actual Usage | Minimum Amount | Final Charge | Action |
|-------------|----------------|--------------|---------|
| £25.00 | £50.00 | £50.00 | Minimum applied |
| £35.50 | £50.00 | £50.00 | Minimum applied |
| £49.99 | £50.00 | £50.00 | Minimum applied |
| £50.00 | £50.00 | £50.00 | No change |
| £75.00 | £50.00 | £75.00 | No change |
| £150.00 | £50.00 | £150.00 | No change |

### Billing Description

When the minimum charge is applied, the billing description includes a note:

- **Normal billing**: `"Billing for 1/2024"`
- **With minimum**: `"Billing for 1/2024 (minimum charge applied)"`

### Logging

The system logs when minimum charges are applied:

```
Minimum charge applied for mandate MD001: £25.00 -> £50.00
```

## Code Location

The billing logic is implemented in:
- **File**: `app/services/billing.service.js`
- **Function**: `billingJob` (cron job)
- **Lines**: ~318-332

## Configuration Changes

To modify the minimum amount:

1. Update the `MINIMUM_BILLING_AMOUNT` constant in `app/services/billing.service.js`
2. Amount should be specified in pence (e.g., 5000 = £50.00)
3. Restart the application for changes to take effect

## Testing

Unit tests are available in `tests/gocardlessMinimumAmount.test.js` to verify:
- Minimum amount calculation logic
- Billing description generation
- Edge case handling
- Cron job simulation

## Important Notes

1. **Currency**: All amounts are handled in pence (smallest currency unit)
2. **Frequency**: Minimum charge is applied monthly, not per transaction
3. **Scope**: Only affects the automated monthly billing cron job
4. **Manual Payments**: API-created payments are not affected by minimum amount logic
5. **Existing Mandates**: All existing GoCardless mandates will be subject to the minimum charge

## Monitoring

Monitor the application logs on the 1st of each month to see:
- Which law firms had minimum charges applied
- The original vs. final amounts
- Any errors in the billing process

## Rollback

To disable the minimum charge feature:
1. Set `MINIMUM_BILLING_AMOUNT = 0` in the billing service
2. Or modify the logic to use `total` instead of `Math.max(total, MINIMUM_BILLING_AMOUNT)`
