# GoCardless Monthly Subscription Billing

## Overview

The GoCardless integration implements a monthly subscription billing system that charges law firms £250 per month plus 20% VAT. For new law firms, the first month is pro-rated based on the number of days used.

## Implementation

### Configuration

The billing configuration is set in `app/services/billing.service.js`:

```javascript
const MONTHLY_FEE = 25000; // £250 in pence - monthly subscription fee
const VAT_RATE = 0.20; // 20% VAT rate
```

### How It Works

1. **Monthly Cron Job**: Runs on the 1st-5th of every month at midnight (`"0 0 0 1-5 * *"`)
2. **Calculate Base Amount**:
   - For first month: Pro-rated based on days used from law firm creation date
   - For subsequent months: Full £250 monthly fee
3. **Add VAT**: Calculate 20% VAT on the base amount
4. **Create Payment**: Process the total amount (base + VAT) through GoCardless

### Pro-Rating Logic

For law firms in their first month of billing:
- Calculate days used: `daysInMonth - creationDay + 1`
- Pro-rated amount: `(MONTHLY_FEE × daysUsed) / daysInMonth`

### Example Scenarios

| Creation Date | Billing Month | Days Used | Base Amount | VAT (20%) | Total |
|--------------|---------------|-----------|-------------|-----------|-------|
| 1st Jan | January | 31/31 | £250.00 | £50.00 | £300.00 |
| 15th Jan | January | 17/31 | £137.10 | £27.42 | £164.52 |
| 31st Jan | January | 1/31 | £8.06 | £1.61 | £9.67 |
| 15th Jan | February | 28/28 | £250.00 | £50.00 | £300.00 |

### Billing Description

When the minimum charge is applied, the billing description includes a note:

- **Normal billing**: `"Billing for 1/2024"`
- **With minimum**: `"Billing for 1/2024 (minimum charge applied)"`

### Logging

The system logs when minimum charges are applied:

```
Minimum charge applied for mandate MD001: £25.00 -> £50.00
```

## Code Location

The billing logic is implemented in:
- **File**: `app/services/billing.service.js`
- **Function**: `billingJob` (cron job)
- **Lines**: ~318-332

## Configuration Changes

To modify the minimum amount:

1. Update the `MINIMUM_BILLING_AMOUNT` constant in `app/services/billing.service.js`
2. Amount should be specified in pence (e.g., 5000 = £50.00)
3. Restart the application for changes to take effect

## Testing

Unit tests are available in `tests/gocardlessMinimumAmount.test.js` to verify:
- Minimum amount calculation logic
- Billing description generation
- Edge case handling
- Cron job simulation

## Important Notes

1. **Currency**: All amounts are handled in pence (smallest currency unit)
2. **Frequency**: Minimum charge is applied monthly, not per transaction
3. **Scope**: Only affects the automated monthly billing cron job
4. **Manual Payments**: API-created payments are not affected by minimum amount logic
5. **Existing Mandates**: All existing GoCardless mandates will be subject to the minimum charge

## Monitoring

Monitor the application logs on the 1st of each month to see:
- Which law firms had minimum charges applied
- The original vs. final amounts
- Any errors in the billing process

## Rollback

To disable the minimum charge feature:
1. Set `MINIMUM_BILLING_AMOUNT = 0` in the billing service
2. Or modify the logic to use `total` instead of `Math.max(total, MINIMUM_BILLING_AMOUNT)`
