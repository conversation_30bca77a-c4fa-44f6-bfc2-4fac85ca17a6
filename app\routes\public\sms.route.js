import express from "express";
import { sendOTP_SMS, verifyOTP_SMS } from "#controllers/sms.controller.js";

const smsRoute = express.Router();

/**
 * @swagger
 * /public/sms/sendSMS:
 *   post:
 *     summary: Send OTP via SMS
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - phone_number
 *             properties:
 *               phone_number:
 *                 type: string
 *                 description: User's phone number
 *     responses:
 *       200:
 *         description: SMS OTP sent successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *       400:
 *         description: Invalid phone number format
 *       500:
 *         description: Error sending SMS
 */

smsRoute.route("/sendSMS").post(sendOTP_SMS);

/**
 * @swagger
 * /public/sms/verifySMS:
 *   post:
 *     summary: Verify SMS OTP code
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - phone_number
 *               - otp
 *             properties:
 *               phone_number:
 *                 type: string
 *                 description: User's phone number
 *               otp:
 *                 type: string
 *                 description: OTP code to verify
 *     responses:
 *       200:
 *         description: SMS OTP verified successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *       400:
 *         description: Invalid OTP or expired
 *       500:
 *         description: Error verifying SMS OTP
 */

smsRoute.route("/verifySMS").post(verifyOTP_SMS);

export default smsRoute;
