import { jest } from '@jest/globals';

describe('GoCardless Minimum Amount Logic', () => {
  let mockRunQuery;
  let mockCreatePayment;
  let mockPool;

  beforeAll(async () => {
    // Mock database pool and queries
    mockRunQuery = jest.fn();
    mockCreatePayment = jest.fn();
    
    mockPool = {
      getConnection: jest.fn().mockResolvedValue({
        destroy: jest.fn().mockResolvedValue()
      })
    };

    // Mock the billing service
    jest.unstable_mockModule('../app/config/db.js', () => ({
      default: mockPool
    }));

    jest.unstable_mockModule('../app/utils/index.js', () => ({
      runQuery: mockRunQuery
    }));
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Monthly Fee Calculation with VAT', () => {
    test('should calculate £250 monthly fee with 20% VAT', () => {
      // Test the new monthly fee structure
      const MONTHLY_FEE = 25000; // £250 in pence
      const VAT_RATE = 0.20; // 20%

      const calculateVAT = (amount) => Math.round(amount * VAT_RATE);
      const calculateTotalWithVAT = (baseAmount) => baseAmount + calculateVAT(baseAmount);

      const testCases = [
        {
          baseAmount: MONTHLY_FEE,
          expectedVAT: 5000, // £50 VAT
          expectedTotal: 30000, // £300 total
          description: '£250 + 20% VAT = £300'
        }
      ];

      testCases.forEach(testCase => {
        const vatAmount = calculateVAT(testCase.baseAmount);
        const totalAmount = calculateTotalWithVAT(testCase.baseAmount);

        expect(vatAmount).toBe(testCase.expectedVAT);
        expect(totalAmount).toBe(testCase.expectedTotal);
        console.log(`✓ ${testCase.description}: Base ${testCase.baseAmount} + VAT ${vatAmount} = Total ${totalAmount} pence`);
      });
    });

    test('should calculate pro-rated amount for first month', () => {
      const MONTHLY_FEE = 25000; // £250 in pence

      const calculateProRatedAmount = (lawFirmCreatedDate, billingMonth) => {
        const createdDate = new Date(lawFirmCreatedDate);
        const billingDate = new Date(billingMonth);

        // Check if this is the first month of billing
        const isFirstMonth = (
          createdDate.getFullYear() === billingDate.getFullYear() &&
          createdDate.getMonth() === billingDate.getMonth()
        );

        if (!isFirstMonth) {
          return MONTHLY_FEE; // Full monthly fee for subsequent months
        }

        // Calculate pro-rated amount for first month
        const daysInMonth = new Date(billingDate.getFullYear(), billingDate.getMonth() + 1, 0).getDate();
        const daysUsed = daysInMonth - createdDate.getDate() + 1; // Include the creation day
        const proRatedAmount = Math.round((MONTHLY_FEE * daysUsed) / daysInMonth);

        return proRatedAmount;
      };

      const testCases = [
        {
          createdDate: '2024-01-01', // Created on 1st of January
          billingMonth: '2024-01-01', // Billing for January
          expectedAmount: 25000, // Full month (31 days)
          description: 'Created on 1st, full month billing'
        },
        {
          createdDate: '2024-01-15', // Created on 15th of January
          billingMonth: '2024-01-01', // Billing for January
          expectedAmount: Math.round((25000 * 17) / 31), // 17 days (15th to 31st inclusive)
          description: 'Created on 15th, pro-rated for 17 days'
        },
        {
          createdDate: '2024-01-31', // Created on last day of January
          billingMonth: '2024-01-01', // Billing for January
          expectedAmount: Math.round((25000 * 1) / 31), // 1 day only
          description: 'Created on last day, pro-rated for 1 day'
        },
        {
          createdDate: '2024-01-15', // Created on 15th of January
          billingMonth: '2024-02-01', // Billing for February (second month)
          expectedAmount: 25000, // Full month for subsequent months
          description: 'Second month billing, full amount'
        }
      ];

      testCases.forEach(testCase => {
        const proRatedAmount = calculateProRatedAmount(testCase.createdDate, testCase.billingMonth);
        expect(proRatedAmount).toBe(testCase.expectedAmount);
        console.log(`✓ ${testCase.description}: ${proRatedAmount} pence (£${(proRatedAmount/100).toFixed(2)})`);
      });
    });

    test('should format amounts correctly in logs', () => {
      const testCases = [
        { pence: 1000, expected: '10.00' },
        { pence: 2550, expected: '25.50' },
        { pence: 5000, expected: '50.00' },
        { pence: 12345, expected: '123.45' }
      ];

      testCases.forEach(({ pence, expected }) => {
        const formatted = (pence / 100).toFixed(2);
        expect(formatted).toBe(expected);
        console.log(`✅ ${pence} pence -> £${formatted}`);
      });

      console.log('✅ Amount formatting is correct');
    });

    test('should generate correct billing descriptions', () => {
      const billingMonth = new Date('2024-01-15'); // January 2024
      const baseDescription = `Billing for ${billingMonth.getMonth() + 1}/${billingMonth.getFullYear()}`;
      
      // Test without minimum charge
      const normalDescription = baseDescription;
      expect(normalDescription).toBe('Billing for 1/2024');
      
      // Test with minimum charge
      const minimumDescription = baseDescription + ' (minimum charge applied)';
      expect(minimumDescription).toBe('Billing for 1/2024 (minimum charge applied)');
      
      console.log('✅ Billing descriptions are correct');
      console.log(`   Normal: "${normalDescription}"`);
      console.log(`   With minimum: "${minimumDescription}"`);
    });
  });

  describe('Cron Job Logic Simulation', () => {
    test('should simulate the billing cron job with minimum amount logic', async () => {
      // Mock data for testing
      const mockMandates = [
        { mandate: 'MD001', lf_id: 1, owner_created_at: '2024-01-01' }, // Full month
        { mandate: 'MD002', lf_id: 2, owner_created_at: '2024-01-15' }, // Pro-rated (17 days)
        { mandate: 'MD003', lf_id: 3, owner_created_at: '2023-12-01' }  // Second month (full)
      ];

      const mockCharges = [
        // Law firm 1: £30 total (should be increased to £50)
        [{ cost: 1500 }, { cost: 1500 }], // £15 + £15 = £30
        
        // Law firm 2: £75 total (should remain £75)
        [{ cost: 7500 }], // £75
        
        // Law firm 3: No charges (should be skipped)
        []
      ];

      // Set up mock responses
      mockRunQuery
        .mockResolvedValueOnce(mockMandates) // Get mandates
        .mockResolvedValueOnce(mockCharges[0]) // LF 1 charges
        .mockResolvedValueOnce(mockCharges[1]) // LF 2 charges
        .mockResolvedValueOnce(mockCharges[2]); // LF 3 charges (empty)

      // Simulate the new billing logic
      const results = [];
      const MONTHLY_FEE = 25000; // £250 in pence
      const VAT_RATE = 0.20; // 20%

      const calculateVAT = (amount) => Math.round(amount * VAT_RATE);
      const calculateTotalWithVAT = (baseAmount) => baseAmount + calculateVAT(baseAmount);

      const calculateProRatedAmount = (lawFirmCreatedDate, billingMonth) => {
        const createdDate = new Date(lawFirmCreatedDate);
        const billingDate = new Date(billingMonth);

        const isFirstMonth = (
          createdDate.getFullYear() === billingDate.getFullYear() &&
          createdDate.getMonth() === billingDate.getMonth()
        );

        if (!isFirstMonth) {
          return MONTHLY_FEE;
        }

        const daysInMonth = new Date(billingDate.getFullYear(), billingDate.getMonth() + 1, 0).getDate();
        const daysUsed = daysInMonth - createdDate.getDate() + 1;
        return Math.round((MONTHLY_FEE * daysUsed) / daysInMonth);
      };

      for (let i = 0; i < mockMandates.length; i++) {
        // Calculate base amount (pro-rated for first month)
        const baseAmount = calculateProRatedAmount(mockMandates[i].owner_created_at, '2024-01-01');

        // Calculate total with VAT
        const vatAmount = calculateVAT(baseAmount);
        const totalAmount = calculateTotalWithVAT(baseAmount);

        results.push({
          mandate: mockMandates[i].mandate,
          baseAmount: baseAmount,
          vatAmount: vatAmount,
          totalAmount: totalAmount,
          isProRated: baseAmount < MONTHLY_FEE,
          action: 'payment_created'
        });
      }

      // Verify results
      expect(results).toHaveLength(3);

      // Check first mandate (created on 1st, full month)
      expect(results[0].mandate).toBe('MD001');
      expect(results[0].baseAmount).toBe(25000); // £250 full month
      expect(results[0].vatAmount).toBe(5000); // £50 VAT
      expect(results[0].totalAmount).toBe(30000); // £300 total
      expect(results[0].isProRated).toBe(false);
      expect(results[0].action).toBe('payment_created');

      // Check second mandate (created on 15th, pro-rated)
      expect(results[1].mandate).toBe('MD002');
      expect(results[1].baseAmount).toBe(Math.round((25000 * 17) / 31)); // Pro-rated for 17 days
      expect(results[1].vatAmount).toBe(Math.round(results[1].baseAmount * 0.20)); // 20% VAT
      expect(results[1].totalAmount).toBe(results[1].baseAmount + results[1].vatAmount);
      expect(results[1].isProRated).toBe(true);
      expect(results[1].action).toBe('payment_created');

      // Check third mandate (created in previous month, full amount)
      expect(results[2].mandate).toBe('MD003');
      expect(results[2].baseAmount).toBe(25000); // £250 full month
      expect(results[2].vatAmount).toBe(5000); // £50 VAT
      expect(results[2].totalAmount).toBe(30000); // £300 total
      expect(results[2].isProRated).toBe(false);
      expect(results[2].action).toBe('payment_created');

      console.log('✅ Integration test passed - new billing logic works correctly');
      console.log(`✅ Full month billing: £${(results[0].totalAmount/100).toFixed(2)}`);
      console.log(`✅ Pro-rated billing: £${(results[1].totalAmount/100).toFixed(2)}`);
    });
  });

  describe('Edge Cases', () => {
    test('should handle zero and negative amounts correctly', () => {
      const minimumAmount = 5000;
      
      expect(Math.max(0, minimumAmount)).toBe(5000);
      expect(Math.max(-100, minimumAmount)).toBe(5000);
      expect(Math.max(null, minimumAmount)).toBe(5000);
      expect(Math.max(undefined, minimumAmount)).toBe(5000);
      
      console.log('✅ Edge cases handled correctly');
    });

    test('should handle very large amounts correctly', () => {
      const minimumAmount = 5000;
      const largeAmount = 1000000; // £10,000
      
      expect(Math.max(largeAmount, minimumAmount)).toBe(largeAmount);
      
      console.log('✅ Large amounts handled correctly');
    });
  });
});
