import mysql from 'mysql2/promise';
import dotenv from "dotenv";

dotenv.config();

const removeDuplicates = async () => {
  let connection;
  
  try {
    console.log('🧹 Starting removal of duplicate records...\n');

    // Create database connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PWD || '',
      database: process.env.DB_NAME || 'propero'
    });

    console.log('✅ Connected to database');

    await connection.beginTransaction();

    // 1. Remove duplicates from 'answers' table
    console.log('🔍 Finding duplicate records in answers table...');
    
    const duplicateAnswers = await connection.query(`
      SELECT id, COUNT(*) as count 
      FROM answers 
      GROUP BY id 
      HAVING COUNT(*) > 1
    `);
    
    console.log(`Found ${duplicateAnswers[0].length} IDs with duplicate records in answers table`);
    
    if (duplicateAnswers[0].length > 0) {
      console.log('🗑️  Removing duplicate records from answers table...');
      
      // For each duplicate ID, keep only the first record (lowest auto-increment ID if exists)
      // This uses a self-join to delete all but the first occurrence
      await connection.query(`
        DELETE a1 FROM answers a1
        INNER JOIN answers a2 
        WHERE a1.id = a2.id 
        AND a1.created_at > a2.created_at
      `);
      
      // Alternative approach if no created_at field - use ROW_NUMBER() with a temporary table
      // First, let's check if there are still duplicates after the above
      const remainingDuplicates = await connection.query(`
        SELECT id, COUNT(*) as count 
        FROM answers 
        GROUP BY id 
        HAVING COUNT(*) > 1
      `);
      
      if (remainingDuplicates[0].length > 0) {
        console.log(`Still have ${remainingDuplicates[0].length} duplicate IDs, using alternative method...`);
        
        // Create a temporary table with unique records
        await connection.query(`
          CREATE TEMPORARY TABLE answers_unique AS
          SELECT MIN(ROWID) as keep_rowid, id
          FROM answers
          GROUP BY id
        `);
        
        // Delete records that are not in the keep list
        await connection.query(`
          DELETE FROM answers 
          WHERE ROWID NOT IN (
            SELECT keep_rowid FROM answers_unique
          )
        `);
        
        await connection.query(`DROP TEMPORARY TABLE answers_unique`);
      }
      
      console.log(`✅ Removed duplicates from answers table`);
    }

    // 2. Remove duplicates from 'answer' table
    console.log('\n🔍 Finding duplicate records in answer table...');
    
    const duplicateAnswerRecords = await connection.query(`
      SELECT ans_id, COUNT(*) as count 
      FROM answer 
      GROUP BY ans_id 
      HAVING COUNT(*) > 1
    `);
    
    console.log(`Found ${duplicateAnswerRecords[0].length} IDs with duplicate records in answer table`);
    
    if (duplicateAnswerRecords[0].length > 0) {
      console.log('🗑️  Removing duplicate records from answer table...');
      
      // For answer table, keep the first record based on creation time or ID
      await connection.query(`
        DELETE a1 FROM answer a1
        INNER JOIN answer a2 
        WHERE a1.ans_id = a2.ans_id 
        AND a1.id > a2.id
      `);
      
      console.log(`✅ Removed duplicates from answer table`);
    }

    // 3. Remove duplicates from 'questions' table if any
    console.log('\n🔍 Finding duplicate records in questions table...');
    
    const duplicateQuestions = await connection.query(`
      SELECT id, COUNT(*) as count 
      FROM questions 
      GROUP BY id 
      HAVING COUNT(*) > 1
    `);
    
    console.log(`Found ${duplicateQuestions[0].length} IDs with duplicate records in questions table`);
    
    if (duplicateQuestions[0].length > 0) {
      console.log('🗑️  Removing duplicate records from questions table...');
      
      // Create temporary table for questions
      await connection.query(`
        CREATE TEMPORARY TABLE questions_unique AS
        SELECT MIN(ROWID) as keep_rowid, id
        FROM questions
        GROUP BY id
      `);
      
      await connection.query(`
        DELETE FROM questions 
        WHERE ROWID NOT IN (
          SELECT keep_rowid FROM questions_unique
        )
      `);
      
      await connection.query(`DROP TEMPORARY TABLE questions_unique`);
      
      console.log(`✅ Removed duplicates from questions table`);
    }

    // 4. Remove duplicates from 'groups' table if any
    console.log('\n🔍 Finding duplicate records in groups table...');
    
    const duplicateGroups = await connection.query(`
      SELECT id, COUNT(*) as count 
      FROM \`groups\` 
      GROUP BY id 
      HAVING COUNT(*) > 1
    `);
    
    console.log(`Found ${duplicateGroups[0].length} IDs with duplicate records in groups table`);
    
    if (duplicateGroups[0].length > 0) {
      console.log('🗑️  Removing duplicate records from groups table...');
      
      await connection.query(`
        CREATE TEMPORARY TABLE groups_unique AS
        SELECT MIN(ROWID) as keep_rowid, id
        FROM \`groups\`
        GROUP BY id
      `);
      
      await connection.query(`
        DELETE FROM \`groups\` 
        WHERE ROWID NOT IN (
          SELECT keep_rowid FROM groups_unique
        )
      `);
      
      await connection.query(`DROP TEMPORARY TABLE groups_unique`);
      
      console.log(`✅ Removed duplicates from groups table`);
    }

    await connection.commit();
    console.log('\n🎉 Duplicate removal completed successfully!');
    
    // Show final summary
    const finalAnswersCount = await connection.query(`SELECT COUNT(*) as total FROM answers`);
    const finalAnswerCount = await connection.query(`SELECT COUNT(*) as total FROM answer`);
    const finalQuestionsCount = await connection.query(`SELECT COUNT(*) as total FROM questions`);
    const finalGroupsCount = await connection.query(`SELECT COUNT(*) as total FROM \`groups\``);
    
    console.log(`📊 Final counts:`);
    console.log(`   - answers: ${finalAnswersCount[0][0].total} records`);
    console.log(`   - answer: ${finalAnswerCount[0][0].total} records`);
    console.log(`   - questions: ${finalQuestionsCount[0][0].total} records`);
    console.log(`   - groups: ${finalGroupsCount[0][0].total} records`);
    
  } catch (error) {
    if (connection) {
      await connection.rollback();
    }
    console.error('❌ Error during duplicate removal:', error.message);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

// Run the duplicate removal
removeDuplicates()
  .then(() => {
    console.log('\n✅ Duplicate removal completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Duplicate removal failed:', error.message);
    process.exit(1);
  });
