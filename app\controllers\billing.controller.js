import { baseService } from "#services";
import { catchAsync } from "#utils";
import { billingService } from "#services";
import { roles } from "#middlewares/roles.js";

export const getAllPayment = catchAsync(async (req, res) => {
  if (req.user.role == roles.Admin) {
    const { mandate } = req.body;
    const token = await billingService.getAllPayment(mandate);
    return res.status(200).json({
      success: true,
      message: "Success",
      data: token,
    });
  } else if (
    req.user.role == roles.LawfirmSuperAdmin ||
    req.user.role == roles.LawfirmAdmin ||
    req.user.role == roles.LawfirmUser
  ) {
    const mandate = await billingService.getMandate(req.user.lf_id);
    const token = await billingService.getAllPayment(mandate);
    return res.status(200).json({
      success: true,
      message: "Success",
      data: token,
    });
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});
export const createPayment = catchAsync(async (req, res) => {
  try {
    const { amount, description, mandate } = req.body;
    const result = await billingService.createPayment(
      amount,
      description,
      mandate
    );
    return res.status(200).json({
      success: true,
      message: "Payment created",
      data: result,
    });
  } catch (error) {
    return res.status(400).json({
      success: false,
      message: error.message,
    });
  }
});
export const getAllHistory = catchAsync(async (req, res) => {
  const role = req.user.role;
  if (
    role == roles.LawfirmSuperAdmin ||
    role == roles.LawfirmAdmin ||
    role == roles.LawfirmUser
  ) {
    try {
      const { page, size, keyword, startDate, endDate } = req.body;
      const result = await billingService.getHistory(
        page,
        size,
        keyword,
        req.user.lf_id,
        startDate,
        endDate
      );
      return res.status(200).json({
        success: true,
        message: "History fetched",
        data: result,
      });
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  } else if (role == roles.Admin) {
    try {
      const { page, size, keyword, lf_id, startDate, endDate } = req.body;
      const result = await billingService.getHistory(
        page,
        size,
        keyword,
        lf_id,
        startDate,
        endDate
      );
      return res.status(200).json({
        success: true,
        message: "History fetched",
        data: result,
      });
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});
