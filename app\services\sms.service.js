import Twilio from "twilio";
import pool from "../config/db.js";
import { runQuery, generateToken } from "#utils";
import generator from "generate-password";
import { OAuth2Client } from "google-auth-library";
import nodemailer from "nodemailer";
import { findUserById } from "../services/user.service.js";
import { generateTempToken } from "../../utils/generateAuthToken.js";
import { updateCaseStatus } from "../services/case.service.js";
import { getLawfirmDetails } from "../services/user.service.js";
import ErrorHandler from "#middlewares/ErrorClass.js";
import { caseMail } from "../mails/caseMail.js";
import * as fs from "fs";
import jwt from "jsonwebtoken";
import { submitCaseMail } from "../mails/submitCaseMail.js";
import { activeQuestionnaireMail } from "../mails/activeQuestionnaireMail.js";
import { updateQuestionnaireMail } from "../mails/updateQuestionnaireMail.js";
import { chaserMail } from "../mails/chaserMail.js";
import { submitCaseClientMail } from "../mails/submitCaseClientMail.js";
import * as mailService from "./mail.service.js";
import dotenv from "dotenv";

const client = Twilio(
  process.env.TWILIO_ACCOUNT_SID,
  process.env.TWILIO_AUTH_TOKEN,
  {
    lazyLoading: true,
  }
);
dotenv.config();

export const sendOTP_SMS = async (email, country_code, phone_number) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    let otp_check = await runQuery(
      con,
      `SELECT code,TIMEDIFF(exp_time,NOW()) as time FROM temp_otp WHERE email = ? AND phone = ? AND exp_time > NOW() ORDER BY exp_time DESC LIMIT 1`,
      [email, phone_number]
    );
    if (otp_check.length > 0) {
      const timeDiffString = otp_check[0].time;
      const timeDiffParts = timeDiffString.split(":");
      const timeDiffInMilliseconds =
        (parseInt(timeDiffParts[0]) * 60 * 60 +
          parseInt(timeDiffParts[1]) * 60 +
          parseInt(timeDiffParts[2])) *
        1000;

      const fourMinutesInMilliseconds = 4 * 60 * 1000;
      if (timeDiffInMilliseconds > fourMinutesInMilliseconds) {
        throw ErrorHandler.badRequestError(
          `OTP already sent. Please wait for ${
            (timeDiffInMilliseconds - fourMinutesInMilliseconds) * 0.001
          } seconds before trying again.`
        );
      }
    }
    let otp = generator.generate({
      length: 5,
      numbers: true,
      lowercase: false,
      uppercase: false,
    });
    if (process.env.NODE_ENV != "development") {
      let check = await client.messages.create({
        body: "Your OTP code is " + otp,
        messagingServiceSid: process.env.TWILIO_SERVER_SID,
        to:
          "+" +
          country_code +
          (phone_number[0] == 0 ? phone_number.substring(1) : phone_number),
      });
    }

    let otp_sql = `INSERT INTO temp_otp (email, phone, code, exp_time) VALUES (?, ?, ? , ADDTIME(NOW(),'0:05'))`;
    let otp_result = await runQuery(con, otp_sql, [email, phone_number, otp]);
    await con.commit();
    return otp;
  } catch (err) {
    await con.rollback();
    throw ErrorHandler.badRequestError(err.message);
  } finally {
    con.destroy();
  }
};

export const getInfo = async (token) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    let getInfoSql = `SELECT source, user_id, party_info_id FROM cases WHERE id = ?`;
    let getInfoSqlResult = await runQuery(con, getInfoSql, [token]);

    if (getInfoSqlResult[0]?.source == 0) {
      let user_id = getInfoSqlResult[0]?.user_id;
      let user = await findUserById(user_id);
      if (user?.length == 0) {
        throw ErrorHandler.badRequestError("User not found");
      }
      return {
        user_id: user_id,
        lf_id: user[0].lf_id,
        role: user[0].role,
        first_name: user[0].first_name,
        email: user[0].email,
        phone: user[0].mb_phone
          ? user[0].mb_phone
          : user[0].wrk_phone
          ? user[0].wrk_phone
          : user[0].home_phone,
        country: user[0].country,
      };
    } else {
      let user_id = getInfoSqlResult[0]?.party_info_id;
      let user = await runQuery(con, "SELECT * FROM party_info WHERE id = ?", [
        user_id,
      ]);
      if (user?.length == 0) {
        throw ErrorHandler.badRequestError("User not found");
      }
      return {
        user_id: user_id,
        lf_id: user[0].lf_id,
        role: user[0].role,
        first_name: user[0].first_name,
        email: user[0].email,
        phone: user[0].phone,
        country: user[0].country_code,
      };
    }
  } catch (err) {
    console.log(err);
    await con.rollback();
    throw ErrorHandler.badRequestError(err.message);
  } finally {
    con.destroy();
  }
};

export const getInfoPMS = async (case_id) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    let getInfoSql = `SELECT party_info_id FROM cases WHERE id = ?`;
    let getInfoSqlResult = await runQuery(con, getInfoSql, [case_id]);

    let user_id = getInfoSqlResult[0]?.party_info_id;
    let user = await runQuery(con, "SELECT * FROM party_info WHERE id = ?", [
      user_id,
    ]);
    if (user?.length == 0) {
      throw ErrorHandler.badRequestError("User not found");
    }

    return {
      user_id: user_id,
      first_name: user[0].first_name,
      email: user[0].email,
      phone: user[0].phone,
      country_code: user[0].country_code,
    };
  } catch (err) {
    console.log(err);
    await con.rollback();
    throw ErrorHandler.badRequestError(err.message);
  } finally {
    con.destroy();
  }
};

export const verifyOTP_SMS = async (email, phone_number, otp, reason) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    let otp_sql = `SELECT * FROM temp_otp WHERE email = ? AND phone = ? AND code = ? AND exp_time > NOW() ORDER BY exp_time DESC LIMIT 1;`;
    let otp_result = await runQuery(con, otp_sql, [email, phone_number, otp]);
    let lf_sql = `select lf.lf_color , lf.path, lf.lf_org_name from law_firm lf join users u on lf.lf_id = u.lf_id where u.email = ?`;
    let lf_result = await runQuery(con, lf_sql, [email]);
    let token = await generateTempToken(reason);
    await con.commit();
    if (otp_result.length > 0) {
      return {
        token: token,
        path: lf_result[0]?.path,
        lf_color: lf_result[0]?.lf_color,
        name: lf_result[0]?.lf_org_name,
      };
    } else {
      throw ErrorHandler.badRequestError("Wrong OTP code. Please try again.");
    }
  } catch (err) {
    console.log(err);
    await con.rollback();
    throw ErrorHandler.badRequestError(err.message);
  } finally {
    con.destroy();
  }
};

export const deleteOTP = async (email, phone_number) => {
  console.log(phone_number);
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    let otp_sql = `DELETE FROM temp_otp WHERE email = ? AND phone = ?`;
    let otp_result = await runQuery(con, otp_sql, [email, phone_number]);
    await con.commit();
  } catch (err) {
    console.log(err);
    await con.rollback();
    throw ErrorHandler.badRequestError(err.message);
  } finally {
    con.destroy();
  }
};

const generateLinkVerifyClient = async (case_id, phone, lf_id) => {
  // id | phone | generate id | expired time | isUsed
  let con = await pool.getConnection();
  let prefix = await runQuery(
    con,
    "SELECT prefix FROM law_firm WHERE lf_id = ?",
    [lf_id]
  );
  if (prefix[0].prefix && prefix[0]?.prefix.length > 0) {
    return (
      "https://" +
      prefix[0].prefix +
      "." +
      process.env.CLIENT_URL +
      "/client/verify?token=" +
      case_id +
      "&phone=" +
      phone
    );
  } else {
    return (
      "https://" +
      process.env.CLIENT_URL +
      "/client/verify?token=" +
      case_id +
      "&phone=" +
      phone
    );
  }
};

export const sendLinkCaseVerify = async (
  first_name,
  email,
  phone,
  case_id,
  lf_id,
  user_id,
  requestingUser = null
) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    let style = await runQuery(
      con,
      "SELECT lf_color, path FROM law_firm WHERE lf_id = ?",
      [lf_id]
    );
    let mail = await runQuery(
      con,
      "SELECT title,header, body, footer, logo FROM template_email WHERE lf_id = ? and mail_type = 1",
      [lf_id]
    );
    let link = await generateLinkVerifyClient(case_id, phone, lf_id);
    let color = style[0]?.lf_color ? JSON.parse(style[0]?.lf_color) : undefined;
    let user = await runQuery(
      con,
      "Select u.email,ui.title, ui.first_name, ui.middle_name, ui.last_name, ui.dob, ui.gender,ui.home_phone,ui.mb_phone,ui.wrk_phone,ui.adr_1,ui.adr_2,ui.adr_3,ui.town,ui.country,ui.post_code from user_info ui JOIN users u ON ui.user_id = u.user_id where ui.user_id = ?",
      [user_id]
    );
    // Use smart wrapper that automatically tries Graph API first, then falls back to SendGrid
    await mailService.sendCaseMailSmart({
      email,
      link,
      first_name,
      path: style[0]?.path,
      bgColor: color?.bgColor,
      textColor: color?.textColor,
      title: mail[0]?.title,
      header: mail[0]?.header,
      body: mail[0]?.body,
      footer: mail[0]?.footer,
      logo: mail[0]?.logo,
      user: user[0],
      lf_id
    }, requestingUser);
    await con.commit();
    return link;
  } catch (err) {
    console.log(err);
    await con.rollback();
    throw ErrorHandler.badRequestError(err.message);
  } finally {
    con.destroy();
  }
};

export const sendLinkCaseVerifyPMS = async (
  first_name,
  email,
  phone,
  case_id,
  lf_id,
  user_id,
  requestingUser = null
) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    let style = await runQuery(
      con,
      "SELECT lf_color, path FROM law_firm WHERE lf_id = ?",
      [lf_id]
    );
    let mail = await runQuery(
      con,
      "SELECT title,header, body, footer, logo FROM template_email WHERE lf_id = ? and mail_type = 1",
      [lf_id]
    );
    let link = await generateLinkVerifyClient(case_id, phone, lf_id);
    let color = style[0]?.lf_color ? JSON.parse(style[0]?.lf_color) : undefined;
    let user = await runQuery(con, "Select * from party_info where id = ?", [
      user_id,
    ]);
    // Use smart wrapper that automatically tries Graph API first, then falls back to SendGrid
    await mailService.sendCaseMailSmart({
      email,
      link,
      first_name,
      path: style[0]?.path,
      bgColor: color?.bgColor,
      textColor: color?.textColor,
      title: mail[0]?.title,
      header: mail[0]?.header,
      body: mail[0]?.body,
      footer: mail[0]?.footer,
      logo: mail[0]?.logo,
      user: user[0],
      lf_id
    }, requestingUser);
    await con.commit();
    return link;
  } catch (err) {
    console.log(err);
    await con.rollback();
    throw ErrorHandler.badRequestError(err.message);
  } finally {
    con.destroy();
  }
};

export const sendChaserMail = async (
  first_name,
  email,
  phone,
  case_id,
  lf_id,
  user_id,
  requestingUser = null
) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    let style = await runQuery(
      con,
      "SELECT lf_color, path FROM law_firm WHERE lf_id = ?",
      [lf_id]
    );
    let mail = await runQuery(
      con,
      "SELECT title,header, body, footer, logo FROM template_email WHERE lf_id = ? and mail_type = 3",
      [lf_id]
    );
    let link = await generateLinkVerifyClient(case_id, phone, lf_id);
    let color = style[0]?.lf_color ? JSON.parse(style[0]?.lf_color) : undefined;
    let user = await runQuery(
      con,
      "Select u.email,ui.title, ui.first_name, ui.middle_name, ui.last_name, ui.dob, ui.gender,ui.home_phone,ui.mb_phone,ui.wrk_phone,ui.adr_1,ui.adr_2,ui.adr_3,ui.town,ui.country,ui.post_code from user_info ui JOIN users u ON ui.user_id = u.user_id where ui.user_id = ?",
      [user_id]
    );
    // Use smart wrapper that automatically tries Graph API first, then falls back to SendGrid
    await mailService.sendChaserMailSmart({
      email,
      link,
      first_name,
      path: style[0]?.path,
      bgColor: color?.bgColor,
      textColor: color?.textColor,
      title: mail[0]?.title,
      header: mail[0]?.header,
      body: mail[0]?.body,
      footer: mail[0]?.footer,
      logo: mail[0]?.logo,
      user: user[0],
      lf_id
    }, requestingUser);
    await con.commit();
    return link;
  } catch (err) {
    console.log(err);
    await con.rollback();
    throw ErrorHandler.badRequestError(err.message);
  } finally {
    con.destroy();
  }
};

export const sendChaserMailPMS = async (
  first_name,
  email,
  phone,
  case_id,
  lf_id,
  user_id,
  requestingUser = null
) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    let style = await runQuery(
      con,
      "SELECT lf_color, path FROM law_firm WHERE lf_id = ?",
      [lf_id]
    );
    let mail = await runQuery(
      con,
      "SELECT title,header, body, footer, logo FROM template_email WHERE lf_id = ? and mail_type = 3",
      [lf_id]
    );
    let link = await generateLinkVerifyClient(case_id, phone, lf_id);
    let color = style[0]?.lf_color ? JSON.parse(style[0]?.lf_color) : undefined;
    let user = await runQuery(con, "Select * from party_info where id = ?", [
      user_id,
    ]);
    // Use smart wrapper that automatically tries Graph API first, then falls back to SendGrid
    await mailService.sendChaserMailSmart({
      email,
      link,
      first_name,
      path: style[0]?.path,
      bgColor: color?.bgColor,
      textColor: color?.textColor,
      title: mail[0]?.title,
      header: mail[0]?.header,
      body: mail[0]?.body,
      footer: mail[0]?.footer,
      logo: mail[0]?.logo,
      user: user[0],
      lf_id
    }, requestingUser);
    await con.commit();
    return link;
  } catch (err) {
    console.log(err);
    await con.rollback();
    throw ErrorHandler.badRequestError(err.message);
  } finally {
    con.destroy();
  }
};

export const sendMailSubmitCase = async (first_name, email, role, case_id) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    let result = await runQuery(
      con,
      "SELECT user_id, lf_id FROM cases WHERE id = ?",
      [case_id]
    );
    if (role != 4) {
      // Use enhanced submit case mail with Graph API support
      await mailService.sendEnhancedSubmitCaseMail(email, case_id, first_name, result[0].lf_id, null);
    } else {
      submitMail(
        email,
        case_id,
        first_name,
        result[0].lf_id,
        result[0].user_id
      );
    }
    await con.commit();
    return case_id;
  } catch (err) {
    console.log(err);
    await con.rollback();
    throw ErrorHandler.badRequestError(err.message);
  } finally {
    con.destroy();
  }
};

export const sendMailActiveQuestionnaire = async (
  email,
  first_name,
  qtn_id
) => {
  let con = await pool.getConnection();
  try {
    // Use enhanced active questionnaire mail with Graph API support
    await mailService.sendEnhancedActiveQuestionnaireMail(email, first_name, qtn_id, null, null);
    return qtn_id;
  } catch (err) {
    console.log(err);
    await con.rollback();
    throw ErrorHandler.badRequestError(err.message);
  } finally {
    con.destroy();
  }
};

export const sendMailUpdateQuestionnaire = async (
  email,
  first_name,
  qtn_name,
  qtn_id
) => {
  let con = await pool.getConnection();
  try {
    // Use enhanced update questionnaire mail with Graph API support
    await mailService.sendEnhancedUpdateQuestionnaireMail(email, first_name, qtn_name, qtn_id, null, null);
    return qtn_id;
  } catch (err) {
    console.log(err);
    await con.rollback();
    throw ErrorHandler.badRequestError(err.message);
  } finally {
    con.destroy();
  }
};

export const sendOTPLogin = async (
  user_id,
  email,
  country_code,
  phone_number,
  token
) => {
  const decoded = jwt.verify(token, process.env.JWT_SECRET);
  if (decoded.reason !== user_id) {
    throw ErrorHandler.badRequestError(
      "Email in token does not match the provided email."
    );
  }
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    let otp_check = await runQuery(
      con,
      `SELECT code,TIMEDIFF(exp_time,NOW()) as time FROM temp_otp WHERE email = ? AND phone = ? AND exp_time > NOW() ORDER BY exp_time DESC LIMIT 1`,
      [email, phone_number]
    );
    if (otp_check.length > 0) {
      const timeDiffString = otp_check[0].time;
      const timeDiffParts = timeDiffString.split(":");
      const timeDiffInMilliseconds =
        (parseInt(timeDiffParts[0]) * 60 * 60 +
          parseInt(timeDiffParts[1]) * 60 +
          parseInt(timeDiffParts[2])) *
        1000;

      const fourMinutesInMilliseconds = 4 * 60 * 1000;
      if (timeDiffInMilliseconds > fourMinutesInMilliseconds) {
        throw ErrorHandler.badRequestError(
          `OTP already sent. Please wait for ${
            (timeDiffInMilliseconds - fourMinutesInMilliseconds) * 0.001
          } seconds before trying again.`
        );
      }
    }
    let otp = generator.generate({
      length: 5,
      numbers: true,
      lowercase: false,
      uppercase: false,
    });
    if (process.env.NODE_ENV != "development") {
      let check = await client.messages.create({
        body: "Your OTP code is " + otp,
        messagingServiceSid: process.env.TWILIO_SERVER_SID,
        to:
          "+" +
          country_code +
          (phone_number[0] == 0 ? phone_number.substring(1) : phone_number),
      });
    }
    let otp_sql = `INSERT INTO temp_otp (email, phone, code, exp_time) VALUES (?, ?, ? , ADDTIME(NOW(),'0:05'))`;
    let otp_result = await runQuery(con, otp_sql, [email, phone_number, otp]);
    await con.commit();
    return otp;
  } catch (err) {
    await con.rollback();
    throw ErrorHandler.badRequestError(err.message);
  } finally {
    con.destroy();
  }
};

export const verifyOTPLogin = async (
  user_id,
  email,
  phone_number,
  otp,
  token
) => {
  const decoded = jwt.verify(token, process.env.JWT_SECRET);
  if (decoded.reason !== user_id) {
    throw ErrorHandler.badRequestError(
      "Email in token does not match the provided email."
    );
  }
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    var sql = fs.readFileSync("app/sql/getUserLF.sql").toString();
    let user = await runQuery(con, sql, [email]);
    let otp_sql = `SELECT * FROM temp_otp WHERE email = ? AND phone = ? AND code = ? AND exp_time > NOW() ORDER BY exp_time DESC LIMIT 1;`;
    let otp_result = await runQuery(con, otp_sql, [email, phone_number, otp]);
    if (otp_result.length > 0) {
      await runQuery(
        con,
        "DELETE FROM temp_otp WHERE email = ? AND phone = ?",
        [email, phone_number]
      );
      let access_token = generateToken(
        user[0].user_id,
        user[0].email,
        user[0].role,
        user[0].lf_id,
        process.env.ACCESS_TOKEN_SECRET,
        process.env.ACCESS_TOKEN_EXPIRY_TIME
      );
      let refresh_token = generateToken(
        user[0].user_id,
        user[0].email,
        user[0].role,
        user[0].lf_id,
        process.env.JWT_SECRET,
        process.env.JWT_EXPIRY_TIME
      );
      await runQuery(
        con,
        "INSERT INTO token (refresh_token, access_token, user_id, created_at) VALUES(?, ? , ?, NOW()) ON DUPLICATE KEY UPDATE refresh_token = ?, access_token = ?, created_at = NOW()",
        [
          refresh_token,
          access_token,
          user[0].user_id,
          refresh_token,
          access_token,
        ]
      );
      let lawfirm = await getLawfirmDetails(user[0].lf_id);
      await con.commit();
      if (lawfirm.length > 0)
        return {
          access_token: access_token,
          refresh_token: refresh_token,
          user_id: user[0].user_id,
          email: user[0].email,
          role: user[0].role,
          lf_id: user[0].lf_id,
          requirePasswordChange: user[0].requirePasswordChange,
          path: lawfirm[0].path,
          lf_color: lawfirm[0].lf_color,
          font: lawfirm[0].font,
        };
      else
        return {
          access_token: access_token,
          refresh_token: refresh_token,
          user_id: user[0].user_id,
          email: user[0].email,
          role: user[0].role,
          lf_id: user[0].lf_id,
          requirePasswordChange: user[0].requirePasswordChange,
        };
    } else {
      throw ErrorHandler.badRequestError("Wrong OTP code. Please try again.");
    }
  } catch (error) {
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

const submitMail = async (email, case_id, first_name, lf_id, user_id) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    let style = await runQuery(
      con,
      "SELECT lf_color, path FROM law_firm WHERE lf_id = ?",
      [lf_id]
    );
    let logo = await runQuery(
      con,
      "SELECT logo_name FROM template_email WHERE lf_id = ? and mail_type = 2",
      [lf_id]
    );
    let mail = await runQuery(
      con,
      "SELECT title,header, body, footer,logo FROM template_email WHERE lf_id = ? and mail_type = 2",
      [lf_id]
    );
    let color = style[0]?.lf_color ? JSON.parse(style[0]?.lf_color) : undefined;
    let user = await runQuery(
      con,
      "Select u.email,ui.title, ui.first_name, ui.middle_name, ui.last_name, ui.dob, ui.gender,ui.home_phone,ui.mb_phone,ui.wrk_phone,ui.adr_1,ui.adr_2,ui.adr_3,ui.town,ui.country,ui.post_code from user_info ui JOIN users u ON ui.user_id = u.user_id where ui.user_id = ?",
      [user_id]
    );
    // Use enhanced submit case client mail with Graph API support
    await mailService.sendEnhancedSubmitCaseClientMail(
      email,
      first_name,
      {
        path: logo[0]?.logo_name ?? style[0]?.path,
        bgColor: color?.bgColor,
        textColor: color?.textColor,
        title: mail[0]?.title,
        header: mail[0]?.header,
        body: mail[0]?.body,
        footer: mail[0]?.footer,
        logo: mail[0]?.logo,
        user: user[0]
      },
      lf_id,
      user[0]?.email
    );
    await con.commit();
    return;
  } catch (err) {
    console.log(err);
    await con.rollback();
    throw ErrorHandler.badRequestError(err.message);
  } finally {
    con.destroy();
  }
};
