import express from "express";
import {
  getAllPayment,
  getAll<PERSON><PERSON><PERSON>,
  createPayment,
} from "#controllers/billing.controller.js";

/**
 * @swagger
 * components:
 *   schemas:
 *     PaymentResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           example: true
 *         message:
 *           type: string
 *           example: Success
 *         data:
 *           type: object
 *
 *     ErrorResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           example: false
 *         message:
 *           type: string
 *           example: Permission denied!
 *
 *     CreatePaymentRequest:
 *       type: object
 *       required:
 *         - amount
 *         - description
 *         - mandate
 *       properties:
 *         amount:
 *           type: number
 *           description: Payment amount
 *         description:
 *           type: string
 *           description: Payment description
 *         mandate:
 *           type: string
 *           description: Mandate ID
 *
 *     GetHistoryRequest:
 *       type: object
 *       required:
 *         - page
 *         - size
 *       properties:
 *         page:
 *           type: number
 *           description: Page number for pagination
 *         size:
 *           type: number
 *           description: Number of items per page
 *         keyword:
 *           type: string
 *           description: Search keyword
 *         startDate:
 *           type: string
 *           format: date
 *           description: Start date for filtering
 *         endDate:
 *           type: string
 *           format: date
 *           description: End date for filtering
 *         lf_id:
 *           type: string
 *           description: Law firm ID (required for Admin role)
 */

/**
 * @swagger
 * tags:
 *   name: Billing
 *   description: Billing management endpoints
 */

const billingRoutes = express.Router();

/**
 * @swagger
 * /private/billing/getAllPayment:
 *   post:
 *     summary: Get all payments for a law firm
 *     tags: [Billing]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               mandate:
 *                 type: string
 *                 description: Mandate ID (required for Admin role only)
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/PaymentResponse'
 *       500:
 *         description: Permission denied
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
billingRoutes.route("/getAllPayment").post(getAllPayment);

/**
 * @swagger
 * /private/billing/getAllHistory:
 *   post:
 *     summary: Get payment history with pagination and filtering
 *     tags: [Billing]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/GetHistoryRequest'
 *     responses:
 *       200:
 *         description: Successfully retrieved history
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/PaymentResponse'
 *       400:
 *         description: Invalid request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Permission denied
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
billingRoutes.route("/getAllHistory").post(getAllHistory);

/**
 * @swagger
 * /private/billing/createPayment:
 *   post:
 *     summary: Create a new payment
 *     tags: [Billing]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreatePaymentRequest'
 *     responses:
 *       200:
 *         description: Payment created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/PaymentResponse'
 *       400:
 *         description: Invalid request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
billingRoutes.route("/createPayment").post(createPayment);

export default billingRoutes;
