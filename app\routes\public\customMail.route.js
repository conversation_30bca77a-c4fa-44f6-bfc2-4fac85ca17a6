import { getStatement } from "#controllers/customMail.controller.js";
import express from "express";

const customMailRoutes = express.Router();

/**
 * @swagger
 * /public/customMail/getStatement:
 *   get:
 *     summary: Get statement document
 *     tags: [Mail]
 *     parameters:
 *       - in: query
 *         name: statement_id
 *         schema:
 *           type: string
 *         required: true
 *         description: Statement ID to retrieve
 *     responses:
 *       200:
 *         description: Statement retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 statement:
 *                   type: object
 *                   description: Statement document
 *       404:
 *         description: Statement not found
 *       500:
 *         description: Error retrieving statement
 */

customMailRoutes.route("/getStatement").get(getStatement);

export default customMailRoutes;
