import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from "#middlewares/ErrorClass.js";
import sgMail from "@sendgrid/mail";
import dotenv from "dotenv";

dotenv.config();
function escapeHtml(unsafe) {
    return unsafe
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;")
        .replace(/\//g, "&#x2F;")
        .replace(/`/g, "&#x60;")
        .replace(/=/g, "&#x3D;");
}
export const tempPasswordMail = async (email, password, first_name) => {
    sgMail.setApiKey(process.env.SENDGRID_API_KEY);
    let msg = {};
    msg = {
        from: { name: "Propero Support", email: "<EMAIL>" },
        to: email,
        subject: `Propero new password`,
        html: `<!DOCTYPE html>
        <html lang="en">

        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Document</title>
            <style>
                * {
                    box-sizing: border-box;
                }

                body {
                    margin: 0;
                    padding: 0;
                    font-family: 'Inter', sans-serif;
                    background-color: #fff;
                }

                p {
                    margin: 0;
                }

                .card {
                    padding: 24px;
                    width: 460px;
                    border: 1px solid rgba(0, 0, 0, 0.75);
                }

                .card .logo-wrapper {
                    text-align: center;
                    align-items: center;
                    margin-bottom: 20px;
                }

                .card .invitation-wrapper {
                    background-color: #F5F5F5;
                    height: 54px;
                    padding: 0 8px;
                }

                .card .invitation-wrapper .invitation-text {
                    line-height: 54px;
                    color: #000;
                    font-weight: 800;
                    font-size: 14px;
                }
                .disable-style-anchor {
                    font-weight: 800;
                    text-decoration: none;
                    color: #667085 !important;
                }

                .card .welcome-text {
                    color: #667085;
                    font-weight: 500;
                    font-size: 14px;
                    margin-bottom: 16px;
                }

                .card .text-credentials {
                    font-weight: 500;
                    font-size: 14px;
                    color: #667085 !important;
                }

                .card .text-change-password {
                    font-weight: 500;
                    font-size: 14px;
                    margin-top: 16px;
                }

                .card .first-name-text {
                    font-weight: 600;
                }

                .card .description-wrapper {
                    margin: 8px;
                    color: #94A3B8;
                    font-size: 11px;
                }

                .card .invite-wrapper {
                    color: #667085;
                    font-size: 14px;
                    line-height: 1.5;
                    margin: 0 8px;
                    gap: 4px;
                }

                .card .button-wrapper {
                    margin: 8px;
                }

                .card .button-wrapper .button {
                    background-color: rgba(213, 49, 49, 0.71);
                    color: #fff;
                    font-size: 14px;
                    height: 40px;
                    border-radius: 16px;
                    cursor: pointer;
                    width: 100%;
                    border: none;
                }

                .card .logo {
                    width: 80px;
                    height: 80px;
                }

                .card .regards-wrapper {
                    margin: 8px;
                    line-height: 1.5;
                    color: #667085;
                    font-size: 14px;
                }

                .card .text-password {
                    border: 1px solid #000;
                    padding: 2px;
                    background-color: #F5F5F5;
                }
            </style>
        </head>

        <body>
            <div class="card">
                <div class="logo-wrapper">
                    <img class="logo" src="${process.env.IMAGE_URL}/static/images/logo.png" alt="Company Logo">
                </div>
                <div class="invitation-wrapper">
                    <p class="invitation-text">Welcome to Propero,</p>
                </div>
                <div class="description-wrapper">
                    <p>
                        This is an automatically generated email, please do not reply.
                    </p>
                </div>
                <div class="invite-wrapper">
                    <p class="welcome-text">
                        Welcome ${first_name},
                    </p>
                    <p class="welcome-text">
                        You are invited you to join <a href="${process.env.CLIENT_URL}" class="disable-style-anchor">Propero</a>, which enables you to generate
                        smart questionnaires for clients and import their data into your case management system.
                    </p>
                    <p class="text-credentials">
                        To access <a class="disable-style-anchor" href="${process.env.CLIENT_URL}">Propero</a>, sign in with the following credentials:
                    </p>
                    <p class="text-credentials">
                        email: ${email}
                    </p>
                    <p class="text-credentials">
                        password:
                        <span class="text-password">
                            ${escapeHtml(password)}
                        </span>
                    </p>
                    <p class="text-change-password">
                        You will be required to change your password after logging in for the first time
                    </p>
                </div>
                <div class="button-wrapper">
                    <a href="${process.env.CLIENT_URL}"
                        style="display: inline-block; padding: 10px 20px; font-size: 14px; color: #fff; background-color: rgba(213, 49, 49, 0.71); text-align: center; text-decoration: none; border-radius: 16px; width: 100%;text-underline-color: rgba(213, 49, 49, 0.71)">
                        <!--[if mso]><i style="mso-font-width:200%;mso-text-raise:100%" hidden>&emsp;</i><span style="mso-text-raise:50%;"><![endif]-->Go
                        to Propero<!--[if mso]></span><i style="mso-font-width:200%;" hidden>&emsp;&#8203;</i><![endif]-->
                    </a>
                </div>
                <div class="regards-wrapper">
                    <p>
                        Regards,
                    </p>
                    <p>
                        Propero Team
                    </p>
                </div>
            </div>
            </div>

        </body>

        </html>`,
    };

    try {
        await sgMail.send(msg).then(() => {
            console.log("Email sent");
        });
    } catch (error) {
        console.log(error);
        if (error.response) {
            console.error(error.response.body);
        }
    }
};
