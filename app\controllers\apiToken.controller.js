import { catchAsync } from "#utils";
import apiTokenService from "../services/apiToken.service.js";
import { roles } from "#middlewares/roles.js";
import <PERSON>rror<PERSON><PERSON><PERSON> from "#middlewares/ErrorClass.js";

/**
 * Create a new API token for AdminAPI user
 */
export const createApiToken = catchAsync(async (req, res) => {
  const { token_name, user_id, expires_at, permissions } = req.body;
  const requestingUserRole = req.user.role;
  const requestingUserLfId = req.user.lf_id;

  // Validate required fields
  if (!token_name || !user_id) {
    return res.status(400).json({
      success: false,
      message: "token_name and user_id are required"
    });
  }

  // Only Admin and LawfirmSuperAdmin can create tokens
  const userRoleString = String(requestingUserRole);
  if (userRoleString !== roles.Admin && userRoleString !== roles.LawfirmSuperAdmin) {
    return res.status(403).json({
      success: false,
      message: "Permission denied! Only Admin or LawfirmSuperAdmin can create API tokens"
    });
  }

  try {
    // For LawfirmSuperAdmin, ensure they can only create tokens for their own law firm
    let lf_id = requestingUserLfId;
    if (userRoleString === roles.Admin && req.body.lf_id) {
      lf_id = req.body.lf_id;
    }

    console.log('Creating API token with:', {
      lf_id,
      user_id,
      token_name,
      created_by: req.user.user_id,
      requesting_user: req.user
    });

    const token = await apiTokenService.createToken({
      lf_id,
      user_id,
      token_name,
      permissions,
      expires_at: expires_at ? new Date(expires_at) : null,
      created_by: req.user.user_id
    });

    return res.status(201).json({
      success: true,
      message: "API token created successfully",
      data: token
    });

  } catch (error) {
    return res.status(400).json({
      success: false,
      message: error.message
    });
  }
});

/**
 * Get all API tokens for a law firm
 */
export const getApiTokens = catchAsync(async (req, res) => {
  const requestingUserRole = req.user.role;
  const requestingUserLfId = req.user.lf_id;

  // Only Admin and LawfirmSuperAdmin can view tokens
  const userRoleString = String(requestingUserRole);
  if (userRoleString !== roles.Admin && userRoleString !== roles.LawfirmSuperAdmin) {
    return res.status(403).json({
      success: false,
      message: "Permission denied!"
    });
  }

  try {
    // For LawfirmSuperAdmin, only show tokens for their law firm
    let lf_id = requestingUserLfId;
    if (userRoleString === roles.Admin && req.params.lf_id) {
      lf_id = req.params.lf_id;
    }

    const tokens = await apiTokenService.getTokensByLawFirm(lf_id);

    return res.status(200).json({
      success: true,
      message: "API tokens retrieved successfully",
      data: tokens
    });

  } catch (error) {
    return res.status(400).json({
      success: false,
      message: error.message
    });
  }
});

/**
 * Revoke an API token
 */
export const revokeApiToken = catchAsync(async (req, res) => {
  const { token_id } = req.params;
  const requestingUserRole = req.user.role;
  const requestingUserLfId = req.user.lf_id;

  // Only Admin and LawfirmSuperAdmin can revoke tokens
  const userRoleString = String(requestingUserRole);
  if (userRoleString !== roles.Admin && userRoleString !== roles.LawfirmSuperAdmin) {
    return res.status(403).json({
      success: false,
      message: "Permission denied!"
    });
  }

  try {
    // For LawfirmSuperAdmin, only allow revoking tokens from their law firm
    let lf_id = requestingUserLfId;
    if (userRoleString === roles.Admin && req.body.lf_id) {
      lf_id = req.body.lf_id;
    }

    const success = await apiTokenService.revokeToken(
      parseInt(token_id),
      lf_id,
      req.user.user_id
    );

    if (success) {
      return res.status(200).json({
        success: true,
        message: "API token revoked successfully"
      });
    } else {
      return res.status(404).json({
        success: false,
        message: "API token not found or already revoked"
      });
    }

  } catch (error) {
    return res.status(400).json({
      success: false,
      message: error.message
    });
  }
});

/**
 * Get current token information (for the token being used)
 */
export const getCurrentTokenInfo = catchAsync(async (req, res) => {
  // This endpoint is only accessible via API token auth
  if (req.user.auth_type !== 'api_token') {
    return res.status(403).json({
      success: false,
      message: "This endpoint requires API token authentication"
    });
  }

  return res.status(200).json({
    success: true,
    message: "Current token information",
    data: {
      token_id: req.user.token_id,
      token_name: req.user.token_name,
      user_email: req.user.email,
      lf_id: req.user.lf_id,
      lf_org_name: req.lawfirm.lf_org_name,
      permissions: req.user.permissions,
      role: req.user.role
    }
  });
});
