import { roles } from "#middlewares/roles.js";
import { customMailService } from "#services";
import { catchAsync } from "#utils";

export const editTemplateMail = catchAsync(async (req, res) => {
  const role = req.user.role;
  const {
    logo_name,
    title,
    header,
    body,
    footer,
    logo,
    lf_id,
    mail_type,
    remind_time,
  } = req.body;
  if (role == roles.LawfirmSuperAdmin && req.user.lf_id == lf_id) {
    let check = await customMailService.editTemplateMail(
      logo_name,
      title,
      header,
      body,
      footer,
      logo,
      lf_id,
      mail_type,
      remind_time
    );
    if (check == 1) {
      return res.status(200).json({
        success: true,
        message: "Success",
      });
    } else
      return res.status(400).json({
        success: false,
        message: "Please check again",
      });
  } else
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
});

export const getTemplateMail = catchAsync(async (req, res) => {
  const role = req.user.role;
  if (role == roles.LawfirmSuperAdmin && req.user.lf_id == req.query.lf_id) {
    let check = await customMailService.getTemplateMail(
      req.query.lf_id,
      req.query.mail_type
    );
    return res.status(200).json({
      success: true,
      message: "Template Mail fetched",
      data: check,
    });
  } else
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
});

export const uploadLogo = catchAsync(async (req, res) => {
  const role = req.user.role;
  if (role == roles.LawfirmSuperAdmin && req.user.lf_id == req.body.lf_id) {
    if (!req.body.file) {
      return res.status(400).json({
        success: false,
        message: "No file uploaded",
      });
    }

    let check = await customMailService.uploadLogo(
      req.body.lf_id,
      req.body.file
    );
    return res.status(200).json({
      success: true,
      message: "Logo uploaded",
      data: check,
    });
  } else {
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
  }
});

export const editStatement = catchAsync(async (req, res) => {
  const role = req.user.role;
  const { lf_statement, lf_id } = req.body;
  req.body;
  if ((role == roles.LawfirmSuperAdmin && req.user.lf_id == lf_id) || role == roles.Admin) {
    let check = await customMailService.editStatement(
      lf_id,
      lf_statement,
    );
    if (check == 1) {
      return res.status(200).json({
        success: true,
        message: "Success",
      });
    } else
      return res.status(400).json({
        success: false,
        message: "Please check again",
      });
  } else
    return res.status(500).json({
      success: false,
      message: "Permission denied!",
    });
});

export const getStatement = catchAsync(async (req, res) => {
  let check = await customMailService.getStatement(
    req.query.lf_id
  );
  return res.status(200).json({
    success: true,
    message: "Statement fetched",
    data: check,
  });
});