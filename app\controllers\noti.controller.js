import { notiService } from "#services";
import { catchAsync } from "#utils";

export const getListNoti = catchAsync(async (req, res) => {
  const { user_id, page, size } = req.query;
  let check = await notiService.getListNoti(user_id, page, size);
  return res.status(200).json({
    success: true,
    message: "Get list noti successfully",
    data: check,
  });
});
export const getCountNoti = catchAsync(async (req, res) => {
  const user_id = req.query;
  let check = await notiService.getCountNoti(user_id.user_id);
  return res.status(200).json({
    success: true,
    message: "Get count noti successfully",
    data: check,
  });
});
export const getNotiById = catchAsync(async (req, res) => {
  const { noti_id } = req.query;
  let check = await notiService.getNotiById(noti_id);
  return res.status(200).json({
    success: true,
    message: "Get noti successfully",
    data: check,
  });
});
export const updateStatusNoti = catchAsync(async (req, res) => {
  const { noti_id, status } = req.body;
  if ([1, 2, 3].includes(status)) {
    let check = await notiService.updateStatusNoti(noti_id, status);
    return res.status(200).json({
      success: true,
      message: "Update status seen noti successfully",
    });
  } else {
    return res.status(500).json({
      success: false,
      message: "Status invalid",
    });
  }
});
export const updateStatusMultiNoti = catchAsync(async (req, res) => {
  const { noti_id, status } = req.body;
  if ([1, 2, 3].includes(status)) {
    let check = await notiService.updateStatusMultiNoti(noti_id, status);
    return res.status(200).json({
      success: true,
      message: "Update status seen noti successfully",
    });
  } else {
    return res.status(500).json({
      success: false,
      message: "Status invalid",
    });
  }
});
export const updateStatusSeenAllNoti = catchAsync(async (req, res) => {
  const { user_id } = req.body;
  let check = await notiService.updateStatusSeenAllNoti(user_id);
  return res.status(200).json({
    success: true,
    message: "Update status seen all noti successfully",
  });
});

export const deleteNotification = catchAsync(async (req, res) => {
  const { noti_id } = req.body;
  let check = await notiService.deleteNotification(noti_id);
  if (check.affectedRows != 0) {
    return res.status(200).json({
      success: true,
      message: "Delete noti successfully",
    });
  } else {
    return res.status(500).json({
      success: false,
      message: "Delete noti failed",
    });
  }
});
