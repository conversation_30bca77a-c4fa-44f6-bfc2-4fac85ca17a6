/**
 * Test for createTemplateFromQuestionnaire function
 * This test verifies that a template can be created from a questionnaire with regenerated IDs
 */

import { templateService } from '../app/services/index.js';
import { questionaireService } from '../app/services/index.js';

describe('createTemplateFromQuestionnaire', () => {
  // Mock data for testing
  const mockQuestionnaireData = {
    id: 'qtn-123',
    name: 'Test Questionnaire',
    description: 'Test questionnaire description',
    price: 100,
    export_config: {
      pms_enabled: true,
      data_enabled: true,
      form_enabled: false,
      form_names: ''
    },
    groups: [
      {
        id: 'group-1',
        name: 'Personal Information',
        tooltips: 'Enter your personal details',
        linkedTo: null,
        questions: [
          {
            id: 'question-1',
            name: 'Full Name',
            description: 'Enter your full name',
            answer_type: 'text',
            required: true,
            tooltips: 'First and last name',
            selectAnswerTable: null,
            linkedTo: null,
            files: null,
            answers: {
              id: 'answer-1',
              name: 'Name Answer',
              modal_id: null,
              modal_type: null,
              expandable: false,
              content: null
            }
          }
        ]
      }
    ],
    conditions: []
  };

  const mockUser = [{ user_id: 'user-123' }];

  beforeEach(() => {
    // Mock the database connection and queries
    jest.clearAllMocks();
  });

  test('should create template from questionnaire with new IDs', async () => {
    // Mock the questionnaire service
    const mockGetQuestionaire = jest.spyOn(questionaireService, 'getQuestionaire')
      .mockResolvedValue(mockQuestionnaireData);

    // Mock database queries
    const mockRunQuery = jest.fn()
      .mockResolvedValueOnce([]) // Check template name doesn't exist
      .mockResolvedValueOnce(mockUser) // Get user
      .mockResolvedValue({}); // All other queries

    const mockGetConnection = jest.fn().mockResolvedValue({
      beginTransaction: jest.fn(),
      commit: jest.fn(),
      rollback: jest.fn(),
      destroy: jest.fn()
    });

    // Mock the pool and fs
    jest.doMock('../app/config/database.js', () => ({
      pool: { getConnection: mockGetConnection }
    }));

    jest.doMock('fs', () => ({
      readFileSync: jest.fn().mockReturnValue('INSERT INTO templates...')
    }));

    // Mock runQuery function
    jest.doMock('../app/utils/query.js', () => ({
      runQuery: mockRunQuery
    }));

    try {
      const result = await templateService.createTemplateFromQuestionnaire(
        'qtn-123',
        'New Template Name',
        'New template description',
        '<EMAIL>'
      );

      // Verify the result
      expect(result).toBeDefined();
      expect(typeof result).toBe('string');
      expect(result).toMatch(/^[a-f0-9]{8}-[a-f0-9]{4}-4[a-f0-9]{3}-[89ab][a-f0-9]{3}-[a-f0-9]{12}$/);

      // Verify questionnaire was fetched
      expect(mockGetQuestionaire).toHaveBeenCalledWith('qtn-123');

      // Verify database operations
      expect(mockGetConnection).toHaveBeenCalled();
      expect(mockRunQuery).toHaveBeenCalledWith(
        expect.anything(),
        "SELECT * FROM templates WHERE tem_name = ?",
        ['New Template Name']
      );
      expect(mockRunQuery).toHaveBeenCalledWith(
        expect.anything(),
        "SELECT user_id FROM users WHERE email = ?",
        ['<EMAIL>']
      );

    } catch (error) {
      console.error('Test failed:', error);
      throw error;
    }
  });

  test('should throw error if questionnaire not found', async () => {
    // Mock questionnaire service to return empty result
    const mockGetQuestionaire = jest.spyOn(questionaireService, 'getQuestionaire')
      .mockResolvedValue([]);

    const mockGetConnection = jest.fn().mockResolvedValue({
      beginTransaction: jest.fn(),
      rollback: jest.fn(),
      destroy: jest.fn()
    });

    jest.doMock('../app/config/database.js', () => ({
      pool: { getConnection: mockGetConnection }
    }));

    await expect(
      templateService.createTemplateFromQuestionnaire(
        'non-existent-qtn',
        'New Template',
        'Description',
        '<EMAIL>'
      )
    ).rejects.toThrow('Questionnaire not found');
  });

  test('should throw error if template name already exists', async () => {
    // Mock questionnaire service
    const mockGetQuestionaire = jest.spyOn(questionaireService, 'getQuestionaire')
      .mockResolvedValue(mockQuestionnaireData);

    // Mock database to return existing template
    const mockRunQuery = jest.fn()
      .mockResolvedValueOnce([{ id: 'existing-template' }]); // Template name exists

    const mockGetConnection = jest.fn().mockResolvedValue({
      beginTransaction: jest.fn(),
      rollback: jest.fn(),
      destroy: jest.fn()
    });

    jest.doMock('../app/config/database.js', () => ({
      pool: { getConnection: mockGetConnection }
    }));

    jest.doMock('../app/utils/query.js', () => ({
      runQuery: mockRunQuery
    }));

    await expect(
      templateService.createTemplateFromQuestionnaire(
        'qtn-123',
        'Existing Template Name',
        'Description',
        '<EMAIL>'
      )
    ).rejects.toThrow('Template with the same name already exists.');
  });

  test('should throw error if user not found', async () => {
    // Mock questionnaire service
    const mockGetQuestionaire = jest.spyOn(questionaireService, 'getQuestionaire')
      .mockResolvedValue(mockQuestionnaireData);

    // Mock database queries
    const mockRunQuery = jest.fn()
      .mockResolvedValueOnce([]) // Template name doesn't exist
      .mockResolvedValueOnce([]); // User not found

    const mockGetConnection = jest.fn().mockResolvedValue({
      beginTransaction: jest.fn(),
      rollback: jest.fn(),
      destroy: jest.fn()
    });

    jest.doMock('../app/config/database.js', () => ({
      pool: { getConnection: mockGetConnection }
    }));

    jest.doMock('../app/utils/query.js', () => ({
      runQuery: mockRunQuery
    }));

    await expect(
      templateService.createTemplateFromQuestionnaire(
        'qtn-123',
        'New Template',
        'Description',
        '<EMAIL>'
      )
    ).rejects.toThrow('User not found');
  });
});
