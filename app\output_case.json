{"id": "2c7ff974-6be4-4bd3-b99b-c51b9b6720c9", "lf_org_name": "Law firm trang", "phone_number": "0333462354", "name": "Test Excel", "status": 6, "description": "undefined - pham 123", "created_at": "2025-01-09 04:07:43", "qtn_id": "e109bc20-3c37-4d5e-9de3-58b744d3fb37", "lf_id": "114", "user_id": 426, "case_id_pms": null, "assigned_by": 265, "groups": [{"id": "8627850b-eefe-4bce-dc83-1d06021f2e14", "name": "About you", "tooltips": "Your contact details and your relationship to the Deceased.", "linkedTo": null, "conditions": null, "questions": [{"id": "c61073ea-7d15-4509-c965-ce516227a66d", "name": "Terms of business question", "description": "Please read the attached letter and documents and confirm your agreement to the terms and conditions contained in them.", "answer_type": "7", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": null, "answers": {"id": "b1897c84-2c7b-4247-8f39-380ee1198b53", "name": "Terms of business answer", "modal_id": null, "modal_type": null, "content": [{"id": "53207dd4-e5bb-4f6b-a864-aff12c100e55", "name": "I agree to the terms and conditions contained in these documents.", "description": ""}]}}, {"id": "828ea38c-c956-40de-af39-b0cb794793fd", "name": "Customer details question", "description": "Please provide some details about yourself and your relationship to the Deceased.", "answer_type": "10", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": null, "answers": {"id": "5411a1d1-68bf-474b-e537-3837d31883d5", "name": "Customer details answer", "modal_id": 150, "modal_type": null, "content": null}}]}, {"id": "5a9aee02-72ee-4c3c-9104-a330c813a03a", "name": "The Deceased", "tooltips": "Details about the Deceased.", "linkedTo": null, "conditions": null, "questions": [{"id": "1de310a9-6d53-47ab-fab0-91e91779127b", "name": "Deceased details question", "description": "Please provide us with some details about the Deceased.", "answer_type": "10", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": null, "answers": {"id": "6f9e7964-697c-40d4-8393-f7b73f67a712", "name": "Deceased details answer", "modal_id": 151, "modal_type": null, "content": null}}, {"id": "db90cf66-e34f-4562-e3ad-c45878e41740", "name": "Valid Will question", "description": "Did the Deceased make a valid Will?", "answer_type": "6", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": null, "answers": {"id": "807e0781-7b90-48b7-f94e-05c571ea78ab", "name": "Valid Will answer", "modal_id": null, "modal_type": null, "content": [{"id": "b35362d1-1fed-4411-a196-fa71e9510e53", "name": "Yes", "description": ""}, {"id": "02701a94-e7cb-4582-bacd-9d07aa11e6d5", "name": "No", "description": ""}]}}, {"id": "e18c31db-4ccf-42f1-e8bb-2b4f9331ff32", "name": "Marital status question", "description": "What was the Deceased's marital status?", "answer_type": "8", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": null, "answers": {"id": "526a1e65-2859-49d7-f78e-efbc3ca15e86", "name": "Marital status answer", "modal_id": null, "modal_type": null, "content": [{"id": "bc9cbc74-5886-4c43-ea76-f52e12f1bdfa", "name": "Married", "description": ""}, {"id": "0a0a49b3-b544-4fdc-d245-c14777ec156a", "name": "In a Civil Partnership", "description": ""}, {"id": "9aaf20ff-9f82-4102-8a08-1aced2bc5010", "name": "Single", "description": ""}, {"id": "c5b3b5a1-b1ab-4a22-8723-d9e65a3f6596", "name": "Divorced or dissolved Civil Partnership", "description": ""}, {"id": "fd37344c-bf67-46fb-97ed-d24ccef93d12", "name": "Separated", "description": ""}, {"id": "e4086c48-4c6d-499a-8e8f-99019146f74d", "name": "Widowed or Surviving Civil Partner", "description": ""}]}}, {"id": "62ef780b-71ec-4579-c47c-2383952980e2", "name": "Deceased other names question", "description": "Was the Deceased known by any other names?", "answer_type": "6", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": null, "answers": {"id": "7e3b6f0a-5525-418f-fe26-5b5b9a828630", "name": "Deceased other names ", "modal_id": null, "modal_type": null, "content": [{"id": "ce6c7080-76da-4f8f-896a-264b6b78abd0", "name": "Yes", "description": ""}, {"id": "b9c026fc-57e9-4870-ec6e-951d767e351d", "name": "No", "description": ""}]}}, {"id": "259f714e-dfc3-4785-ec4d-111455641c4e", "name": "Deceased other names details question", "description": "What other name or names was the Deceased know by?", "answer_type": "1", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": ["62ef780b-71ec-4579-c47c-2383952980e2"], "answers": {"id": "bee87916-d5ba-42a9-a39a-808ef8364eea", "name": "Deceased other names details answer", "modal_id": null, "modal_type": null, "content": null}}, {"id": "3466e88a-a26c-490e-dd6f-dee26ed5cc0a", "name": "Surviving spouse question", "description": "Is the Deceased's spouse still alive?", "answer_type": "6", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": ["e18c31db-4ccf-42f1-e8bb-2b4f9331ff32"], "answers": {"id": "06855471-8a0f-40f8-b66d-a786e0345c7e", "name": "Surviving spouse answer", "modal_id": null, "modal_type": null, "content": [{"id": "ad52d453-d060-4cec-d4ed-1f804cd21f75", "name": "Yes", "description": ""}, {"id": "6aa22b13-200a-4fa0-e617-f55915419c5c", "name": "No", "description": ""}]}}, {"id": "5130c199-bedb-4438-964e-40c81f2a5d1d", "name": "Surviving civil partner question", "description": "Is the Deceased's Civil Partner still alive?", "answer_type": "6", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": ["e18c31db-4ccf-42f1-e8bb-2b4f9331ff32"], "answers": {"id": "0ad18baa-ec92-49ed-fc5f-edc0bce64513", "name": "Surviving civil partner answer", "modal_id": null, "modal_type": null, "content": [{"id": "910ae66e-12e1-4c43-cdbf-d23a4e6bff95", "name": "Yes", "description": ""}, {"id": "58fa8db0-ab23-4152-c648-644f6e43e99d", "name": "No", "description": ""}]}}, {"id": "7ca1e251-256f-4cc8-a2f7-9537a6ed469e", "name": "Deceased care home question", "description": "Was the Deceased in a care home?", "answer_type": "6", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": null, "answers": {"id": "b92e59d8-ffba-47e8-ddc4-610f5258c9aa", "name": "Deceased care home answer", "modal_id": null, "modal_type": null, "content": [{"id": "56dd958d-65cb-4953-9991-9f6a4e9907d2", "name": "Yes", "description": ""}, {"id": "42820adc-f277-4a5e-cfa7-0a57a9a193e2", "name": "No", "description": ""}]}}, {"id": "6205d1d1-501a-4b21-bf7b-c64fcd16335a", "name": "Property question", "description": "Was the Deceased the owner or tenant of any property?", "answer_type": "6", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": null, "answers": {"id": "61791ec2-d6cb-4f38-8c82-9679eb8d1be5", "name": "Property answer", "modal_id": null, "modal_type": null, "content": [{"id": "8764f419-bd90-4a2d-b6a6-2d0374c2ce21", "name": "Yes", "description": ""}, {"id": "9b0d4b53-2fae-491f-eb2f-71d2063c3a0d", "name": "No", "description": ""}]}}, {"id": "d02f709f-6fb6-487f-deec-2c3d3cc87b41", "name": "Bank question", "description": "Did the Deceased have any bank or building society accounts?", "answer_type": "6", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": null, "answers": {"id": "271b8dac-c809-4e68-a5df-bed594227c1f", "name": "Bank answer", "modal_id": null, "modal_type": null, "content": [{"id": "4c4bb0c5-7642-413a-a58d-143ba1effc44", "name": "Yes", "description": ""}, {"id": "4099063a-04ae-45cd-ffd9-6cdc981fa692", "name": "No", "description": ""}]}}, {"id": "7b87b8da-5675-4d9f-cb0e-7bc33a2efb14", "name": "National Savings question", "description": "Did the Deceased have any National Savings & Investment accounts?", "answer_type": "6", "files": [], "required": 1, "tooltips": "For example, premium bonds, national savings and investment accounts, etc.", "selectAnswerTable": null, "conditions": null, "linkedTo": null, "answers": {"id": "5eec3cfc-4dba-4e1d-ac4b-0396815487a6", "name": "National Savings answer", "modal_id": null, "modal_type": null, "content": [{"id": "44fa76cb-5981-4ad2-99fe-76d7ca6382a0", "name": "Yes", "description": ""}, {"id": "2320b095-b640-45a7-a5fe-24afc80d6f82", "name": "No", "description": ""}]}}, {"id": "0de03047-f0a3-47a7-b5f7-ae3a4348eb48", "name": "Stock question", "description": "Did the Deceased own any stocks or shares?", "answer_type": "6", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": null, "answers": {"id": "5f0faade-7704-4712-9dcd-02b9d233167e", "name": "Stock answer", "modal_id": null, "modal_type": null, "content": [{"id": "a690b3ef-6854-4cb6-c6a4-ad8ba7648948", "name": "Yes", "description": ""}, {"id": "a054cfeb-28c1-495b-fc38-e96f3d3b082e", "name": "No", "description": ""}]}}, {"id": "50502ccc-6ea5-4814-85ed-90cd62f36c9a", "name": "Investments question", "description": "Did the Deceased own any other investments?", "answer_type": "6", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": null, "answers": {"id": "ce4fc396-97b2-4e91-e3ab-438405450eca", "name": "Investments answer", "modal_id": null, "modal_type": null, "content": [{"id": "3bc851ce-1cac-4f6a-adb1-fb9498346097", "name": "Yes", "description": ""}, {"id": "ccc20525-3133-4efe-b2c8-d6d7a14b67f5", "name": "No", "description": ""}]}}, {"id": "43a3e45d-153d-46c9-e53d-62db8f976791", "name": "Pensions question", "description": "Did the Deceased have any pensions?", "answer_type": "6", "files": [], "required": 1, "tooltips": "Whether state or private and whether or not the Deceased was drawing a pension.", "selectAnswerTable": null, "conditions": null, "linkedTo": null, "answers": {"id": "626a01f5-0fd5-465f-f7ab-ad97bb73922d", "name": "Pensions answer", "modal_id": null, "modal_type": null, "content": [{"id": "076f8e80-99a5-436a-e63d-11f477ba1bc0", "name": "Yes", "description": ""}, {"id": "562c4707-1aca-4fab-9cba-1b1eafd192dd", "name": "No", "description": ""}]}}, {"id": "369e7172-fbd1-4d09-b57b-29f182b3e96a", "name": "Life insurance question", "description": "Did the Deceased have any life policies?", "answer_type": "6", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": null, "answers": {"id": "60f486bf-3aaa-467e-cb9d-0ef5bf5439d6", "name": "Life insurance answer", "modal_id": null, "modal_type": null, "content": [{"id": "404109c2-03de-487c-a60f-b883ba9dacb6", "name": "Yes", "description": ""}, {"id": "bf952c0c-f636-420c-caae-b8d25f9ff8f7", "name": "No", "description": ""}]}}, {"id": "a81d864a-f9d2-4dbc-e694-ac78b7724319", "name": "Other assets question", "description": "Did the Deceased have any other assets?", "answer_type": "6", "files": [], "required": 1, "tooltips": "Including interests in a business or farm, benefit from assets held in trust, transfers of assets where the Deceased continued to benefit from the assets, foreign assets, payment of life insurance premiums on policies not benefitting the Deceased, and disposals of or changes to distribution of pensions in the two years prior to death.", "selectAnswerTable": null, "conditions": null, "linkedTo": null, "answers": {"id": "aaf1ed85-f150-4239-ef88-cd508482251f", "name": "Other assets answer", "modal_id": null, "modal_type": null, "content": [{"id": "ff1b9d17-a3dc-4d16-91b7-2aa038a705f2", "name": "Yes", "description": ""}, {"id": "cacc9f89-2094-4462-f980-068f186992a1", "name": "No", "description": ""}]}}, {"id": "adf2910c-36b3-48a0-fac3-ec72da618419", "name": "Death certificate question", "description": "Please upload a copy of the death certificate", "answer_type": "11", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": null, "answers": {"id": "75a7c853-5df8-4e41-d5a7-90233cb639d3", "name": "Death certificate answer", "modal_id": null, "modal_type": null, "content": null}}, {"id": "47265d3c-67ce-4de0-de55-07857e67432a", "name": "PETS question", "description": "Did the Deceased make any gifts totalling £3,000 in any given year in the last seven years prior to death?", "answer_type": "6", "files": [], "required": 1, "tooltips": "Exclude any gifts of less than £250 and exclude Christmas and birthday gifts.", "selectAnswerTable": null, "conditions": null, "linkedTo": null, "answers": {"id": "4654f9e1-dad2-46c3-f253-90377f536fc5", "name": "PETS answer", "modal_id": null, "modal_type": null, "content": [{"id": "2a7cfc0f-2aa9-430b-fbb2-eb09f78d2967", "name": "Yes", "description": ""}, {"id": "bfad542e-8c8c-439e-8643-ff64b4fe882b", "name": "No", "description": ""}]}}, {"id": "09020ae8-e95d-49aa-bfdc-972a5741abea", "name": "IHT reference question", "description": "Please obtain a copy of the Inheritance Tax reference for the Deceased's estate and upload it", "answer_type": "11", "files": [], "required": 1, "tooltips": "You can apply for the reference using the following link: https://www.tax.service.gov.uk/shortforms/form/CAR_IHT_Pre_Ref", "selectAnswerTable": null, "conditions": null, "linkedTo": null, "answers": {"id": "c2f6b396-1b6b-4265-90fa-f3e3a6eeb87b", "name": "IHT reference answer", "modal_id": null, "modal_type": null, "content": null}}, {"id": "0ab92110-8fd6-4312-8c77-c2d3231ceed6", "name": "Personal loans question", "description": "Did the Deceased owe any personal loans to anyone?", "answer_type": "6", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": null, "answers": {"id": "482b0940-7167-4639-e539-0f5d0579c6c9", "name": "Personal loans answer", "modal_id": null, "modal_type": null, "content": [{"id": "ad3d19de-b849-4514-e285-55845661a8bb", "name": "Yes", "description": ""}, {"id": "933e2205-1ffe-43df-ae64-3becc9664f15", "name": "No", "description": ""}]}}, {"id": "d1cccc73-2835-4ad9-b8b4-8a611c168d4e", "name": "Credit cards question", "description": "Did the Deceased have any credit or store cards?", "answer_type": "6", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": null, "answers": {"id": "2de66bc3-6f4a-432e-f6e3-654f4c24f52a", "name": "Credit cards answer", "modal_id": null, "modal_type": null, "content": [{"id": "30ad1c4e-6274-4483-827c-cf3da6501d1a", "name": "Yes", "description": ""}, {"id": "ba1fffdc-1680-4975-89ec-fea966834f63", "name": "No", "description": ""}]}}, {"id": "1baab982-2995-4618-b029-4361f1a225c8", "name": "Liabilities question", "description": "Did the Deceased have any liabilities apart from mortgages, personal loans or credit or store cards?", "answer_type": "6", "files": [], "required": 1, "tooltips": "Do not include funeral or associated expenses.", "selectAnswerTable": null, "conditions": null, "linkedTo": null, "answers": {"id": "80c52737-067f-42a6-b285-106c4efea2f3", "name": "Liabilities answer", "modal_id": null, "modal_type": null, "content": [{"id": "31325550-f3bd-4a4e-f2f0-733a71a38517", "name": "Yes", "description": ""}, {"id": "8e28ced5-624d-4b5c-f68c-bc6ad5d44b17", "name": "No", "description": ""}]}}]}, {"id": "006a6661-b331-49cd-bde0-afae844b2add", "name": "Will", "tooltips": "Details about the Deceased's Will.", "linkedTo": ["db90cf66-e34f-4562-e3ad-c45878e41740"], "conditions": null, "questions": [{"id": "c5ddcb8d-2493-4b25-f642-f84a414c9d95", "name": "Original will question", "description": "Do you have the original Will?", "answer_type": "6", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": null, "answers": {"id": "3b84e666-2856-42bd-c631-a56c6e33513e", "name": "Original will answer", "modal_id": null, "modal_type": null, "content": [{"id": "fdf26a7c-1ccf-4c19-f8cd-3d37aa885fb0", "name": "Yes", "description": ""}, {"id": "c6c25cfe-0f2f-49c4-dba7-090244527b94", "name": "No", "description": ""}]}}, {"id": "0a6d6935-a1e7-4cde-bbd7-0457831dffd4", "name": "<PERSON><PERSON> will question", "description": "Do you have a copy of the Will?", "answer_type": "6", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": ["c5ddcb8d-2493-4b25-f642-f84a414c9d95"], "answers": {"id": "fc640b54-ff4c-45ba-b91d-018592a6cb5c", "name": "<PERSON><PERSON> will answer", "modal_id": null, "modal_type": null, "content": [{"id": "6f5782ca-38d0-4456-a7c6-54e1311c4efc", "name": "Yes", "description": ""}, {"id": "06f77924-4062-4ffd-facf-8271495d6b37", "name": "No", "description": ""}]}}, {"id": "9ff6ac3d-a0e8-46cd-f7ba-39e9cf11f4e3", "name": "Upload will question", "description": "Please upload a copy of the Will", "answer_type": "11", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": ["c5ddcb8d-2493-4b25-f642-f84a414c9d95", "0a6d6935-a1e7-4cde-bbd7-0457831dffd4"], "answers": {"id": "db19eae4-1a22-4658-a5d8-914e5f5ffc2c", "name": "Upload will answer", "modal_id": null, "modal_type": null, "content": null}}, {"id": "9c366535-8b43-4835-8494-a4cdda0e0b8b", "name": "Codicils question", "description": "Are there any codicils with the Will?", "answer_type": "6", "files": [], "required": 1, "tooltips": "A codicil is a written document that refers to the Will and makes changes to it.", "selectAnswerTable": null, "conditions": null, "linkedTo": null, "answers": {"id": "57bd0f2c-c4df-46fd-8fbf-05134c66683e", "name": "Codicils answer", "modal_id": null, "modal_type": null, "content": [{"id": "94870fbe-fd2e-4284-f6a6-091ba1d4f02d", "name": "Yes", "description": ""}, {"id": "a6426889-6409-462d-e062-215232426af8", "name": "No", "description": ""}]}}, {"id": "38893b82-be84-4a07-b240-f43529abcdf6", "name": "Minor beneficiaries question", "description": "Are any of the beneficiaries of the Will aged under 18?", "answer_type": "6", "files": [], "required": 1, "tooltips": "Beneficiaries are persons who have been left a gift or a share of the Deceased's estate in the Will.  The 'estate' is the net value of the deceased's assets after deducting liabilities.", "selectAnswerTable": null, "conditions": null, "linkedTo": ["db90cf66-e34f-4562-e3ad-c45878e41740"], "answers": {"id": "dacfc524-c26e-4e1d-8f95-2f93a40f15d7", "name": "Minor beneficiaries answer", "modal_id": null, "modal_type": null, "content": [{"id": "49754d17-5f58-4bae-a70e-448f29b23b68", "name": "Yes", "description": ""}, {"id": "bdd12fcc-54ec-42a1-9b40-ead9eb9d21ba", "name": "No", "description": ""}]}}, {"id": "0d4613e0-98a7-49df-f253-57b63abeb174", "name": "Executors question", "description": "Please provide details of all executors.", "answer_type": "10", "files": [], "required": 1, "tooltips": "Executors are the persons named in the Will to carry out the Deceased's wishes under the Will.  After you have entered the first executor's details, please click the '+' button to add each additional executor named.", "selectAnswerTable": null, "conditions": null, "linkedTo": null, "answers": {"id": "81cb034d-cbf3-43d8-cae5-ccfdef87c580", "name": "Executors answer", "modal_id": 152, "modal_type": null, "content": null}}]}, {"id": "2dcff60a-f7c8-477e-8a68-0babcfaa630b", "name": "Intestacy", "tooltips": "Who is entitled to benefit from the estate where there is no valid Will.", "linkedTo": ["db90cf66-e34f-4562-e3ad-c45878e41740"], "conditions": null, "questions": [{"id": "ffbf0e02-60ca-48f3-c1f9-6dbda2756994", "name": "Spouse details question", "description": "Please provide contact details for the surviving spouse.", "answer_type": "10", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": ["db90cf66-e34f-4562-e3ad-c45878e41740", "3466e88a-a26c-490e-dd6f-dee26ed5cc0a"], "answers": {"id": "c987890f-4be0-4387-f120-2a02b5687576", "name": "Spouse details answer", "modal_id": 170, "modal_type": null, "content": null}}, {"id": "ea92de8a-391e-452a-8319-a030af00206b", "name": "Civil Partner details question", "description": "Please provide contact details for the surviving Civil Partner", "answer_type": "10", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": ["db90cf66-e34f-4562-e3ad-c45878e41740", "5130c199-bedb-4438-964e-40c81f2a5d1d"], "answers": {"id": "1fbc4ed7-35f7-49fb-8515-3bde770e447f", "name": "Civil Partner details answer", "modal_id": 170, "modal_type": null, "content": null}}, {"id": "33410276-f957-48da-8966-d354108f810a", "name": "Children question", "description": "Did the Deceased have any children?", "answer_type": "6", "files": [], "required": 1, "tooltips": "Regardless of their age, and including illegitimate or adopted children, but not step-children.  Also include any children who died before the deceased.", "selectAnswerTable": null, "conditions": null, "linkedTo": null, "answers": {"id": "d86c6a7f-75dc-4dc9-8dae-9a391628b698", "name": "Children answer", "modal_id": null, "modal_type": null, "content": [{"id": "9263d667-1c94-4937-e4ed-ed50c2321f5e", "name": "Yes", "description": ""}, {"id": "2090dd44-d8f6-456d-a558-68a250024043", "name": "No", "description": ""}]}}, {"id": "5599b945-88a4-4189-ea93-13215b54a574", "name": "Deceased children question", "description": "Did any of the Deceased's children die before the Deceased?", "answer_type": "6", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": ["33410276-f957-48da-8966-d354108f810a"], "answers": {"id": "5ad6ca7a-f819-4e02-9b5f-ad37cf990958", "name": "Deceased children answer", "modal_id": null, "modal_type": null, "content": [{"id": "ee2ec44e-66ce-4425-b902-e042430da433", "name": "Yes", "description": ""}, {"id": "68564903-94a4-4391-fe9e-4cfd2669bfa5", "name": "No", "description": ""}]}}, {"id": "aa8727da-1a37-4982-c057-bb368e347cbf", "name": "Children of deceased question", "description": "Did any of the children who died before the Deceased have any children?", "answer_type": "6", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": ["5599b945-88a4-4189-ea93-13215b54a574"], "answers": {"id": "6312f6ca-a694-4655-8a5a-817a4984a6f2", "name": "Children of deceased answer", "modal_id": null, "modal_type": null, "content": [{"id": "e0d43948-8e6a-4dc3-8db3-184d9f155d6d", "name": "Yes", "description": ""}, {"id": "66c82f21-4bea-4648-cf0f-f51d29ea5db1", "name": "No", "description": ""}]}}, {"id": "ef9cdd42-0b27-4dff-f532-ff101c9ee54e", "name": "Parents question", "description": "Did any parents survive the Deceased?", "answer_type": "6", "files": [], "required": 1, "tooltips": "If the Deceased was single and had no children, any surviving parents will inherit the estate.", "selectAnswerTable": null, "conditions": null, "linkedTo": ["33410276-f957-48da-8966-d354108f810a"], "answers": {"id": "a70de4c9-a99c-475b-aa72-d2342327d51d", "name": "Parents answer", "modal_id": null, "modal_type": null, "content": [{"id": "070e3c5b-044a-4bae-ae33-932b1f3ab849", "name": "Yes", "description": ""}, {"id": "0159a9ce-a8f1-4c34-ae82-860909f871de", "name": "No", "description": ""}]}}, {"id": "e6c8a9c3-4dbf-4bc1-ead0-8958574be5ce", "name": "Full siblings question", "description": "Did the Deceased have any brothers or sisters?", "answer_type": "6", "files": [], "required": 1, "tooltips": "Not including half-brothers or sisters.  Include any full brothers or sisters who died before the Deceased.", "selectAnswerTable": null, "conditions": null, "linkedTo": ["ef9cdd42-0b27-4dff-f532-ff101c9ee54e"], "answers": {"id": "57dd26a1-6ac3-49dd-9fcb-31fd5360d99c", "name": "Full siblings answer", "modal_id": null, "modal_type": null, "content": [{"id": "1d663e92-246c-4959-94e7-326af2bff9dc", "name": "Yes", "description": ""}, {"id": "2eabc0a9-4d3c-47b1-ec26-f949d02d5804", "name": "No", "description": ""}]}}, {"id": "1ccec419-7882-4f16-ef75-0caef1f5abcf", "name": "Deceased full siblings question", "description": "Did the Deceased have any brothers or sisters who died before the Deceased?", "answer_type": "6", "files": [], "required": 1, "tooltips": "Not including half brothers or sisters.", "selectAnswerTable": null, "conditions": null, "linkedTo": ["e6c8a9c3-4dbf-4bc1-ead0-8958574be5ce"], "answers": {"id": "eaf8fa2c-6655-4863-ebe1-f94c473b0789", "name": "Deceased full siblings answer", "modal_id": null, "modal_type": null, "content": [{"id": "9b354e44-a53f-4f8e-80c5-34108e28f77d", "name": "Yes", "description": ""}, {"id": "65723e4b-d82d-453f-b3cb-8b2184d4c61a", "name": "No", "description": ""}]}}, {"id": "3872ad4b-76c6-4e68-8a54-71db4e4801a7", "name": "Deceased full siblings children question", "description": "Did any of the full brothers or sisters who died before the Deceased have any children who are still alive?", "answer_type": "6", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": ["1ccec419-7882-4f16-ef75-0caef1f5abcf"], "answers": {"id": "3734806c-1455-4a2e-9ad8-36e7d68b5c2a", "name": "Deceased full siblings children answer", "modal_id": null, "modal_type": null, "content": [{"id": "ec3487f8-5e54-493b-a382-abda396078e8", "name": "Yes", "description": ""}, {"id": "c7bdb52f-827b-4c74-85d2-b9035bf57d18", "name": "No", "description": ""}]}}, {"id": "2827e742-c036-4fe6-9ab7-4a7d824c3a44", "name": "Half siblings question", "description": "Did the Deceased have any half-brothers or sisters?", "answer_type": "6", "files": [], "required": 1, "tooltips": "Including any who died before the Deceased.", "selectAnswerTable": null, "conditions": null, "linkedTo": ["e6c8a9c3-4dbf-4bc1-ead0-8958574be5ce"], "answers": {"id": "decdba25-96e7-44e9-9d84-80286d0a59de", "name": "Half siblings answer", "modal_id": null, "modal_type": null, "content": [{"id": "3857f84f-384b-4924-97ae-6d665f5d0a00", "name": "Yes", "description": ""}, {"id": "32ff8079-3abb-4766-d57a-f360f880afa0", "name": "No", "description": ""}]}}, {"id": "43f2f2d1-b60d-4701-f00e-314a0d190a34", "name": "Deceased half siblings question", "description": "Did the Deceased have any half-brothers or sisters who died before the Deceased?", "answer_type": "6", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": ["2827e742-c036-4fe6-9ab7-4a7d824c3a44"], "answers": {"id": "015d58f3-4f45-4012-c993-a271fc08e5ee", "name": "Deceased half siblings answer", "modal_id": null, "modal_type": null, "content": [{"id": "d22f0fdf-f3d2-49cf-e1b7-faab3be9864c", "name": "Yes", "description": ""}, {"id": "b46cf7fe-17d1-4520-adec-75e789835a80", "name": "No", "description": ""}]}}, {"id": "f91321b9-c879-4a30-be31-df77077f4979", "name": "Predeceased half siblings children question", "description": "Did any of the half-brothers or sisters who died before the Deceased have any children who are still alive?", "answer_type": "6", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": ["43f2f2d1-b60d-4701-f00e-314a0d190a34"], "answers": {"id": "162128d1-b13c-4cdd-ed2c-30b33fc55ce8", "name": "Predeceased half siblings children answer", "modal_id": null, "modal_type": null, "content": [{"id": "3eefebb7-1fd3-4f54-91ed-e86cb174398e", "name": "Yes", "description": ""}, {"id": "01439819-11c2-4656-84e0-384118b88495", "name": "No", "description": ""}]}}, {"id": "ed6a37cf-4163-49b0-edb0-a1daa8414891", "name": "Grandparents question", "description": "Are any of the Deceased's grandparents still alive?", "answer_type": "6", "files": [], "required": 1, "tooltips": "These grandparents will inherit the estate.", "selectAnswerTable": null, "conditions": null, "linkedTo": ["2827e742-c036-4fe6-9ab7-4a7d824c3a44"], "answers": {"id": "b8be9145-33f3-47b2-ee4d-11bf275dc852", "name": "Grandparents answer", "modal_id": null, "modal_type": null, "content": [{"id": "7105d87e-6e80-48c4-eab1-e0c6d96c0c21", "name": "Yes", "description": ""}, {"id": "ddceddc1-77c4-41c4-d85b-3e3f2f99b890", "name": "No", "description": ""}]}}, {"id": "1ea853a4-3695-4d30-85f4-aaafa2864cfa", "name": "Aunts and uncles question", "description": "Did the Deceased have any aunts or uncles?", "answer_type": "6", "files": [], "required": 1, "tooltips": "Not including half-aunts or uncles.  Include any aunts or uncles who died before the Deceased.", "selectAnswerTable": null, "conditions": null, "linkedTo": ["ed6a37cf-4163-49b0-edb0-a1daa8414891"], "answers": {"id": "733e4791-5504-4050-e93c-15da99f95a2b", "name": "Aunts and uncles answer", "modal_id": null, "modal_type": null, "content": [{"id": "ad75bc65-ec41-42e4-a239-8382ef7b0095", "name": "Yes", "description": ""}, {"id": "dda64e11-69b4-4587-a69a-974910b5d7b1", "name": "No", "description": ""}]}}, {"id": "80e32b9c-9981-48d7-f6fa-dac553192f74", "name": "Predeceased aunts or uncles question", "description": "Did the Deceased have any aunts or uncles who died before the Deceased?", "answer_type": "6", "files": [], "required": 1, "tooltips": "Not including half aunts or uncles.", "selectAnswerTable": null, "conditions": null, "linkedTo": ["1ea853a4-3695-4d30-85f4-aaafa2864cfa"], "answers": {"id": "8252222a-593b-473b-a919-bf668da1c227", "name": "Predeceased aunts or uncles answer", "modal_id": null, "modal_type": null, "content": [{"id": "cfc30ef2-caac-41dd-fea0-13f8e342d5dc", "name": "Yes", "description": ""}, {"id": "0c6928f1-0abe-4d73-f8bc-58e9a84b8492", "name": "No", "description": ""}]}}, {"id": "5e2c0208-7141-4f52-85f1-6fa61c060cbc", "name": "Predeceased aunts or uncles children question", "description": "Did any of the aunts or uncles who died before the Deceased have any children who are still alive?", "answer_type": "6", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": ["80e32b9c-9981-48d7-f6fa-dac553192f74"], "answers": {"id": "3257c274-29f9-49f0-9599-68702475c06d", "name": "Predeceased aunts or uncles children answer", "modal_id": null, "modal_type": null, "content": [{"id": "25cf6d00-eb64-4510-c241-345f905c232d", "name": "Yes", "description": ""}, {"id": "d69012b2-f8aa-47bb-90d3-cc871b9d3dce", "name": "No", "description": ""}]}}, {"id": "a29ed948-556d-4023-ba7c-3c6a9b9adfcd", "name": "Half aunts and uncles question", "description": "Did the Deceased have any half-aunts or uncles?", "answer_type": "6", "files": [], "required": 1, "tooltips": "Including any who died before the Deceased.", "selectAnswerTable": null, "conditions": null, "linkedTo": ["1ea853a4-3695-4d30-85f4-aaafa2864cfa"], "answers": {"id": "de3b8d42-88a6-4851-ac79-a471e9b0d2b1", "name": "Half aunts and uncles question", "modal_id": null, "modal_type": null, "content": [{"id": "ecde100e-5e4e-4e06-d1e1-9090f8855fa0", "name": "Yes", "description": ""}, {"id": "32f0e9fa-5de9-431f-bc0e-291ca53490e4", "name": "No", "description": ""}]}}, {"id": "5724598f-233b-4db6-e036-e5c53f5cfce1", "name": "Predeceased half aunts and uncles question", "description": "Did the Deceased have any half-aunts or uncles who died before the Deceased?", "answer_type": "6", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": ["a29ed948-556d-4023-ba7c-3c6a9b9adfcd"], "answers": {"id": "0651b0c4-b7c1-4df4-eb29-c4fdf1aeeb3a", "name": "Predeceased half aunts and uncles answer", "modal_id": null, "modal_type": null, "content": [{"id": "54c377bd-a154-4af3-8d7b-6a3290e23bea", "name": "Yes", "description": ""}, {"id": "00b25691-9350-4f69-9203-bf34ee2b15f4", "name": "No", "description": ""}]}}, {"id": "0f73ffe8-02da-4ca2-b3d3-6b13fdae961f", "name": "Predeceased half aunts and uncles children question", "description": "Did any of the half-aunts or uncles who died before the Deceased have any surviving children?", "answer_type": "6", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": ["5724598f-233b-4db6-e036-e5c53f5cfce1"], "answers": {"id": "dc720e97-e1c6-47e0-cda3-2eaee86273ce", "name": "Predeceased half aunts and uncles children answer", "modal_id": null, "modal_type": null, "content": [{"id": "36780106-00c1-4fb1-c254-b98f95019f74", "name": "Yes", "description": ""}, {"id": "aefe64e9-dc4c-4a38-ed78-2a801b114171", "name": "No", "description": ""}]}}]}, {"id": "b82c941d-9cb6-4889-ac65-fc28487b3481", "name": "Property", "tooltips": "Details of properties owned or rented by the Deceased", "linkedTo": ["6205d1d1-501a-4b21-bf7b-c64fcd16335a"], "conditions": null, "questions": [{"id": "2bede201-67ad-42bc-a7ee-45da91b06245", "name": "Property details question", "description": "Please provide details of all properties owned or rented by the Deceased", "answer_type": "10", "files": [], "required": 1, "tooltips": "If there is more than one property, please click the '+' button and continue until all have been added.", "selectAnswerTable": null, "conditions": null, "linkedTo": null, "answers": {"id": "d2756307-ea35-4251-c63f-3520602309e4", "name": "Property details answer", "modal_id": 153, "modal_type": null, "content": null}}]}, {"id": "33a0ab15-32e3-4964-e771-580cd41d5abf", "name": "Bank and Building Society accounts", "tooltips": null, "linkedTo": ["d02f709f-6fb6-487f-deec-2c3d3cc87b41"], "conditions": null, "questions": [{"id": "40a56dfb-f75a-45df-d9f6-b9bc08f53dad", "name": "Bank details question", "description": "Please provide details of all bank or building society accounts.", "answer_type": "10", "files": [], "required": 1, "tooltips": "If more than one, please press the '+' button to add details of each additional account.", "selectAnswerTable": null, "conditions": null, "linkedTo": null, "answers": {"id": "367b067f-6f9a-462b-e3a5-3527ced34125", "name": "Bank details answer", "modal_id": 154, "modal_type": null, "content": null}}]}, {"id": "412bdaeb-e5c2-44d1-a6de-38ee9d582d8c", "name": "NS&I", "tooltips": "National Savings & Investments accounts", "linkedTo": ["7b87b8da-5675-4d9f-cb0e-7bc33a2efb14"], "conditions": null, "questions": [{"id": "********-e645-42ed-c47e-53c333b11d0b", "name": "NSI question", "description": "Please provide details of all National Savings & Investments Accounts", "answer_type": "10", "files": [], "required": 1, "tooltips": "If more than one, please press the '+' button to add details of each additional account.", "selectAnswerTable": null, "conditions": null, "linkedTo": null, "answers": {"id": "a15837d8-d04e-4c5a-ebde-d77ae0ce5b64", "name": "NSI answer", "modal_id": 159, "modal_type": null, "content": null}}]}, {"id": "8d4b98b9-f262-4927-a9bd-1a402e4bd6f5", "name": "Stocks and Shares", "tooltips": null, "linkedTo": ["0de03047-f0a3-47a7-b5f7-ae3a4348eb48"], "conditions": null, "questions": [{"id": "965f2302-f78b-49b6-cd6b-3b514b018faf", "name": "Stock details question", "description": "Please provide details of all stocks and shares owned by the Deceased", "answer_type": "10", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": null, "answers": {"id": "6aaf592e-3ef4-49ca-b7ac-bc60da6087e3", "name": "Stock details answer", "modal_id": 155, "modal_type": null, "content": null}}]}, {"id": "8a3f74d5-e577-47e0-82ef-52df314d2769", "name": "Investments", "tooltips": "Investments other than stocks, shares and National Savings & Investments accounts.", "linkedTo": ["50502ccc-6ea5-4814-85ed-90cd62f36c9a"], "conditions": null, "questions": [{"id": "fedf67e4-e0d6-46e1-80b8-6c713a005758", "name": "Investments details question", "description": "Please provide details of all investments held by the Deceased", "answer_type": "10", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": null, "answers": {"id": "f0075034-a7c8-44b9-e9b9-c6b9a5437be3", "name": "Investments details answer", "modal_id": 156, "modal_type": null, "content": null}}]}, {"id": "466a421d-6565-4511-d60c-f7adc7c6f98e", "name": "Pensions", "tooltips": "State and private pensions", "linkedTo": ["43a3e45d-153d-46c9-e53d-62db8f976791"], "conditions": null, "questions": [{"id": "12a57c7c-c5c4-4305-f9e9-2a65e6a1b545", "name": "State pension question", "description": "Was the Deceased drawing the state pension?", "answer_type": "6", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": null, "answers": {"id": "bbe3cf9a-39e6-4ed1-e927-9bb344ec58cb", "name": "State pension answer", "modal_id": null, "modal_type": null, "content": [{"id": "7a2cf625-8b03-4e4c-90e3-ada82ed9470a", "name": "Yes", "description": ""}, {"id": "126c7231-f679-4646-ee20-d0cedba979d4", "name": "No", "description": ""}]}}, {"id": "71399252-9f1a-4ddd-de43-91da2cf5e927", "name": "Private pensions question", "description": "Did the Deceased have any private pensions?", "answer_type": "6", "files": [], "required": 1, "tooltips": "Whether or not the pension was being drawn.", "selectAnswerTable": null, "conditions": null, "linkedTo": null, "answers": {"id": "798910eb-7334-455a-b174-e4a94f43e9cd", "name": "Private pensions answer", "modal_id": null, "modal_type": null, "content": [{"id": "969417d0-d850-4742-ecf9-56942e52e1d0", "name": "Yes", "description": ""}, {"id": "8364d104-ef3d-4383-e7b3-34b8288bda3c", "name": "No", "description": ""}]}}, {"id": "509e051e-24f2-4c07-af33-4676fb0b48ec", "name": "Private pensions details question", "description": "Please provide details of all private pensions", "answer_type": "10", "files": [], "required": 1, "tooltips": "If more than one, please press the '+' button to add details of each additional pension.", "selectAnswerTable": null, "conditions": null, "linkedTo": ["71399252-9f1a-4ddd-de43-91da2cf5e927"], "answers": {"id": "0666e82f-e18e-46d3-cb3a-c488b0ed7489", "name": "Private pensions details answer", "modal_id": 157, "modal_type": null, "content": null}}]}, {"id": "e62205a9-67ba-48a4-f21d-08d4e0c62a4d", "name": "Life policies", "tooltips": "Life insurance policies", "linkedTo": null, "conditions": null, "questions": [{"id": "16c46d90-2d12-4345-b00f-f1e6296d7ad4", "name": "Life policies details question", "description": "Please provide details of all life policies held by the Deceased", "answer_type": "10", "files": [], "required": 1, "tooltips": "If more than one, please press the '+' button to add details of each additional policy.", "selectAnswerTable": null, "conditions": null, "linkedTo": ["369e7172-fbd1-4d09-b57b-29f182b3e96a"], "answers": {"id": "d752b826-b685-4667-dbd0-45452e8aa167", "name": "Life policies details answer", "modal_id": 158, "modal_type": null, "content": null}}]}, {"id": "4f95bd6c-36df-4d47-a4cd-d484df9c3a1e", "name": "Other assets", "tooltips": "Other assets held by the Deceased", "linkedTo": ["a81d864a-f9d2-4dbc-e694-ac78b7724319"], "conditions": null, "questions": [{"id": "7ccdebe2-b1a9-487d-8685-b48b95cc7ffe", "name": "GROB question", "description": "Did the Deceased transfer any assets but continue to benefit from them?", "answer_type": "6", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": null, "answers": {"id": "b6a87fae-f6df-4e96-f3b7-c163697e685a", "name": "GROB answer", "modal_id": null, "modal_type": null, "content": [{"id": "37d227c5-f12a-492f-cd43-a42198996d54", "name": "Yes", "description": ""}, {"id": "7a97a344-5cda-4a4d-a95d-c37312040cac", "name": "No", "description": ""}]}}, {"id": "83347d3c-15a6-4a95-8a89-1f005da10088", "name": "GROB details question", "description": "Please provide details of assets transferred by the Deceased, from which the Deceased continued to benefit", "answer_type": "1", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": ["7ccdebe2-b1a9-487d-8685-b48b95cc7ffe"], "answers": {"id": "b7ee8503-49fc-4488-aed2-c3fa3acc4323", "name": "GROB details answer", "modal_id": null, "modal_type": null, "content": null}}, {"id": "4f3cbed1-9db4-421e-9615-57782cf12dc6", "name": "Trusts question", "description": "Was the Deceased benefiting from assets held in trust immediately prior to death?", "answer_type": "6", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": null, "answers": {"id": "ea3ecd54-340b-45c0-90b4-9e30d24eab06", "name": "Trusts answer", "modal_id": null, "modal_type": null, "content": [{"id": "ef1c526e-bb73-48f6-925f-da2c090c2e4b", "name": "Yes", "description": ""}, {"id": "e38ea0df-d388-4691-edce-360cd34e4f58", "name": "No", "description": ""}]}}, {"id": "e70aa457-764e-46f6-bd2b-e7078540f110", "name": "Trusts details question", "description": "Please provide details of the Deceased's interests in trusts", "answer_type": "1", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": ["4f3cbed1-9db4-421e-9615-57782cf12dc6"], "answers": {"id": "20bc14da-d336-4454-9f5a-5e5b42184425", "name": "Trusts details answer", "modal_id": null, "modal_type": null, "content": null}}, {"id": "fa72b2e8-071d-4fb3-a11c-bd0e77051bfd", "name": "Foreign assets question", "description": "Did the Deceased hold any foreign assets outside of England and Wales?", "answer_type": "6", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": null, "answers": {"id": "83a1fadd-a6cc-4335-d31a-e31586ac598f", "name": "Foreign assets answer", "modal_id": null, "modal_type": null, "content": [{"id": "1250e5f7-1dd2-4651-f3de-e88ed57d35f0", "name": "Yes", "description": ""}, {"id": "fbcdc0b6-0a0f-4a9f-98b2-bafffeb35ad7", "name": "No", "description": ""}]}}, {"id": "60e45617-3003-4cb0-b77f-48575cd08104", "name": "Foreign assets value question", "description": "Is the value of the Deceased's foreign assets in excess of £100,000?", "answer_type": "6", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": ["fa72b2e8-071d-4fb3-a11c-bd0e77051bfd"], "answers": {"id": "5d83befc-04f7-4549-9c20-8746e8f83555", "name": "Foreign assets value answer", "modal_id": null, "modal_type": null, "content": [{"id": "40015a39-50bf-47c6-8464-1fc7ba59396a", "name": "Yes", "description": ""}, {"id": "cd7bed20-6f09-4748-de07-b36be65442fb", "name": "No", "description": ""}]}}, {"id": "527072d2-436d-4f9b-b3ff-375f7c5b0deb", "name": "Business assets question", "description": "Did the Deceased own or have an interest in a business or farm?", "answer_type": "6", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": null, "answers": {"id": "1fdef83f-14c3-46e6-a9c8-dc997aeccff0", "name": "Business assets answer", "modal_id": null, "modal_type": null, "content": [{"id": "52d6ab51-c2fd-43d6-f50d-e088e2f1696c", "name": "Yes", "description": ""}, {"id": "ac4e9672-b0db-4f60-8e3b-b0ea13dd861b", "name": "No", "description": ""}]}}, {"id": "2b3b151b-6664-444d-ed06-edf2e8084b5a", "name": "Business assets details question", "description": "Please provide details of the Deceased's interest in a business or farm", "answer_type": "1", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": ["527072d2-436d-4f9b-b3ff-375f7c5b0deb"], "answers": {"id": "75d8a889-22f5-414c-e242-16a4694da78a", "name": "Business assets details answer", "modal_id": null, "modal_type": null, "content": null}}, {"id": "df66f7fe-d1a4-47fd-9087-a1c9cd73216a", "name": "Life premiums question", "description": "Did the Deceased pay premiums on a life insurance policy that was not for their benefit or the benefit of their estate?", "answer_type": "6", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": null, "answers": {"id": "c8513972-12d9-43e8-dee7-52a36b1f55ff", "name": "Life premiums answer", "modal_id": null, "modal_type": null, "content": [{"id": "4272a661-bd44-4acf-96a0-dd739e99ff9b", "name": "Yes", "description": ""}, {"id": "15827251-183a-4f80-c08a-f42d5ca17452", "name": "No", "description": ""}]}}, {"id": "9b675864-5fdf-41d9-aeba-838ea1cd4e31", "name": "Life premiums details question", "description": "Please provide details of life insurance premiums paid by the Deceased for policies that did not benefit the Deceased or their estate", "answer_type": "1", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": ["df66f7fe-d1a4-47fd-9087-a1c9cd73216a"], "answers": {"id": "bc47a61f-f954-4ea3-c416-3893bddb9197", "name": "Life premiums details answer", "modal_id": null, "modal_type": null, "content": null}}, {"id": "496a57e3-914a-4442-fb1e-aa9a97faf8cc", "name": "Pension disposal question", "description": "Did the Deceased change the distribution or dispose of a pension in the two years prior to death?", "answer_type": "6", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": null, "answers": {"id": "e78b6f5d-6165-4677-e6ac-9fa29cf475c1", "name": "Pension disposal answer", "modal_id": null, "modal_type": null, "content": [{"id": "00f03c8e-a691-484c-d237-b98a59fffa8c", "name": "Yes", "description": ""}, {"id": "3553ea9d-987e-46ba-b853-d3b8142cc202", "name": "No", "description": ""}]}}, {"id": "adf16c16-eee7-4c11-ca3a-9ca22353abb1", "name": "Pension disposal details question", "description": "Please provide details of pensions disposals or changes made by the Deceased", "answer_type": "1", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": ["496a57e3-914a-4442-fb1e-aa9a97faf8cc"], "answers": {"id": "bb7e8916-3dae-4bea-c019-cca8426d57a6", "name": "Pension disposal details answer", "modal_id": null, "modal_type": null, "content": null}}, {"id": "a9ef52cf-ff6d-408f-d6d8-170899a83011", "name": "Any other assets question", "description": "Did the Deceased have any other assets?", "answer_type": "6", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": null, "answers": {"id": "d0ab7545-a62e-4485-8d29-6c7a129f0876", "name": "Any other assets answer", "modal_id": null, "modal_type": null, "content": [{"id": "bb03acdb-9cbf-4f4c-9ef9-dd7af3e768a1", "name": "Yes", "description": ""}, {"id": "36a858d8-ae61-40f8-d67c-5b599f4b1700", "name": "No", "description": ""}]}}, {"id": "0d0175e0-dc78-40a6-9a85-709fe39906d7", "name": "Other assets details question", "description": "Please provide details of all other assets held by the Deceased", "answer_type": "10", "files": [], "required": 1, "tooltips": "If more than one, please press the '+' button to add details of each additional asset.", "selectAnswerTable": null, "conditions": null, "linkedTo": ["a9ef52cf-ff6d-408f-d6d8-170899a83011"], "answers": {"id": "b4fa5f7d-2b88-4888-92a0-775369002e77", "name": "Other assets details answer", "modal_id": 160, "modal_type": null, "content": null}}]}, {"id": "2460013b-3309-4f4f-93ad-8992e25cff0d", "name": "Gifts", "tooltips": "Gifts made by the Deceased in the last seven years prior to death.", "linkedTo": null, "conditions": null, "questions": [{"id": "c26ef932-80a6-4c9f-e633-cca0d65f8122", "name": "Gifts details question", "description": "Please provide details of all gifts made by the Deceased in the last seven years prior to death, where the gift exceeded £3,000 in any given year", "answer_type": "2", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": ["47265d3c-67ce-4de0-de55-07857e67432a"], "answers": {"id": "78624f61-043f-4519-d2c3-974ee4539c7c", "name": "Gifts details answer", "modal_id": null, "modal_type": null, "content": null}}]}, {"id": "8be7b3ad-b9f0-4f58-9ce8-08c3cd9030f4", "name": "Funeral", "tooltips": "Funeral expenses", "linkedTo": null, "conditions": null, "questions": [{"id": "e46b5feb-9e73-4634-d419-f84f57ba0980", "name": "Funeral question", "description": "Please state the actual or estimated cost of the funeral", "answer_type": "5", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": null, "answers": {"id": "8c2561ea-fe12-4817-8599-16fab95f27d3", "name": "Funeral answer", "modal_id": null, "modal_type": null, "content": null}}, {"id": "9d8c6463-e8e8-477b-ec44-8d06ea737aef", "name": "Other expenses question", "description": "Have any other expenses been incurred?", "answer_type": "6", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": null, "answers": {"id": "aaef3270-072e-4413-bb3e-2f104b29f375", "name": "Other expenses answer", "modal_id": null, "modal_type": null, "content": [{"id": "0f9d8a1b-e6e6-48e5-d579-e066888a5427", "name": "Yes", "description": ""}, {"id": "18f31a46-cc05-4484-dbdd-372af17483bd", "name": "No", "description": ""}]}}, {"id": "bb6504e3-a2b0-4879-9f35-8a7908ce37b3", "name": "Other expenses details question", "description": "Please state the total of all other expenses", "answer_type": "5", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": ["9d8c6463-e8e8-477b-ec44-8d06ea737aef"], "answers": {"id": "51a78566-3edc-4d40-ca22-ac5cc51b5e7b", "name": "Other expenses details answer", "modal_id": null, "modal_type": null, "content": null}}]}, {"id": "0e09477d-be5a-4b84-d39f-cc8d9a2a15a8", "name": "Personal loans", "tooltips": "Personal loans owed by the Deceased", "linkedTo": ["0ab92110-8fd6-4312-8c77-c2d3231ceed6"], "conditions": null, "questions": [{"id": "e53d64fb-9c78-4309-f76f-e17b102fad74", "name": "Personal loans details question", "description": "Please provide details of all personal loans owed by the Deceased", "answer_type": "10", "files": [], "required": 1, "tooltips": null, "selectAnswerTable": null, "conditions": null, "linkedTo": null, "answers": {"id": "53b247cd-884a-4c22-f39f-42ee4d6bf40c", "name": "Personal loans details answer", "modal_id": 161, "modal_type": null, "content": null}}]}, {"id": "29f46803-1b4b-43b8-b593-87619e052553", "name": "Credit and store cards", "tooltips": null, "linkedTo": ["d1cccc73-2835-4ad9-b8b4-8a611c168d4e"], "conditions": null, "questions": [{"id": "6a0d8fb8-58b8-40c5-b619-a14d542ffee4", "name": "Credit card details question", "description": "Please provide details of all credit or store cards held by the Deceased", "answer_type": "10", "files": [], "required": 1, "tooltips": "If more than one, please press the '+' button to add details of each additional credit or store card.", "selectAnswerTable": null, "conditions": null, "linkedTo": null, "answers": {"id": "72b81e68-94dc-43f1-ab39-5c0f705c65d4", "name": "Credit card details answer", "modal_id": 162, "modal_type": null, "content": null}}]}, {"id": "7ec1f2ae-1562-466e-e00c-755675a93b18", "name": "Other liabilities", "tooltips": "Liabilities of the Deceased, other than mortgages, personal loans, credit and store cards.", "linkedTo": ["1baab982-2995-4618-b029-4361f1a225c8"], "conditions": null, "questions": [{"id": "41a7af75-83ac-4183-90ce-2bf5a137d7a4", "name": "Other liabilities question", "description": "Please provide details of all other liabilities of the Deceased, apart from mortgages, personal loans, credit or store cards.", "answer_type": "10", "files": [], "required": 1, "tooltips": "Do not include funeral or associated expenses.", "selectAnswerTable": null, "conditions": null, "linkedTo": null, "answers": {"id": "4eb42ad2-e760-4e9a-be29-dbac3c3ab44a", "name": "Other liabilities answer", "modal_id": 163, "modal_type": null, "content": null}}]}], "conditions": [{"list": [{"id": "f6f076e1-7076-4b48-b873-1d6f9937af7b", "type": "group", "children": [{"id": "c8c0191d-f16c-4f18-d6fd-ceba35a45df5", "questionId": "db90cf66-e34f-4562-e3ad-c45878e41740", "type": "question", "operator": "1", "value": "b35362d1-1fed-4411-a196-fa71e9510e53"}]}], "linkTo": "006a6661-b331-49cd-bde0-afae844b2add"}, {"list": [{"id": "9e39ee59-6c4b-4995-8d5e-5dd7cb44bbed", "questionId": "62ef780b-71ec-4579-c47c-2383952980e2", "type": "question", "operator": "1", "value": "ce6c7080-76da-4f8f-896a-264b6b78abd0"}], "linkTo": "259f714e-dfc3-4785-ec4d-111455641c4e"}, {"list": [{"id": "d58b2a40-563c-4d20-bcc9-c6d961a3cf5b", "questionId": "e18c31db-4ccf-42f1-e8bb-2b4f9331ff32", "type": "question", "operator": "1", "value": "bc9cbc74-5886-4c43-ea76-f52e12f1bdfa"}], "linkTo": "3466e88a-a26c-490e-dd6f-dee26ed5cc0a"}, {"list": [{"id": "272ab365-f86d-4d22-b515-2388e945531a", "questionId": "e18c31db-4ccf-42f1-e8bb-2b4f9331ff32", "type": "question", "operator": "1", "value": "0a0a49b3-b544-4fdc-d245-c14777ec156a"}], "linkTo": "5130c199-bedb-4438-964e-40c81f2a5d1d"}, {"list": [{"id": "b98e79b0-3026-4fd4-9e3a-94e7cf39daca", "type": "group", "children": [{"id": "c53802dd-53f3-446c-ac21-ef45620c44a7", "questionId": "db90cf66-e34f-4562-e3ad-c45878e41740", "type": "question", "operator": "1", "value": "02701a94-e7cb-4582-bacd-9d07aa11e6d5"}]}], "linkTo": "2dcff60a-f7c8-477e-8a68-0babcfaa630b"}, {"list": [{"id": "88539386-cf22-4bfd-db9b-5c0d36e431b1", "questionId": "33410276-f957-48da-8966-d354108f810a", "type": "question", "operator": "1", "value": "2090dd44-d8f6-456d-a558-68a250024043"}], "linkTo": "ef9cdd42-0b27-4dff-f532-ff101c9ee54e"}, {"list": [{"id": "505acb4e-c5c3-4ebb-c596-170fa64718a9", "questionId": "ef9cdd42-0b27-4dff-f532-ff101c9ee54e", "type": "question", "operator": "1", "value": "0159a9ce-a8f1-4c34-ae82-860909f871de"}], "linkTo": "e6c8a9c3-4dbf-4bc1-ead0-8958574be5ce"}, {"list": [{"id": "a205302c-4e98-4de4-b306-6ecc98ed5862", "questionId": "e6c8a9c3-4dbf-4bc1-ead0-8958574be5ce", "type": "question", "operator": "1", "value": "2eabc0a9-4d3c-47b1-ec26-f949d02d5804"}], "linkTo": "2827e742-c036-4fe6-9ab7-4a7d824c3a44"}, {"list": [{"id": "43d41916-6e28-4e47-977d-e928d4945ecd", "questionId": "2827e742-c036-4fe6-9ab7-4a7d824c3a44", "type": "question", "operator": "1", "value": "32ff8079-3abb-4766-d57a-f360f880afa0"}], "linkTo": "ed6a37cf-4163-49b0-edb0-a1daa8414891"}, {"list": [{"id": "7cb1d1b4-1579-4984-fbeb-89335f3f0b34", "questionId": "ed6a37cf-4163-49b0-edb0-a1daa8414891", "type": "question", "operator": "1", "value": "ddceddc1-77c4-41c4-d85b-3e3f2f99b890"}], "linkTo": "1ea853a4-3695-4d30-85f4-aaafa2864cfa"}, {"list": [{"id": "394789ef-b6a2-4c23-88a1-eda09bfc8808", "type": "group", "children": [{"id": "d2831ed4-d502-4527-a6e9-6551fa9236e4", "questionId": "db90cf66-e34f-4562-e3ad-c45878e41740", "type": "question", "operator": "1", "value": "02701a94-e7cb-4582-bacd-9d07aa11e6d5"}, {"id": "7a1f6926-e450-48bd-b3d0-01fb855a120a", "value": "8", "type": "operator", "connect": "d2831ed4-d502-4527-a6e9-6551fa9236e4-connect-a634b936-01e9-449d-9f9c-527c3eb7daa3"}, {"id": "a634b936-01e9-449d-9f9c-527c3eb7daa3", "type": "group", "children": [{"id": "72a8a160-688c-4bf9-f006-0cc5c667e5a5", "questionId": "3466e88a-a26c-490e-dd6f-dee26ed5cc0a", "type": "question", "operator": "1", "value": "ad52d453-d060-4cec-d4ed-1f804cd21f75"}]}]}], "linkTo": "ffbf0e02-60ca-48f3-c1f9-6dbda2756994"}, {"list": [{"id": "e1414fa4-1327-4c99-9686-7b5c4d2f4b17", "type": "group", "children": [{"id": "cc4b6388-0321-445d-ee9c-e79d79c64466", "questionId": "db90cf66-e34f-4562-e3ad-c45878e41740", "type": "question", "operator": "1", "value": "02701a94-e7cb-4582-bacd-9d07aa11e6d5"}, {"id": "bdb4f580-b54c-484f-e061-4bc56c5e8948", "value": "8", "type": "operator", "connect": "cc4b6388-0321-445d-ee9c-e79d79c64466-connect-47986cbe-5da0-4aa9-ce67-2028872d4d15"}, {"id": "47986cbe-5da0-4aa9-ce67-2028872d4d15", "type": "group", "children": [{"id": "f5a33888-0614-4bf1-d6c4-254be8d508f3", "questionId": "5130c199-bedb-4438-964e-40c81f2a5d1d", "type": "question", "operator": "1", "value": "910ae66e-12e1-4c43-cdbf-d23a4e6bff95"}]}]}], "linkTo": "ea92de8a-391e-452a-8319-a030af00206b"}, {"list": [{"id": "4b7b5c7d-993b-481f-e837-d750bbeefc39", "questionId": "1ea853a4-3695-4d30-85f4-aaafa2864cfa", "type": "question", "operator": "1", "value": "dda64e11-69b4-4587-a69a-974910b5d7b1"}], "linkTo": "a29ed948-556d-4023-ba7c-3c6a9b9adfcd"}, {"list": [{"id": "91c47a99-70ce-426b-d91c-3aa35b80c0eb", "type": "group", "children": [{"id": "b4dd91a1-ff0a-48d6-fd2e-67ccc31e6a64", "questionId": "6205d1d1-501a-4b21-bf7b-c64fcd16335a", "type": "question", "operator": "1", "value": "8764f419-bd90-4a2d-b6a6-2d0374c2ce21"}]}], "linkTo": "b82c941d-9cb6-4889-ac65-fc28487b3481"}, {"list": [{"id": "33bc050e-8d78-42ac-b010-edc982935aa3", "type": "group", "children": [{"id": "d13167d1-4270-4555-da7f-f562949d1874", "questionId": "d02f709f-6fb6-487f-deec-2c3d3cc87b41", "type": "question", "operator": "1", "value": "4c4bb0c5-7642-413a-a58d-143ba1effc44"}]}], "linkTo": "33a0ab15-32e3-4964-e771-580cd41d5abf"}, {"list": [{"id": "d1d56901-b4c7-417c-89d1-f990cfa31e57", "type": "group", "children": [{"id": "dd22b66d-6ba1-454e-f92e-49321e512985", "questionId": "7b87b8da-5675-4d9f-cb0e-7bc33a2efb14", "type": "question", "operator": "1", "value": "44fa76cb-5981-4ad2-99fe-76d7ca6382a0"}]}], "linkTo": "412bdaeb-e5c2-44d1-a6de-38ee9d582d8c"}, {"list": [{"id": "cad152a5-1d04-4509-fbcb-56c7fc9f91e2", "type": "group", "children": [{"id": "d45fc4cd-27f7-4b27-e382-db597c49dce8", "questionId": "0de03047-f0a3-47a7-b5f7-ae3a4348eb48", "type": "question", "operator": "1", "value": "a690b3ef-6854-4cb6-c6a4-ad8ba7648948"}]}], "linkTo": "8d4b98b9-f262-4927-a9bd-1a402e4bd6f5"}, {"list": [{"id": "b2069b0d-1662-48b5-aea0-25ada3d85f47", "type": "group", "children": [{"id": "7427a0c9-ceb8-421b-e6f3-7c9a326ea17c", "questionId": "50502ccc-6ea5-4814-85ed-90cd62f36c9a", "type": "question", "operator": "1", "value": "3bc851ce-1cac-4f6a-adb1-fb9498346097"}]}], "linkTo": "8a3f74d5-e577-47e0-82ef-52df314d2769"}, {"list": [{"id": "33436775-794a-4302-ceb6-72779dc99aa0", "type": "group", "children": [{"id": "0225eb32-70b6-4c7a-ff9b-fd18de14ba8d", "questionId": "43a3e45d-153d-46c9-e53d-62db8f976791", "type": "question", "operator": "1", "value": "076f8e80-99a5-436a-e63d-11f477ba1bc0"}]}], "linkTo": "466a421d-6565-4511-d60c-f7adc7c6f98e"}, {"list": [{"id": "86b36d75-49b2-4861-960d-2c3791158a40", "questionId": "71399252-9f1a-4ddd-de43-91da2cf5e927", "type": "question", "operator": "1", "value": "969417d0-d850-4742-ecf9-56942e52e1d0"}], "linkTo": "509e051e-24f2-4c07-af33-4676fb0b48ec"}, {"list": [{"id": "5191d33f-258b-45ce-febd-879a383b5684", "type": "group", "children": [{"id": "cbe810c7-b32e-49d6-b8d5-38c32e3b3f57", "questionId": "369e7172-fbd1-4d09-b57b-29f182b3e96a", "type": "question", "operator": "1", "value": "404109c2-03de-487c-a60f-b883ba9dacb6"}]}], "linkTo": "16c46d90-2d12-4345-b00f-f1e6296d7ad4"}, {"list": [{"id": "9462ad1a-fe3d-4cf6-cbdd-f201f53a76e6", "type": "group", "children": [{"id": "b3a4f08d-fde5-40e5-b083-6eb926455c90", "questionId": "47265d3c-67ce-4de0-de55-07857e67432a", "type": "question", "operator": "1", "value": "2a7cfc0f-2aa9-430b-fbb2-eb09f78d2967"}]}], "linkTo": "c26ef932-80a6-4c9f-e633-cca0d65f8122"}, {"list": [{"id": "43c0a463-b0a6-4903-9811-1f330af0196a", "questionId": "9d8c6463-e8e8-477b-ec44-8d06ea737aef", "type": "question", "operator": "1", "value": "0f9d8a1b-e6e6-48e5-d579-e066888a5427"}], "linkTo": "bb6504e3-a2b0-4879-9f35-8a7908ce37b3"}, {"list": [{"id": "237385d0-1962-4039-8a8d-12c419aac8fc", "type": "group", "children": [{"id": "3b0045e6-c1ae-47e9-a5fb-388684b54300", "questionId": "0ab92110-8fd6-4312-8c77-c2d3231ceed6", "type": "question", "operator": "1", "value": "ad3d19de-b849-4514-e285-55845661a8bb"}]}], "linkTo": "0e09477d-be5a-4b84-d39f-cc8d9a2a15a8"}, {"list": [{"id": "76543c31-e5b1-4547-d29b-f5805bacd265", "type": "group", "children": [{"id": "f948eb44-5747-458c-8184-bbbd099589a9", "questionId": "d1cccc73-2835-4ad9-b8b4-8a611c168d4e", "type": "question", "operator": "1", "value": "30ad1c4e-6274-4483-827c-cf3da6501d1a"}]}], "linkTo": "29f46803-1b4b-43b8-b593-87619e052553"}, {"list": [{"id": "cc1455bd-0f08-43b8-abcc-0056c1ea84f9", "type": "group", "children": [{"id": "3333675f-644d-4f29-cfe6-ad88b096581f", "questionId": "1baab982-2995-4618-b029-4361f1a225c8", "type": "question", "operator": "1", "value": "31325550-f3bd-4a4e-f2f0-733a71a38517"}]}], "linkTo": "7ec1f2ae-1562-466e-e00c-755675a93b18"}, {"list": [{"id": "703fe52e-c958-49ba-c002-7208ce22a03c", "type": "group", "children": [{"id": "1c1db8ee-b58c-4ddf-ed37-ecd40306d085", "questionId": "a81d864a-f9d2-4dbc-e694-ac78b7724319", "type": "question", "operator": "1", "value": "ff1b9d17-a3dc-4d16-91b7-2aa038a705f2"}]}], "linkTo": "4f95bd6c-36df-4d47-a4cd-d484df9c3a1e"}, {"list": [{"id": "f718aae9-735f-4441-b358-c7864b3d3d14", "questionId": "7ccdebe2-b1a9-487d-8685-b48b95cc7ffe", "type": "question", "operator": "1", "value": "37d227c5-f12a-492f-cd43-a42198996d54"}], "linkTo": "83347d3c-15a6-4a95-8a89-1f005da10088"}, {"list": [{"id": "5e89b986-9d19-4369-9655-ebce355a73e3", "questionId": "4f3cbed1-9db4-421e-9615-57782cf12dc6", "type": "question", "operator": "1", "value": "ef1c526e-bb73-48f6-925f-da2c090c2e4b"}], "linkTo": "e70aa457-764e-46f6-bd2b-e7078540f110"}, {"list": [{"id": "3a3ec789-6518-4229-cecd-20a199202c37", "questionId": "fa72b2e8-071d-4fb3-a11c-bd0e77051bfd", "type": "question", "operator": "1", "value": "1250e5f7-1dd2-4651-f3de-e88ed57d35f0"}], "linkTo": "60e45617-3003-4cb0-b77f-48575cd08104"}, {"list": [{"id": "1d971b9d-af3c-47b3-cb61-153db372fced", "questionId": "527072d2-436d-4f9b-b3ff-375f7c5b0deb", "type": "question", "operator": "1", "value": "52d6ab51-c2fd-43d6-f50d-e088e2f1696c"}], "linkTo": "2b3b151b-6664-444d-ed06-edf2e8084b5a"}, {"list": [{"id": "d57df1bc-c14f-4cb7-96fb-e646e3adbdba", "questionId": "df66f7fe-d1a4-47fd-9087-a1c9cd73216a", "type": "question", "operator": "1", "value": "4272a661-bd44-4acf-96a0-dd739e99ff9b"}], "linkTo": "9b675864-5fdf-41d9-aeba-838ea1cd4e31"}, {"list": [{"id": "74fcff4b-880a-4b5b-dbec-f2dc7d5689fa", "questionId": "496a57e3-914a-4442-fb1e-aa9a97faf8cc", "type": "question", "operator": "1", "value": "00f03c8e-a691-484c-d237-b98a59fffa8c"}], "linkTo": "adf16c16-eee7-4c11-ca3a-9ca22353abb1"}, {"list": [{"id": "cbe25ce7-519f-46d7-b7f3-9bf81e41c8ce", "questionId": "c5ddcb8d-2493-4b25-f642-f84a414c9d95", "type": "question", "operator": "1", "value": "c6c25cfe-0f2f-49c4-dba7-090244527b94"}], "linkTo": "0a6d6935-a1e7-4cde-bbd7-0457831dffd4"}, {"list": [{"id": "7cf65a16-5df6-4513-a328-b05a49bd9578", "questionId": "c5ddcb8d-2493-4b25-f642-f84a414c9d95", "type": "question", "operator": "1", "value": "fdf26a7c-1ccf-4c19-f8cd-3d37aa885fb0"}, {"id": "4b42a56b-642d-4707-fc0d-a381694906bb", "value": "8", "type": "operator", "connect": "7cf65a16-5df6-4513-a328-b05a49bd9578-connect-3054d0f2-c049-4ff0-d0ce-0535630ef664"}, {"id": "3054d0f2-c049-4ff0-d0ce-0535630ef664", "questionId": "0a6d6935-a1e7-4cde-bbd7-0457831dffd4", "type": "question", "operator": "1", "value": "06f77924-4062-4ffd-facf-8271495d6b37"}], "linkTo": "9ff6ac3d-a0e8-46cd-f7ba-39e9cf11f4e3"}, {"list": [{"id": "a95bb1a3-3132-4100-c122-8dc97b56d505", "questionId": "0a6d6935-a1e7-4cde-bbd7-0457831dffd4", "type": "question", "operator": "1", "value": "6f5782ca-38d0-4456-a7c6-54e1311c4efc"}, {"id": "8baebfcc-5c8f-469b-e89f-1dcb5164be53", "value": "8", "type": "operator", "connect": "a95bb1a3-3132-4100-c122-8dc97b56d505-connect-d5ee3944-a42a-43d8-aad2-12951d3af279"}, {"id": "d5ee3944-a42a-43d8-aad2-12951d3af279", "questionId": "c5ddcb8d-2493-4b25-f642-f84a414c9d95", "type": "question", "operator": "1", "value": "c6c25cfe-0f2f-49c4-dba7-090244527b94"}], "linkTo": "9ff6ac3d-a0e8-46cd-f7ba-39e9cf11f4e3"}, {"list": [{"id": "80609150-2e42-4759-ed72-fa611852446a", "questionId": "a9ef52cf-ff6d-408f-d6d8-170899a83011", "type": "question", "operator": "1", "value": "bb03acdb-9cbf-4f4c-9ef9-dd7af3e768a1"}], "linkTo": "0d0175e0-dc78-40a6-9a85-709fe39906d7"}, {"list": [{"id": "db37847f-a4d0-4f00-906d-c60fe142de20", "questionId": "c5ddcb8d-2493-4b25-f642-f84a414c9d95", "type": "question", "operator": "1", "value": "fdf26a7c-1ccf-4c19-f8cd-3d37aa885fb0"}, {"id": "8df0b41a-affa-479d-c583-4fc203854026", "value": "8", "type": "operator", "connect": "db37847f-a4d0-4f00-906d-c60fe142de20-connect-b8a3a0c0-bf8d-4104-9e4e-76aa950ffde6"}, {"id": "b8a3a0c0-bf8d-4104-9e4e-76aa950ffde6", "questionId": "0a6d6935-a1e7-4cde-bbd7-0457831dffd4", "type": "question", "operator": "1", "value": "6f5782ca-38d0-4456-a7c6-54e1311c4efc"}], "linkTo": "9ff6ac3d-a0e8-46cd-f7ba-39e9cf11f4e3"}, {"list": [{"id": "f301f257-327f-44a1-f39a-419456a40445", "questionId": "5599b945-88a4-4189-ea93-13215b54a574", "type": "question", "operator": "1", "value": "ee2ec44e-66ce-4425-b902-e042430da433"}], "linkTo": "aa8727da-1a37-4982-c057-bb368e347cbf"}, {"list": [{"id": "0f9c8943-d844-480f-a940-1c67add38676", "questionId": "c5ddcb8d-2493-4b25-f642-f84a414c9d95", "type": "question", "operator": "1", "value": "fdf26a7c-1ccf-4c19-f8cd-3d37aa885fb0"}], "linkTo": "9ff6ac3d-a0e8-46cd-f7ba-39e9cf11f4e3"}, {"list": [{"id": "b9249796-1c5f-4833-e0d3-aa9e732044e9", "questionId": "0a6d6935-a1e7-4cde-bbd7-0457831dffd4", "type": "question", "operator": "1", "value": "6f5782ca-38d0-4456-a7c6-54e1311c4efc"}], "linkTo": "9ff6ac3d-a0e8-46cd-f7ba-39e9cf11f4e3"}, {"list": [{"id": "bd163c63-ed90-4ce0-e83e-2e9a6a8bbc54", "questionId": "e6c8a9c3-4dbf-4bc1-ead0-8958574be5ce", "type": "question", "operator": "1", "value": "1d663e92-246c-4959-94e7-326af2bff9dc"}], "linkTo": "1ccec419-7882-4f16-ef75-0caef1f5abcf"}, {"list": [{"id": "ba0f9b9b-e61b-44dd-c627-139ccf9e454f", "questionId": "1ccec419-7882-4f16-ef75-0caef1f5abcf", "type": "question", "operator": "1", "value": "9b354e44-a53f-4f8e-80c5-34108e28f77d"}], "linkTo": "3872ad4b-76c6-4e68-8a54-71db4e4801a7"}, {"list": [{"id": "e232186c-4a3c-4914-cb78-eaad06047549", "questionId": "33410276-f957-48da-8966-d354108f810a", "type": "question", "operator": "1", "value": "9263d667-1c94-4937-e4ed-ed50c2321f5e"}], "linkTo": "5599b945-88a4-4189-ea93-13215b54a574"}, {"list": [{"id": "01bf994c-1da0-46ca-aff5-26cad43eec8c", "questionId": "db90cf66-e34f-4562-e3ad-c45878e41740", "type": "question", "operator": "1", "value": "b35362d1-1fed-4411-a196-fa71e9510e53"}], "linkTo": "38893b82-be84-4a07-b240-f43529abcdf6"}, {"list": [{"id": "baf952a2-25d1-474a-c338-e728c1b1e0da", "questionId": "2827e742-c036-4fe6-9ab7-4a7d824c3a44", "type": "question", "operator": "1", "value": "3857f84f-384b-4924-97ae-6d665f5d0a00"}], "linkTo": "43f2f2d1-b60d-4701-f00e-314a0d190a34"}, {"list": [{"id": "cb861a78-c9e9-4be5-e6ce-48ec43f5f72b", "questionId": "43f2f2d1-b60d-4701-f00e-314a0d190a34", "type": "question", "operator": "1", "value": "d22f0fdf-f3d2-49cf-e1b7-faab3be9864c"}], "linkTo": "f91321b9-c879-4a30-be31-df77077f4979"}, {"list": [{"id": "6bd802ba-d25c-4b92-8420-763fe9368fb7", "questionId": "1ea853a4-3695-4d30-85f4-aaafa2864cfa", "type": "question", "operator": "1", "value": "ad75bc65-ec41-42e4-a239-8382ef7b0095"}], "linkTo": "80e32b9c-9981-48d7-f6fa-dac553192f74"}, {"list": [{"id": "a22fac80-4da1-4994-e678-d8d85882855b", "questionId": "80e32b9c-9981-48d7-f6fa-dac553192f74", "type": "question", "operator": "1", "value": "cfc30ef2-caac-41dd-fea0-13f8e342d5dc"}], "linkTo": "5e2c0208-7141-4f52-85f1-6fa61c060cbc"}, {"list": [{"id": "30547456-b48c-41b6-ba3d-5b894874d471", "questionId": "a29ed948-556d-4023-ba7c-3c6a9b9adfcd", "type": "question", "operator": "1", "value": "ecde100e-5e4e-4e06-d1e1-9090f8855fa0"}], "linkTo": "5724598f-233b-4db6-e036-e5c53f5cfce1"}, {"list": [{"id": "411b1317-a138-4285-e1a6-abbdf74a639c", "questionId": "5724598f-233b-4db6-e036-e5c53f5cfce1", "type": "question", "operator": "1", "value": "54c377bd-a154-4af3-8d7b-6a3290e23bea"}], "linkTo": "0f73ffe8-02da-4ca2-b3d3-6b13fdae961f"}], "answer": [{"id": 2390, "ques_id": "c61073ea-7d15-4509-c965-ce516227a66d", "answer": ["53207dd4-e5bb-4f6b-a864-aff12c100e55"], "case_id": "2c7ff974-6be4-4bd3-b99b-c51b9b6720c9", "parse": 1}, {"id": 2391, "ques_id": "828ea38c-c956-40de-af39-b0cb794793fd", "answer": {"f2e18ff1-5eb0-4f7e-817b-fee56a4196e7": {"0": "Mr"}, "b1d03dd9-71ac-41eb-acbf-850fb22376ec": {"0": "<PERSON>"}, "61cba17a-8ca6-4a1e-ff12-7d5f11e2670e": {"0": ""}, "db7e682d-9a6f-4af7-c3fa-638de3886eae": {"0": "Le"}, "5f564c59-54c8-4783-edd5-00cd2ce683fa": {"0": "OCT5A"}, "f4e290ab-86e5-4c5b-daed-ea1901a59b0f": {"0": ""}, "5966291b-3a11-40c2-c6f0-fac9f23a2ac2": {"0": ""}, "9c1379de-8176-40b7-8d05-ae742007e514": {"0": "Co Nhue"}, "d5a37580-2392-487f-96c7-794751fddaab": {"0": "Bac <PERSON>"}, "9b3d6b30-7048-4eac-bc7a-ac84c59ee79f": {"0": "2193213"}, "e50e150c-18c4-45e8-8493-39d698ee92e6": {"0": ""}, "d878f23d-9d19-4f5f-ce3c-9ca3c92e8e1d": {"0": ""}, "ef2ccb1a-e019-45eb-ba84-cd19bba5b873": {"0": "0928371251"}, "8f085a5f-bbdc-4ee2-8d48-bad1482cce11": {"0": "1"}, "a6ee0c30-32f3-4e05-c0c8-3bb13d4e5529": {"0": "F"}, "7d5c0831-6344-47e3-aa8d-2cecc9d17911": {"0": "1993-05-12T04:08:56.259Z"}}, "case_id": "2c7ff974-6be4-4bd3-b99b-c51b9b6720c9", "parse": 1}, {"id": 2392, "ques_id": "1de310a9-6d53-47ab-fab0-91e91779127b", "answer": {"14033ecf-7e3a-46a0-9f57-0eed60d6c70d": {"0": "1!!"}, "bf92ddd0-01ae-4b4f-b9be-af47df43df4f": {"0": "1"}, "df7a2bd8-4e3a-4c60-a962-c8a6daaef1e0": {"0": "1"}, "0897bbee-755d-4489-c65a-f984ca18d3b7": {"0": "1"}, "14ba207c-22b7-4dfa-cf1d-dc41906a20b3": {"0": "2025-01-09T04:13:08.713Z"}, "68c94e87-fd78-42db-e0df-4aea03433af9": {"0": "2025-01-07T04:13:10.190Z"}, "9460b382-c9dd-4fee-c739-8c620b367b92": {"0": "12"}, "556c59a7-3577-4c79-b1bb-89ffdd36a71b": {"0": "123"}}, "case_id": "2c7ff974-6be4-4bd3-b99b-c51b9b6720c9", "parse": 1}, {"id": 2393, "ques_id": "db90cf66-e34f-4562-e3ad-c45878e41740", "answer": "b35362d1-1fed-4411-a196-fa71e9510e53", "case_id": "2c7ff974-6be4-4bd3-b99b-c51b9b6720c9", "parse": 1}, {"id": 2394, "ques_id": "e18c31db-4ccf-42f1-e8bb-2b4f9331ff32", "answer": "9aaf20ff-9f82-4102-8a08-1aced2bc5010", "case_id": "2c7ff974-6be4-4bd3-b99b-c51b9b6720c9", "parse": 1}, {"id": 2395, "ques_id": "62ef780b-71ec-4579-c47c-2383952980e2", "answer": "b9c026fc-57e9-4870-ec6e-951d767e351d", "case_id": "2c7ff974-6be4-4bd3-b99b-c51b9b6720c9", "parse": 1}, {"id": 2396, "ques_id": "7ca1e251-256f-4cc8-a2f7-9537a6ed469e", "answer": "42820adc-f277-4a5e-cfa7-0a57a9a193e2", "case_id": "2c7ff974-6be4-4bd3-b99b-c51b9b6720c9", "parse": 1}, {"id": 2397, "ques_id": "6205d1d1-501a-4b21-bf7b-c64fcd16335a", "answer": "8764f419-bd90-4a2d-b6a6-2d0374c2ce21", "case_id": "2c7ff974-6be4-4bd3-b99b-c51b9b6720c9", "parse": 1}, {"id": 2398, "ques_id": "d02f709f-6fb6-487f-deec-2c3d3cc87b41", "answer": "4099063a-04ae-45cd-ffd9-6cdc981fa692", "case_id": "2c7ff974-6be4-4bd3-b99b-c51b9b6720c9", "parse": 1}, {"id": 2399, "ques_id": "7b87b8da-5675-4d9f-cb0e-7bc33a2efb14", "answer": "2320b095-b640-45a7-a5fe-24afc80d6f82", "case_id": "2c7ff974-6be4-4bd3-b99b-c51b9b6720c9", "parse": 1}, {"id": 2400, "ques_id": "0de03047-f0a3-47a7-b5f7-ae3a4348eb48", "answer": "a054cfeb-28c1-495b-fc38-e96f3d3b082e", "case_id": "2c7ff974-6be4-4bd3-b99b-c51b9b6720c9", "parse": 1}, {"id": 2401, "ques_id": "50502ccc-6ea5-4814-85ed-90cd62f36c9a", "answer": "ccc20525-3133-4efe-b2c8-d6d7a14b67f5", "case_id": "2c7ff974-6be4-4bd3-b99b-c51b9b6720c9", "parse": 1}, {"id": 2402, "ques_id": "43a3e45d-153d-46c9-e53d-62db8f976791", "answer": "562c4707-1aca-4fab-9cba-1b1eafd192dd", "case_id": "2c7ff974-6be4-4bd3-b99b-c51b9b6720c9", "parse": 1}, {"id": 2403, "ques_id": "369e7172-fbd1-4d09-b57b-29f182b3e96a", "answer": "bf952c0c-f636-420c-caae-b8d25f9ff8f7", "case_id": "2c7ff974-6be4-4bd3-b99b-c51b9b6720c9", "parse": 1}, {"id": 2404, "ques_id": "a81d864a-f9d2-4dbc-e694-ac78b7724319", "answer": "cacc9f89-2094-4462-f980-068f186992a1", "case_id": "2c7ff974-6be4-4bd3-b99b-c51b9b6720c9", "parse": 1}, {"id": 2405, "ques_id": "adf2910c-36b3-48a0-fac3-ec72da618419", "answer": [{"name": "banner_category_1.png", "path": "1736396029778-banner_category_1.png"}], "case_id": "2c7ff974-6be4-4bd3-b99b-c51b9b6720c9", "parse": 1}, {"id": 2406, "ques_id": "47265d3c-67ce-4de0-de55-07857e67432a", "answer": "bfad542e-8c8c-439e-8643-ff64b4fe882b", "case_id": "2c7ff974-6be4-4bd3-b99b-c51b9b6720c9", "parse": 1}, {"id": 2407, "ques_id": "09020ae8-e95d-49aa-bfdc-972a5741abea", "answer": [{"name": "no-items-cart.svg", "path": "1736396036894-no-items-cart.svg"}], "case_id": "2c7ff974-6be4-4bd3-b99b-c51b9b6720c9", "parse": 1}, {"id": 2408, "ques_id": "0ab92110-8fd6-4312-8c77-c2d3231ceed6", "answer": "933e2205-1ffe-43df-ae64-3becc9664f15", "case_id": "2c7ff974-6be4-4bd3-b99b-c51b9b6720c9", "parse": 1}, {"id": 2409, "ques_id": "d1cccc73-2835-4ad9-b8b4-8a611c168d4e", "answer": "ba1fffdc-1680-4975-89ec-fea966834f63", "case_id": "2c7ff974-6be4-4bd3-b99b-c51b9b6720c9", "parse": 1}, {"id": 2410, "ques_id": "1baab982-2995-4618-b029-4361f1a225c8", "answer": "8e28ced5-624d-4b5c-f68c-bc6ad5d44b17", "case_id": "2c7ff974-6be4-4bd3-b99b-c51b9b6720c9", "parse": 1}, {"id": 2411, "ques_id": "33410276-f957-48da-8966-d354108f810a", "answer": null, "case_id": "2c7ff974-6be4-4bd3-b99b-c51b9b6720c9", "parse": 1}, {"id": 2412, "ques_id": "ef9cdd42-0b27-4dff-f532-ff101c9ee54e", "answer": null, "case_id": "2c7ff974-6be4-4bd3-b99b-c51b9b6720c9", "parse": 1}, {"id": 2413, "ques_id": "e46b5feb-9e73-4634-d419-f84f57ba0980", "answer": "44.00", "case_id": "2c7ff974-6be4-4bd3-b99b-c51b9b6720c9", "parse": 1}, {"id": 2414, "ques_id": "9d8c6463-e8e8-477b-ec44-8d06ea737aef", "answer": "0f9d8a1b-e6e6-48e5-d579-e066888a5427", "case_id": "2c7ff974-6be4-4bd3-b99b-c51b9b6720c9", "parse": 1}, {"id": 2415, "ques_id": "c5ddcb8d-2493-4b25-f642-f84a414c9d95", "answer": "c6c25cfe-0f2f-49c4-dba7-090244527b94", "case_id": "2c7ff974-6be4-4bd3-b99b-c51b9b6720c9", "parse": 1}, {"id": 2416, "ques_id": "0a6d6935-a1e7-4cde-bbd7-0457831dffd4", "answer": "06f77924-4062-4ffd-facf-8271495d6b37", "case_id": "2c7ff974-6be4-4bd3-b99b-c51b9b6720c9", "parse": 1}, {"id": 2417, "ques_id": "9c366535-8b43-4835-8494-a4cdda0e0b8b", "answer": "94870fbe-fd2e-4284-f6a6-091ba1d4f02d", "case_id": "2c7ff974-6be4-4bd3-b99b-c51b9b6720c9", "parse": 1}, {"id": 2418, "ques_id": "38893b82-be84-4a07-b240-f43529abcdf6", "answer": "49754d17-5f58-4bae-a70e-448f29b23b68", "case_id": "2c7ff974-6be4-4bd3-b99b-c51b9b6720c9", "parse": 1}, {"id": 2419, "ques_id": "0d4613e0-98a7-49df-f253-57b63abeb174", "answer": {"8c2c2811-4dab-4cc6-a961-69d469c795e8": {"0": "Mr123123123123123", "1": "Mrs", "2": "Mr"}, "bc91b04f-10e1-43fb-b512-fa7a59c554e5": {"0": "Tan1", "1": "Tan2", "2": "Tan4"}, "8ba6e34a-91f5-4427-dd10-8387fb43607e": {"0": "1", "1": "2", "2": "3"}, "ce73495c-baa4-4598-80d5-c140930cb01d": {"0": "Le", "1": "Le", "2": "Le"}, "418e5884-069c-4cdf-d5a5-d415dc16c3ed": {"0": "1", "1": "2", "2": "3"}, "869b6455-25fa-4a8f-e592-620b30fd7500": {"0": "1", "1": "2", "2": "3"}, "8655f59a-9371-4896-a366-55119a20ebb0": {"0": "1", "1": "2", "2": "3"}, "fb38521d-0193-48d1-9c96-b5e20da9d71c": {"0": "1", "1": "2", "2": "3"}, "77867190-b739-405e-b7b1-38df0d8192db": {"0": "1", "1": "2", "2": "3"}, "bb2a1cab-d5e7-4622-d19f-af93ed359f80": {"0": "1", "1": "2", "2": "3"}, "62cf4152-c88d-4048-812f-ea3b405a273e": {"0": "1", "1": "2", "2": "3"}, "acdb6c0c-99e1-4f70-e1d0-a0df7fd5ddcf": {"0": "2025-01-22T08:38:41.001Z", "1": "2025-01-21T08:54:14.795Z", "2": "2025-01-22T08:54:22.020Z"}}, "case_id": "2c7ff974-6be4-4bd3-b99b-c51b9b6720c9", "parse": 1}, {"id": 2420, "ques_id": "bb6504e3-a2b0-4879-9f35-8a7908ce37b3", "answer": "11.00", "case_id": "2c7ff974-6be4-4bd3-b99b-c51b9b6720c9", "parse": 1}, {"id": 2421, "ques_id": "2bede201-67ad-42bc-a7ee-45da91b06245", "answer": {"793fe12b-3efb-44f3-8d16-f1f21990e2ae": {"0": "1", "1": "1", "2": "1"}, "05e8b9c9-5869-46aa-dd4a-5b8c1181e344": {"0": "1", "1": "1", "2": "1"}, "2a35fdd5-7651-4bdc-ac02-48737b896cc0": {"0": "Owned", "1": "Rented", "2": "Owned"}, "96c5e070-4012-43ca-fd02-3e6ac6e277e9": {"0": "2121W1", "1": "1", "2": "1"}, "849e08cb-ff14-4dec-96f1-8e4478522e1b": {"0": "Jointly as joint tenants", "1": "Jointly as joint tenants", "2": "Jointly but type of joint ownership unknown"}, "b3976626-69aa-4073-8ab9-e5e4a7306101": {"0": "15.00", "1": "1.00", "2": "20.00"}, "7b0147d5-9565-482b-f6cb-80ad0c99168a": {"0": "10.00", "1": "1.00", "2": "1.00"}, "117f0ea5-c590-4b7c-fd6a-0d0983c322d3": {"0": "Sold", "1": "Transferred", "2": "Uncertain"}, "0ab54d91-3d0c-4cdc-e73a-8e59e1348b4e": {"0": "1.00", "1": "1.00", "2": "1.00"}, "72c97fdf-9d5b-4c64-f4ff-c4748aae9bbd": {"0": "Yes", "1": "No", "2": "Not known"}, "39c451cb-293c-4cde-a809-7e9a33d4cbc4": {"1": "33333"}}, "case_id": "2c7ff974-6be4-4bd3-b99b-c51b9b6720c9", "parse": 1}, {"id": 2422, "ques_id": "8c2c2811-4dab-4cc6-a961-69d469c795e8", "answer": {"0": "Mr3333", "2": "Mr44"}, "case_id": "2c7ff974-6be4-4bd3-b99b-c51b9b6720c9", "parse": 1}], "userName": "pham", "userEmail": "<EMAIL>"}