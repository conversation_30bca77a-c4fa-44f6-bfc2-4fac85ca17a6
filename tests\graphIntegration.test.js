import { describe, test, expect, beforeEach, jest } from '@jest/globals';
import { GraphMailService } from '../app/services/graphMail.service.js';
import { MailProviderService } from '../app/services/mailProvider.service.js';
import { GraphCredentialsService } from '../app/services/graphCredentials.service.js';

// Mock dependencies
jest.mock('../app/config/db.js');
jest.mock('@azure/msal-node');
jest.mock('@microsoft/microsoft-graph-client');

describe('Microsoft Graph Integration Tests', () => {
  let graphMailService;
  let mailProviderService;
  let graphCredentialsService;

  beforeEach(() => {
    graphMailService = new GraphMailService();
    mailProviderService = new MailProviderService();
    graphCredentialsService = new GraphCredentialsService();
  });

  describe('GraphCredentialsService', () => {
    test('should encrypt and decrypt client secrets', () => {
      const originalSecret = 'test-client-secret-123';
      const encrypted = graphCredentialsService.encrypt(originalSecret);
      const decrypted = graphCredentialsService.decrypt(encrypted);

      expect(encrypted).not.toBe(originalSecret);
      expect(decrypted).toBe(originalSecret);
    });

    test('should handle empty or null values gracefully', () => {
      expect(graphCredentialsService.encrypt('')).toBe('');
      expect(graphCredentialsService.encrypt(null)).toBe(null);
      expect(graphCredentialsService.decrypt('')).toBe('');
    });

    test('should validate credentials data structure', () => {
      const validCredentials = {
        lf_id: 123,
        tenant_id: 'test-tenant-id',
        client_id: 'test-client-id',
        client_secret: 'test-client-secret',
        created_by: 1
      };

      expect(validCredentials.lf_id).toBeDefined();
      expect(validCredentials.tenant_id).toBeDefined();
      expect(validCredentials.client_id).toBeDefined();
      expect(validCredentials.client_secret).toBeDefined();
    });
  });

  describe('MailProviderService', () => {
    test('should validate email data correctly', () => {
      const validEmailData = {
        to: '<EMAIL>',
        subject: 'Test Subject',
        html: '<h1>Test Content</h1>'
      };

      const validation = mailProviderService.validateEmailData(validEmailData);
      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    test('should detect invalid email data', () => {
      const invalidEmailData = {
        to: 'invalid-email',
        subject: '',
        // missing content
      };

      const validation = mailProviderService.validateEmailData(invalidEmailData);
      expect(validation.isValid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
    });

    test('should validate email format correctly', () => {
      expect(mailProviderService.isValidEmail('<EMAIL>')).toBe(true);
      expect(mailProviderService.isValidEmail('<EMAIL>')).toBe(true);
      expect(mailProviderService.isValidEmail('invalid-email')).toBe(false);
      expect(mailProviderService.isValidEmail('test@')).toBe(false);
      expect(mailProviderService.isValidEmail('@example.com')).toBe(false);
    });

    test('should format email data for SendGrid correctly', () => {
      const emailData = {
        to: '<EMAIL>',
        subject: 'Test Subject',
        html: '<h1>Test</h1>',
        cc: '<EMAIL>',
        bcc: '<EMAIL>'
      };

      const formatted = mailProviderService.formatForSendGrid(emailData);
      
      expect(formatted.to).toBe(emailData.to);
      expect(formatted.subject).toBe(emailData.subject);
      expect(formatted.html).toBe(emailData.html);
      expect(formatted.cc).toBe(emailData.cc);
      expect(formatted.bcc).toBe(emailData.bcc);
      expect(formatted.from).toBeDefined();
    });
  });

  describe('GraphMailService', () => {
    test('should format recipients correctly for Graph API', () => {
      const stringRecipient = '<EMAIL>';
      const objectRecipient = { email: '<EMAIL>', name: 'Test User' };
      const mixedRecipients = [stringRecipient, objectRecipient];

      const formatted = graphMailService.formatRecipients(mixedRecipients);
      
      expect(formatted).toHaveLength(2);
      expect(formatted[0].emailAddress.address).toBe('<EMAIL>');
      expect(formatted[1].emailAddress.address).toBe('<EMAIL>');
      expect(formatted[1].emailAddress.name).toBe('Test User');
    });

    test('should handle empty recipients array', () => {
      const formatted = graphMailService.formatRecipients([]);
      expect(formatted).toHaveLength(0);
    });

    test('should handle null recipients', () => {
      const formatted = graphMailService.formatRecipients(null);
      expect(formatted).toHaveLength(0);
    });

    test('should cache tokens correctly', () => {
      const cacheKey = 'tenant_client';
      const token = 'test-token';
      const expiresAt = Date.now() + 3600000; // 1 hour from now

      graphMailService.tokenCache.set(cacheKey, { token, expiresAt });
      
      const cached = graphMailService.tokenCache.get(cacheKey);
      expect(cached.token).toBe(token);
      expect(cached.expiresAt).toBe(expiresAt);
    });
  });

  describe('Integration Scenarios', () => {
    test('should handle law firm without Graph credentials', async () => {
      // Mock hasGraphConfig to return false
      jest.spyOn(mailProviderService, 'hasGraphConfig').mockResolvedValue(false);
      
      const emailData = {
        to: '<EMAIL>',
        subject: 'Test',
        html: '<h1>Test</h1>'
      };

      // Should use SendGrid as primary provider
      const providerInfo = await mailProviderService.getProviderInfo(999);
      expect(providerInfo.primaryProvider).toBe('sendgrid');
      expect(providerInfo.graphConfigured).toBe(false);
    });

    test('should handle law firm with Graph credentials', async () => {
      // Mock hasGraphConfig to return true
      jest.spyOn(mailProviderService, 'hasGraphConfig').mockResolvedValue(true);
      
      const providerInfo = await mailProviderService.getProviderInfo(123);
      expect(providerInfo.primaryProvider).toBe('microsoft-graph');
      expect(providerInfo.graphConfigured).toBe(true);
    });

    test('should validate email data before sending', () => {
      const testCases = [
        {
          data: { to: '<EMAIL>', subject: 'Test', html: '<h1>Test</h1>' },
          shouldBeValid: true
        },
        {
          data: { to: '', subject: 'Test', html: '<h1>Test</h1>' },
          shouldBeValid: false
        },
        {
          data: { to: '<EMAIL>', subject: '', html: '<h1>Test</h1>' },
          shouldBeValid: false
        },
        {
          data: { to: '<EMAIL>', subject: 'Test' },
          shouldBeValid: false
        }
      ];

      testCases.forEach(({ data, shouldBeValid }) => {
        const validation = mailProviderService.validateEmailData(data);
        expect(validation.isValid).toBe(shouldBeValid);
      });
    });
  });

  describe('Error Handling', () => {
    test('should handle encryption errors gracefully', () => {
      // Test with invalid input
      const result = graphCredentialsService.encrypt(undefined);
      expect(result).toBe(undefined);
    });

    test('should handle decryption errors gracefully', () => {
      // Test with malformed encrypted data
      const result = graphCredentialsService.decrypt('invalid-encrypted-data');
      expect(result).toBe('invalid-encrypted-data');
    });

    test('should validate required fields for credentials', () => {
      const incompleteCredentials = {
        lf_id: 123,
        tenant_id: 'test-tenant',
        // missing client_id and client_secret
      };

      expect(incompleteCredentials.client_id).toBeUndefined();
      expect(incompleteCredentials.client_secret).toBeUndefined();
    });
  });

  describe('Security Tests', () => {
    test('should not expose client secrets in responses', () => {
      const credentials = {
        id: 1,
        lf_id: 123,
        tenant_id: 'test-tenant',
        client_id: 'test-client',
        client_secret: 'secret-value',
        is_active: true
      };

      // Simulate removing secret from response
      const safeCredentials = { ...credentials };
      delete safeCredentials.client_secret;

      expect(safeCredentials.client_secret).toBeUndefined();
      expect(safeCredentials.tenant_id).toBeDefined();
      expect(safeCredentials.client_id).toBeDefined();
    });

    test('should encrypt sensitive data', () => {
      const sensitiveData = 'very-secret-client-secret';
      const encrypted = graphCredentialsService.encrypt(sensitiveData);
      
      expect(encrypted).not.toBe(sensitiveData);
      expect(encrypted).toContain(':'); // Should contain IV separator
    });
  });
});
