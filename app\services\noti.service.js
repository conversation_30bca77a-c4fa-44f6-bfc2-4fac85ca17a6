import pool from "../config/db.js";
import { runQuery } from "#utils";
import { connectedUsers, io, notificationsNamespace } from "../../index.js";

export const getListNoti = async (user_id, page, size = 10) => {
  let con = await pool.getConnection();

  try {
    await con.beginTransaction();
    let sql = `SELECT * FROM notification WHERE user_id = ? ORDER BY created_at DESC LIMIT ?, ?`;
    let result = await runQuery(con, sql, [
      user_id,
      (page - 1) * size,
      size * 1,
    ]);
    let sql_count = `SELECT COUNT(*) AS COUNT FROM notification WHERE user_id = ?`;
    let result_ = await runQuery(con, sql_count, [user_id]);
    await con.commit();
    return { lst_noti: result, count: result_[0] };
  } catch (err) {
    await con.rollback();
    return null;
  } finally {
    con.destroy();
  }
};

export const getCountNoti = async (user_id) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    let sql = `SELECT COUNT(*) AS COUNT FROM notification WHERE user_id = ? AND status = 1`;
    let result = await runQuery(con, sql, [user_id]);
    // let sql_get_admin = `SELECT user_id FROM users WHERE role = ?`;
    // let rst = await runQuery(con, sql_get_admin, [1]);
    // let lst_user_id = rst.map((user) => user.user_id);
    // let connectedUser = Object.keys(connectedUsers);
    // let commonUser = lst_user_id.filter((id) =>
    // connectedUser.map((id) => parseInt(id)).includes(id)
    // );

    await con.commit();
    // if (commonUser.length > 0) {
    //   commonUser.forEach((id) => {
    //     notificationsNamespace
    //       .to(connectedUsers[id].id)
    //       .emit("notification", result[0].COUNT);
    //   });
    // } else {
    return result;
    // }
  } catch (err) {
    await con.rollback();
    return null;
  } finally {
    con.destroy();
  }
};

export const getNotiById = async (noti_id) => {
  let con = await pool.getConnection();

  try {
    await con.beginTransaction();

    let sql = `SELECT * FROM notification WHERE noti_id = ?`;
    let result = await runQuery(con, sql, [noti_id]);
    await con.commit();
    return result;
  } catch (err) {
    await con.rollback();
    return null;
  } finally {
    con.destroy();
  }
};

export const updateStatusNoti = async (noti_id, status) => {
  let con = await pool.getConnection();

  try {
    await con.beginTransaction();
    let sql = `UPDATE notification SET status = ? WHERE noti_id = ?`;
    let result = await runQuery(con, sql, [status, noti_id]);
    await con.commit();
    notificationsNamespace;
    return result;
  } catch (err) {
    await con.rollback();
    return null;
  } finally {
    con.destroy();
  }
};

export const updateStatusMultiNoti = async (noti_id, status) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    let lst_noti = noti_id.map(() => "?").join(",");
    let sql = `UPDATE notification SET status = ? WHERE noti_id IN (${lst_noti})`;
    let result = await runQuery(con, sql, [status, ...noti_id]);
    await con.commit();
    notificationsNamespace;
    return result;
  } catch (err) {
    console.log(err);
    await con.rollback();
    return null;
  } finally {
    con.destroy();
  }
};

export const updateStatusSeenAllNoti = async (user_id) => {
  let con = await pool.getConnection();

  try {
    await con.beginTransaction();

    let sql = `UPDATE notification SET status = 3 WHERE user_id = ?`;
    let result = await runQuery(con, sql, [user_id]);
    await con.commit();
    return result;
  } catch (err) {
    await con.rollback();
    return null;
  } finally {
    con.destroy();
  }
};

export const sendNotification = async (receiver, message) => {
  if (!connectedUsers[receiver]) {
    return false;
  }
  notificationsNamespace
    .to(connectedUsers[receiver].id)
    .emit("notification", message);
  return true;
};

export const deleteNotification = async (noti_id) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    let sql = `DELETE FROM notification WHERE noti_id = ?`;
    let result = await runQuery(con, sql, [noti_id]);
    await con.commit();
    return result;
  } catch (err) {
    con.rollback();
    return null;
  } finally {
    con.destroy();
  }
};

// export const insertNotification = async (message, user_id) => {
//   let con = await pool.getConnection();
//   try {
//     await con.beginTransaction();
//     let sql = `INSERT INTO notifications (message, created_at, status, user_id) VALUES (?, NOW(), 1, ?)`;
//     let result = await runQuery(con, sql, [message, user_id]);
//     await con.commit();
//     return result;
//   } catch (err) {
//     con.rollback();
//     return null;
//   } finally {
//     con.destroy();
//   }
// };
