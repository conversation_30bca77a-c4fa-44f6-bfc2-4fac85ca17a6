import { graphCredentialsService, mailProviderService } from "#services";
import { catchAsync } from "#utils";
import { roles } from "#middlewares/roles.js";
import <PERSON>rror<PERSON><PERSON><PERSON> from "#middlewares/ErrorClass.js";

/**
 * Add Microsoft Graph credentials for a law firm
 */
export const addGraphCredentials = catchAsync(async (req, res) => {
  const role = req.user.role;
  // Only Admin and LawfirmSuperAdmin can add credentials
  if (role != roles.Admin && role != roles.LawfirmSuperAdmin) {
    return res.status(403).json({
      success: false,
      message: "Permission denied!"
    });
  }

  const { lf_id, tenant_id, client_id, client_secret } = req.body;

  // Validate required fields
  if (!lf_id || !tenant_id || !client_id || !client_secret) {
    return res.status(400).json({
      success: false,
      message: "All fields are required: lf_id, tenant_id, client_id, client_secret"
    });
  }

  // LawfirmSuperAdmin can only manage their own law firm
  if (role == roles.LawfirmSuperAdmin && req.user.lf_id != parseInt(lf_id)) {
    return res.status(403).json({
      success: false,
      message: "You can only manage credentials for your own law firm"
    });
  }

  try {
    const credentials = await graphCredentialsService.addCredentials({
      lf_id,
      tenant_id,
      client_id,
      client_secret,
      created_by: req.user.user_id
    });

    return res.status(201).json({
      success: true,
      message: "Graph credentials added successfully",
      data: credentials
    });
  } catch (error) {
    return res.status(400).json({
      success: false,
      message: error.message
    });
  }
});

/**
 * Get Microsoft Graph credentials for a law firm
 */
export const getGraphCredentials = catchAsync(async (req, res) => {
  const role = req.user.role;
  const { lf_id } = req.params;

  // Check permissions
  if (role != roles.Admin && role != roles.LawfirmSuperAdmin && role != roles.LawfirmAdmin) {
    return res.status(403).json({
      success: false,
      message: "Permission denied!"
    });
  }

  // Non-admin users can only access their own law firm
  if (role != roles.Admin && req.user.lf_id != parseInt(lf_id)) {
    return res.status(403).json({
      success: false,
      message: "You can only access credentials for your own law firm"
    });
  }

  try {
    const credentials = await graphCredentialsService.getCredentials(lf_id, false);
    
    if (!credentials) {
      return res.status(404).json({
        success: false,
        message: "Graph credentials not found for this law firm"
      });
    }

    return res.status(200).json({
      success: true,
      message: "Graph credentials retrieved successfully",
      data: credentials
    });
  } catch (error) {
    return res.status(400).json({
      success: false,
      message: error.message
    });
  }
});

/**
 * Update Microsoft Graph credentials for a law firm
 */
export const updateGraphCredentials = catchAsync(async (req, res) => {
  const role = req.user.role;
  const { lf_id } = req.params;

  // Only Admin and LawfirmSuperAdmin can update credentials
  if (role != roles.Admin && role != roles.LawfirmSuperAdmin) {
    return res.status(403).json({
      success: false,
      message: "Permission denied!"
    });
  }

  // LawfirmSuperAdmin can only manage their own law firm
  if (role == roles.LawfirmSuperAdmin && req.user.lf_id != parseInt(lf_id)) {
    return res.status(403).json({
      success: false,
      message: "You can only manage credentials for your own law firm"
    });
  }

  const { tenant_id, client_id, client_secret, is_active } = req.body;

  try {
    const result = await graphCredentialsService.updateCredentials(lf_id, {
      tenant_id,
      client_id,
      client_secret,
      is_active,
      updated_by: req.user.user_id
    });

    return res.status(200).json({
      success: true,
      message: "Graph credentials updated successfully",
      data: result
    });
  } catch (error) {
    return res.status(400).json({
      success: false,
      message: error.message
    });
  }
});

/**
 * Delete Microsoft Graph credentials for a law firm
 */
export const deleteGraphCredentials = catchAsync(async (req, res) => {
  const role = req.user.role;
  const { lf_id } = req.params;

  // Only Admin and LawfirmSuperAdmin can delete credentials
  if (role != roles.Admin && role != roles.LawfirmSuperAdmin) {
    return res.status(403).json({
      success: false,
      message: "Permission denied!"
    });
  }

  // LawfirmSuperAdmin can only manage their own law firm
  if (role == roles.LawfirmSuperAdmin && req.user.lf_id != parseInt(lf_id)) {
    return res.status(403).json({
      success: false,
      message: "You can only manage credentials for your own law firm"
    });
  }

  try {
    const result = await graphCredentialsService.deleteCredentials(lf_id, req.user.user_id);

    return res.status(200).json({
      success: true,
      message: "Graph credentials deleted successfully",
      data: result
    });
  } catch (error) {
    return res.status(400).json({
      success: false,
      message: error.message
    });
  }
});

/**
 * Get all law firms with Graph credentials (Admin only)
 */
export const getAllGraphCredentials = catchAsync(async (req, res) => {
  const role = req.user.role;

  // Only Admin can view all credentials
  if (role != roles.Admin) {
    return res.status(403).json({
      success: false,
      message: "Permission denied!"
    });
  }

  try {
    const credentials = await graphCredentialsService.getAllWithCredentials();

    return res.status(200).json({
      success: true,
      message: "All Graph credentials retrieved successfully",
      data: credentials
    });
  } catch (error) {
    return res.status(400).json({
      success: false,
      message: error.message
    });
  }
});

/**
 * Test Graph API connection for a law firm
 */
export const testGraphConnection = catchAsync(async (req, res) => {
  const role = req.user.role;
  const { lf_id } = req.params;

  // Check permissions
  if (role != roles.Admin && role != roles.LawfirmSuperAdmin && role != roles.LawfirmAdmin) {
    return res.status(403).json({
      success: false,
      message: "Permission denied!"
    });
  }

  // Non-admin users can only test their own law firm
  if (role != roles.Admin && req.user.lf_id != parseInt(lf_id)) {
    return res.status(403).json({
      success: false,
      message: "You can only test credentials for your own law firm"
    });
  }

  try {
    const testResults = await mailProviderService.testProviders(lf_id);

    return res.status(200).json({
      success: true,
      message: "Connection test completed",
      data: testResults
    });
  } catch (error) {
    return res.status(400).json({
      success: false,
      message: error.message
    });
  }
});

/**
 * Get mail provider info for a law firm
 */
export const getProviderInfo = catchAsync(async (req, res) => {
  const role = req.user.role;
  const { lf_id } = req.params;

  // Check permissions
  if (role != roles.Admin && role != roles.LawfirmSuperAdmin && role != roles.LawfirmAdmin) {
    return res.status(403).json({
      success: false,
      message: "Permission denied!"
    });
  }

  // Non-admin users can only access their own law firm
  if (role != roles.Admin && req.user.lf_id != parseInt(lf_id)) {
    return res.status(403).json({
      success: false,
      message: "You can only access provider info for your own law firm"
    });
  }

  try {
    const providerInfo = await mailProviderService.getProviderInfo(lf_id);

    return res.status(200).json({
      success: true,
      message: "Provider info retrieved successfully",
      data: providerInfo
    });
  } catch (error) {
    return res.status(400).json({
      success: false,
      message: error.message
    });
  }
});
