import express from "express";
import baseRoute from "./base.route.js";
import userRoute from "./user.route.js";
import templateRoute from "../private/template.route.js";
import mailRoute from "./mail.route.js";
import questionaireRoutes from "./questionaire.route.js";
import caseRoutes from "./case.route.js";
import smsRoute from "./sms.route.js";
import billingRoute from "./billing.route.js";
import modalRoute from "./modal.route.js";
import notiRoute from "./noti.route.js";
import customMailRoutes from "./customMail.route.js";
import graphCredentialsRoutes from "./graphCredentials.route.js";
import apiTokenRoutes from "./apiToken.route.js";
import {
  roleBasedAccessControl,
  routeProtection,
  authProtection
} from "#middlewares/authMiddleware.js";
import { refreshToken } from "#controllers/user.controller.js";
const router = express.Router();
router.use("/base", routeProtection, baseRoute);
router.use("/user", routeProtection, userRoute);
router.use("/template", routeProtection, templateRoute);
router.use("/mail", routeProtection, mailRoute);
router.use("/questionaire", routeProtection, questionaireRoutes);
router.use("/case", routeProtection, caseRoutes);
router.use("/sms", routeProtection, smsRoute);
router.use("/billing", routeProtection, billingRoute);
router.use("/modal", routeProtection, modalRoute);
router.use("/noti", notiRoute);
router.post("/refreshToken", authProtection, refreshToken);
router.use("/customMail", routeProtection, customMailRoutes);
router.use("/graph-credentials", routeProtection, graphCredentialsRoutes);
router.use("/api-tokens", routeProtection, apiTokenRoutes);
export default router;
