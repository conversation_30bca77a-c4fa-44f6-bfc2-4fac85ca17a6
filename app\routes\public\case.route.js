import express from "express";
import {
  getPrefixFromCaseId,

  //   createLink,
  //   getCompletedRequests,
} from "#controllers/case.controller.js";

const caseRoutes = express.Router();

/**
 * @swagger
 * /public/case/getPrefixFromCaseId:
 *   get:
 *     summary: Get law firm prefix from case ID
 *     tags: [Cases]
 *     parameters:
 *       - in: query
 *         name: case_id
 *         schema:
 *           type: string
 *         required: true
 *         description: Case ID to get prefix for
 *     responses:
 *       200:
 *         description: Prefix retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 prefix:
 *                   type: string
 *                   description: Law firm prefix
 *       404:
 *         description: Case not found
 *       500:
 *         description: Error retrieving prefix
 */
caseRoutes.route("/getPrefixFromCaseId").get(getPrefixFromCaseId);

export default caseRoutes;
