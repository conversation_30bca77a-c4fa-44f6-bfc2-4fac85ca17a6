SELECT 
	COUNT(*)
FROM users u
JOIN user_info ui 
ON u.user_id  = ui.user_id 
JOIN `roles` r 
ON r.id = u.role
JOIN `status` s 
ON s.id = u.status
WHERE u.lf_id = ?
AND u.role in (2,3,5)
AND (ui.first_name like ?
OR ui.last_name like ?
OR u.email like ?
OR ui.country like ?
OR u.user_id like ?
OR r.name like ?
OR s.name like ?
OR COALESCE(NULLIF(ui.mb_phone, ''), NULLIF(ui.wrk_phone, ''), NULLIF(ui.home_phone, '')) like ?
);
