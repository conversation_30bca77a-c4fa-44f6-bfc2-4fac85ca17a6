-- Migration: Create API tokens table for firm-level API access
-- Date: 2025-01-26
-- Description: Creates table to store API tokens for AdminAPI users to access firm-level functions

-- Create the api_tokens table
CREATE TABLE IF NOT EXISTS api_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    lf_id INT NOT NULL,
    user_id INT NOT NULL,
    token_name VARCHAR(255) NOT NULL,
    token_hash VARCHAR(255) NOT NULL,
    token_prefix VARCHAR(16) NOT NULL,
    permissions JSON DEFAULT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    expires_at TIMESTAMP NULL,
    last_used_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,

    UNIQUE KEY unique_token_hash (token_hash),
    UNIQUE KEY unique_token_prefix (token_prefix),
    KEY idx_lf_id (lf_id),
    KEY idx_user_id (user_id),
    KEY idx_token_hash (token_hash),
    KEY idx_token_prefix (token_prefix),
    KEY idx_active (is_active),
    KEY idx_expires_at (expires_at),

    CONSTRAINT fk_api_tokens_lf_id FOREIGN KEY (lf_id) REFERENCES law_firm(lf_id) ON DELETE CASCADE,
    CONSTRAINT fk_api_tokens_user_id FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    CONSTRAINT fk_api_tokens_created_by FOREIGN KEY (created_by) REFERENCES users(user_id) ON DELETE SET NULL,
    CONSTRAINT fk_api_tokens_updated_by FOREIGN KEY (updated_by) REFERENCES users(user_id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='API tokens for firm-level access by AdminAPI users';

-- Create a view for safe token access (without sensitive data)
CREATE OR REPLACE VIEW api_tokens_safe AS
SELECT
    id,
    lf_id,
    user_id,
    token_name,
    token_prefix,
    permissions,
    is_active,
    expires_at,
    last_used_at,
    created_at,
    updated_at,
    created_by,
    updated_by
FROM api_tokens;

-- Grant appropriate permissions (adjust as needed for your setup)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON api_tokens TO 'propero_app'@'%';
-- GRANT SELECT ON api_tokens_safe TO 'propero_readonly'@'%';
