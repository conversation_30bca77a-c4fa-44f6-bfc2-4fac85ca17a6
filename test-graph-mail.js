#!/usr/bin/env node

/**
 * Standalone Graph API Mail Test
 * 
 * This script tests Microsoft Graph API email sending without running the full server.
 * It uses the provided credentials directly to test the Graph API integration.
 * 
 * Usage:
 *   node test-graph-mail.js
 * 
 * Requirements:
 *   - npm install @azure/msal-node @microsoft/microsoft-graph-client
 */

import { Client } from '@microsoft/microsoft-graph-client';
import { ConfidentialClientApplication } from '@azure/msal-node';

// Test credentials (replace with your actual credentials)
const TEST_CREDENTIALS = {
  tenant_id: "c294f073-4dd8-42a8-ac2c-028a2c9d9983",
  client_id: "c06acc85-fd60-4f49-b87e-10e2ef62222b",
  client_secret: "****************************************"
};

// Test email configuration
const TEST_EMAIL = {
  from: "<EMAIL>", // This will be overridden by the actual sender
  to: "<EMAIL>", // CHANGE THIS TO YOUR TEST EMAIL
  subject: "Test Email from Graph API",
  html: `
    <h2>🧪 Graph API Test Email</h2>
    <p>This is a test email sent directly via Microsoft Graph API.</p>
    <p><strong>Timestamp:</strong> ${new Date().toISOString()}</p>
    <p><strong>Test Status:</strong> ✅ Graph API is working correctly!</p>
    <hr>
    <p><em>This email was sent using the standalone Graph API test script.</em></p>
  `,
  text: `Graph API Test Email\n\nThis is a test email sent directly via Microsoft Graph API.\nTimestamp: ${new Date().toISOString()}\nTest Status: Graph API is working correctly!`
};

// User email to send from (CHANGE THIS TO A VALID USER IN YOUR TENANT)
const SENDER_EMAIL = "<EMAIL>"; // CHANGE THIS!

class GraphMailTester {
  constructor(credentials) {
    this.credentials = credentials;
    this.msalConfig = {
      auth: {
        clientId: credentials.client_id,
        clientSecret: credentials.client_secret,
        authority: `https://login.microsoftonline.com/${credentials.tenant_id}`
      }
    };
    this.cca = new ConfidentialClientApplication(this.msalConfig);
  }

  /**
   * Get access token for Graph API
   */
  async getAccessToken() {
    try {
      console.log('🔑 Getting access token...');
      
      const clientCredentialRequest = {
        scopes: ['https://graph.microsoft.com/.default']
      };

      const response = await this.cca.acquireTokenByClientCredential(clientCredentialRequest);
      
      if (!response || !response.accessToken) {
        throw new Error('Failed to acquire access token');
      }

      console.log('✅ Access token acquired successfully');
      return response.accessToken;
    } catch (error) {
      console.error('❌ Failed to get access token:', error.message);
      throw error;
    }
  }

  /**
   * Create Graph client with authentication
   */
  createGraphClient(accessToken) {
    return Client.init({
      authProvider: (done) => {
        done(null, accessToken);
      }
    });
  }

  /**
   * Test Graph API connection by getting organization info
   */
  async testConnection(graphClient) {
    try {
      console.log('🔍 Testing Graph API connection...');
      
      const organization = await graphClient.api('/organization').get();
      console.log('✅ Graph API connection successful');
      console.log(`   Organization: ${organization.value[0]?.displayName || 'Unknown'}`);
      console.log(`   Tenant ID: ${organization.value[0]?.id || 'Unknown'}`);
      
      return true;
    } catch (error) {
      console.error('❌ Graph API connection failed:', error.message);
      return false;
    }
  }

  /**
   * Get user information
   */
  async getUserInfo(graphClient, userEmail) {
    try {
      console.log(`👤 Getting user info for: ${userEmail}`);
      
      const user = await graphClient.api(`/users/${userEmail}`).get();
      console.log('✅ User found:');
      console.log(`   Display Name: ${user.displayName || 'N/A'}`);
      console.log(`   Email: ${user.mail || user.userPrincipalName}`);
      console.log(`   User Type: ${user.userType || 'N/A'}`);
      console.log(`   Account Enabled: ${user.accountEnabled}`);
      
      return user;
    } catch (error) {
      console.error(`❌ Failed to get user info for ${userEmail}:`, error.message);
      throw error;
    }
  }

  /**
   * Send test email
   */
  async sendTestEmail(graphClient, senderEmail, emailData) {
    try {
      console.log('📧 Preparing email message...');
      
      const message = {
        message: {
          subject: emailData.subject,
          body: {
            contentType: 'HTML',
            content: emailData.html
          },
          toRecipients: [{
            emailAddress: {
              address: emailData.to
            }
          }]
        }
      };

      console.log(`📤 Sending email from: ${senderEmail}`);
      console.log(`📬 Sending email to: ${emailData.to}`);
      console.log(`📝 Subject: ${emailData.subject}`);
      
      const result = await graphClient
        .api(`/users/${senderEmail}/sendMail`)
        .post(message);

      console.log('✅ Email sent successfully!');
      console.log(`   Message ID: ${result?.id || 'N/A'}`);
      
      return {
        success: true,
        messageId: result?.id || 'sent',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('❌ Failed to send email:', error.message);
      
      // Provide specific error guidance
      if (error.message.includes('mailbox is either inactive, soft-deleted, or is hosted on-premise')) {
        console.error('💡 Suggestion: The sender email might be an external user or doesn\'t have a mailbox.');
        console.error('   Try using an internal user email from your tenant.');
      } else if (error.message.includes('Insufficient privileges')) {
        console.error('💡 Suggestion: The application might not have Mail.Send permission.');
        console.error('   Check the app registration permissions in Azure Portal.');
      } else if (error.message.includes('does not exist')) {
        console.error('💡 Suggestion: The sender email doesn\'t exist in the tenant.');
        console.error('   Make sure to use a valid user email from your organization.');
      }
      
      throw error;
    }
  }

  /**
   * Run the complete test
   */
  async runTest() {
    console.log('🚀 Starting Graph API Mail Test');
    console.log('================================');
    console.log(`Tenant ID: ${this.credentials.tenant_id}`);
    console.log(`Client ID: ${this.credentials.client_id}`);
    console.log(`Sender Email: ${SENDER_EMAIL}`);
    console.log(`Recipient Email: ${TEST_EMAIL.to}`);
    console.log('');

    try {
      // Step 1: Get access token
      const accessToken = await this.getAccessToken();
      
      // Step 2: Create Graph client
      const graphClient = this.createGraphClient(accessToken);
      
      // Step 3: Test connection
      const connectionOk = await this.testConnection(graphClient);
      if (!connectionOk) {
        throw new Error('Graph API connection test failed');
      }
      
      // Step 4: Get user info
      const user = await this.getUserInfo(graphClient, SENDER_EMAIL);
      
      // Step 5: Send test email
      const result = await this.sendTestEmail(graphClient, SENDER_EMAIL, TEST_EMAIL);
      
      console.log('');
      console.log('🎉 Test completed successfully!');
      console.log('================================');
      console.log('✅ Access token: OK');
      console.log('✅ Graph API connection: OK');
      console.log('✅ User lookup: OK');
      console.log('✅ Email sending: OK');
      console.log('');
      console.log('💡 Check the recipient email inbox for the test message.');
      
      return result;
      
    } catch (error) {
      console.log('');
      console.log('❌ Test failed!');
      console.log('===============');
      console.error('Error:', error.message);
      console.log('');
      console.log('🔧 Troubleshooting tips:');
      console.log('1. Verify the credentials are correct');
      console.log('2. Ensure the app has proper permissions (Mail.Send)');
      console.log('3. Check that admin consent has been granted');
      console.log('4. Verify the sender email exists in the tenant');
      console.log('5. Make sure the sender has an Exchange Online mailbox');
      
      throw error;
    }
  }
}

// Main execution
async function main() {
  // Validation
  if (SENDER_EMAIL === "<EMAIL>") {
    console.error('❌ Please update SENDER_EMAIL with a valid user from your tenant!');
    process.exit(1);
  }
  
  if (TEST_EMAIL.to === "<EMAIL>") {
    console.error('❌ Please update TEST_EMAIL.to with a valid recipient email!');
    process.exit(1);
  }

  const tester = new GraphMailTester(TEST_CREDENTIALS);
  
  try {
    await tester.runTest();
    process.exit(0);
  } catch (error) {
    process.exit(1);
  }
}

// Run the test
main().catch(console.error);
