import Error<PERSON><PERSON><PERSON> from "#middlewares/ErrorClass.js";
import { runQuery } from "#utils";
import pool from "../config/db.js";
import * as fs from "fs";

export const createModal = async (obj) => {
  let con = await pool.getConnection();

  try {
    await con.beginTransaction();
    // let res = await runQuery(con, "Select * from modal where name = ?",[obj.name]);
    // if (res.length>0){
    //   throw new Error('Modal name duplicated');

    // }
    let modal = await runQuery(
      con,
      "Insert into modal (content,name,status,created_at) VALUES (?,?,1, NOW())",
      [JSON.stringify(obj.content), obj.name]
    );
    await con.commit();
    return modal.insertId;
  } catch (error) {
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const getAllModal = async (page, size, keyword, status) => {
  let con = await pool.getConnection();

  try {
    await con.beginTransaction();
    let temp_ans, temp_count;
    if (keyword == null) {
      var sql = fs.readFileSync("app/sql/getAllModal.sql").toString();
      temp_ans = await runQuery(con, sql, [
        status,
        (page - 1) * size,
        size * 1,
      ]);
      var sqlcount = fs.readFileSync("app/sql/getAllModalCount.sql").toString();
      temp_count = await runQuery(con, sqlcount, [status]);
    } else {
      var sql = fs.readFileSync("app/sql/getAllModalKeyword.sql").toString();
      temp_ans = await runQuery(con, sql, [
        status,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        (page - 1) * size,
        size * 1,
      ]);
      var sqlcount = fs
        .readFileSync("app/sql/getAllModalCountKeyword.sql")
        .toString();
      temp_count = await runQuery(con, sqlcount, [
        status,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
      ]);
    }
    let count = temp_count[0]["COUNT(*)"];
    await con.commit();
    return { temp_ans, count };
  } catch (error) {
    await con.rollback();
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const getModal = async (id) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();

    let ans = await runQuery(con, "Select * from modal where id = ?", [id]);
    await con.commit();

    if (ans[0]?.content) ans[0].content = JSON.parse(ans[0]?.content);
    return ans[0];
  } catch (error) {
    await con.rollback();
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const deleteModal = async (id) => {
  let con = await pool.getConnection();

  try {
    await con.beginTransaction();

    await runQuery(con, "Delete from modal where id = ?", [id]);
    await con.commit();
    return 1;
  } catch (error) {
    console.log(error);
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const updateModal = async (obj) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    await runQuery(
      con,
      "Update modal set content = ?, name = ?, status = ?, update_at = NOW() where id = ?",
      [
        JSON.stringify(obj.content),
        obj.name,
        typeof obj?.status !== "undefined" ? obj?.status : 1,
        obj.id,
      ]
    );
    await con.commit();
    return 1;
  } catch (error) {
    console.log(error);
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const updateModalStatus = async (obj) => {
  let con = await pool.getConnection();

  try {
    await con.beginTransaction();
    console.log(obj);
    await runQuery(
      con,
      "Update modal SET status = ?, update_at=NOW() where id = ?",
      [typeof obj?.status !== "undefined" ? obj?.status : 1, obj.id]
    );
    await con.commit();
    return 1;
  } catch (error) {
    console.log(error);
    await con.rollback();

    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const insertModalAnswer = async (obj) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    if (obj.user_id) {
      for (const property in obj.answer) {
        await runQuery(
          con,
          "INSERT INTO modal_answer (user_id, field, answer) SELECT ?, ?, ? WHERE NOT EXISTS ( SELECT 1 FROM modal_answer WHERE user_id = ? AND field = ? AND answer = ? )",
          [
            obj.user_id,
            property,
            obj.answer[property],
            obj.user_id,
            property,
            obj.answer[property],
          ]
        );
      }
    }
    await con.commit();
    return 1;
  } catch (error) {
    console.log(error);
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const getModalType = async () => {
  let con = await pool.getConnection();

  try {
    await con.beginTransaction();
    let modalType = await runQuery(con, "SELECT * FROM modal_type");
    await con.commit();
    return modalType;
  } catch (error) {
    console.log(error);
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};
export const addModalType = async (name) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    let modal = await runQuery(
      con,
      "Insert into modal_type (name) VALUES (?)",
      [name]
    );
    await con.commit();
    return modal.insertId;
  } catch (error) {
    console.log(error);
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};
export const deleteModalType = async (id) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    await runQuery(con, "Delete from modal_type where id = ?", [id]);
    await con.commit();
    return 1;
  } catch (error) {
    console.log(error);
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};
