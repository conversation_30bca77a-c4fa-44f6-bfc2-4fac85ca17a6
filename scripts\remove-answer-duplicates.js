import mysql from 'mysql2/promise';
import dotenv from "dotenv";

dotenv.config();

const removeAnswerDuplicates = async () => {
  let connection;
  
  try {
    console.log('🧹 Removing duplicate records from answer table...\n');

    // Create database connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PWD || '',
      database: process.env.DB_NAME || 'propero'
    });

    console.log('✅ Connected to database');

    await connection.beginTransaction();

    // Find duplicate records
    console.log('🔍 Finding duplicate records...');
    
    const duplicates = await connection.query(`
      SELECT ans_id, COUNT(*) as count 
      FROM answer 
      GROUP BY ans_id 
      HAVING COUNT(*) > 1
    `);
    
    console.log(`Found ${duplicates[0].length} ans_id values with duplicates`);
    
    if (duplicates[0].length > 0) {
      // Show total duplicate records
      const totalDuplicateRecords = duplicates[0].reduce((sum, row) => sum + (row.count - 1), 0);
      console.log(`Total duplicate records to remove: ${totalDuplicateRecords}`);
      
      console.log('🗑️  Removing duplicate records (keeping the first one for each ans_id)...');
      
      // Delete duplicates, keeping only the record with the lowest id for each ans_id
      const result = await connection.query(`
        DELETE a1 FROM answer a1
        INNER JOIN answer a2 
        WHERE a1.ans_id = a2.ans_id 
        AND a1.id > a2.id
      `);
      
      console.log(`✅ Removed ${result[0].affectedRows} duplicate records`);
    } else {
      console.log('ℹ️  No duplicate records found');
    }

    await connection.commit();
    console.log('\n🎉 Cleanup completed successfully!');
    
    // Show final count
    const finalCount = await connection.query(`SELECT COUNT(*) as total FROM answer`);
    console.log(`📊 Final count: ${finalCount[0][0].total} records in answer table`);
    
  } catch (error) {
    if (connection) {
      await connection.rollback();
    }
    console.error('❌ Error during cleanup:', error.message);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

// Run the cleanup
removeAnswerDuplicates()
  .then(() => {
    console.log('\n✅ Duplicate removal completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Duplicate removal failed:', error.message);
    process.exit(1);
  });
