import express from "express";

const router = express.Router();

const time = new Date();

/**
 * @swagger
 * /:
 *   get:
 *     summary: Get server status
 *     description: Returns server status information including deployment time and environment
 *     tags: [Server]
 *     responses:
 *       200:
 *         description: Server status information
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Server is running...
 *                 deployTime:
 *                   type: string
 *                   format: date-time
 *                 env:
 *                   type: string
 *                   example: development
 *                 CICD:
 *                   type: string
 *                   nullable: true
 */
router.get("/", (req, res, next) => {
  return res.json({
    message: "Server is running...",
    deployTime: time,
    env: process.env.NODE_ENV,
    CICD: process.env.CI_CD,
  });
  // res.sendFile(join(__dirname, "index.html"));
});

export default router;
