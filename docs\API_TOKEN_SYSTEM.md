# API Token System Documentation

## Overview

The API Token System provides a two-layer credential approach for law firm API access:

1. **Application-level credentials** (existing JWT system)
2. **Firm-specific API tokens** for AdminAPI users to access law firm functions without OTP

This system allows AdminAPI users to perform law firm operations programmatically using secure API tokens instead of requiring OTP verification.

## Features

- **Secure Token Generation**: Cryptographically secure tokens with SHA-256 hashing
- **Role-based Access**: Only AdminAPI users can use API tokens
- **Law Firm Isolation**: Tokens are scoped to specific law firms
- **Token Management**: Create, list, revoke, and monitor token usage
- **Expiration Support**: Optional token expiration dates
- **Permission System**: Extensible permissions for fine-grained access control

## API Token Format

API tokens follow the format: `propero_{prefix}_{token}`

Example: `propero_a1b2c3d4_1234567890abcdef...`

## Database Schema

### api_tokens Table

```sql
CREATE TABLE api_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    lf_id INT NOT NULL,
    user_id INT NOT NULL,
    token_name VARCHAR(255) NOT NULL,
    token_hash VARCHAR(255) NOT NULL,
    token_prefix VARCHAR(16) NOT NULL,
    permissions JSON DEFAULT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    expires_at TIMESTAMP NULL,
    last_used_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    
    FOREIGN KEY (lf_id) REFERENCES law_firm(lf_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    UNIQUE KEY unique_token_hash (token_hash)
);
```

## API Endpoints

### Token Management (Private Routes)

#### Create API Token
```http
POST /private/api-tokens
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "token_name": "My API Token",
  "user_id": 123,
  "lf_id": 456,
  "expires_at": "2025-12-31T23:59:59Z",
  "permissions": {
    "cases": ["read", "write"],
    "users": ["read", "write"]
  }
}
```

#### List API Tokens
```http
GET /private/api-tokens
Authorization: Bearer <jwt_token>
```

#### Revoke API Token
```http
PUT /private/api-tokens/{token_id}/revoke
Authorization: Bearer <jwt_token>
```

### Firm API Routes (API Token Authentication)

#### Login with API Token
```http
POST /api/firm/login
Authorization: Bearer propero_a1b2c3d4_1234567890abcdef...
```

#### Add User to Law Firm
```http
POST /api/firm/users
Authorization: Bearer propero_a1b2c3d4_1234567890abcdef...
Content-Type: application/json

{
  "email": "<EMAIL>",
  "first_name": "John",
  "last_name": "Doe",
  "role": "3"
}
```

#### Create Case
```http
POST /api/firm/cases
Authorization: Bearer propero_a1b2c3d4_1234567890abcdef...
Content-Type: application/json

{
  "case_name": "New Case",
  "desc": "Case description",
  "user_id": 123,
  "qtn_id": 456,
  "case_id_pms": "PMS_REF_001",
  "token_": {
    "{{client_name}}": "John Doe",
    "{{case_reference}}": "REF-2024-001",
    "{{custom_field}}": "Custom Value"
  }
}
```

**Token Replacement**: The `token_` parameter allows you to replace custom tokens in questionnaire templates. Tokens in the questionnaire will be replaced with the provided values during case creation.

#### Get Cases
```http
GET /api/firm/cases?page=1&size=10&status=4
Authorization: Bearer propero_a1b2c3d4_1234567890abcdef...
```

#### Update Case
```http
PUT /api/firm/cases/{case_id}
Authorization: Bearer propero_a1b2c3d4_1234567890abcdef...
Content-Type: application/json

{
  "case_name": "Updated Case Name",
  "status": 5
}
```

#### Export Case Data
```http
GET /api/firm/cases/{case_id}/export?format=json
Authorization: Bearer propero_a1b2c3d4_1234567890abcdef...
```

## Usage Examples

### 1. Creating an API Token

```javascript
// Admin or LawfirmSuperAdmin creates token for AdminAPI user
const response = await fetch('/private/api-tokens', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + adminJwtToken,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    token_name: 'Integration Token',
    user_id: adminApiUserId,
    expires_at: '2025-12-31T23:59:59Z'
  })
});

const { data } = await response.json();
console.log('API Token:', data.token); // Only shown once!
```

### 2. Using API Token for Firm Operations

```javascript
// Use API token to login and get JWT
const loginResponse = await fetch('/api/firm/login', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer propero_a1b2c3d4_1234567890abcdef...'
  }
});

const { data } = await loginResponse.json();
const jwtToken = data.access_token;

// Now use JWT for regular operations or continue with API token
const casesResponse = await fetch('/api/firm/cases', {
  headers: {
    'Authorization': 'Bearer propero_a1b2c3d4_1234567890abcdef...'
  }
});
```

## Security Considerations

1. **Token Storage**: Store tokens securely, never in plain text
2. **Token Rotation**: Regularly rotate tokens for security
3. **Scope Limitation**: Tokens are limited to their law firm
4. **Role Verification**: Only AdminAPI users can use tokens
5. **Audit Trail**: All token usage is logged with timestamps
6. **Expiration**: Set appropriate expiration dates
7. **Revocation**: Immediately revoke compromised tokens

## Error Handling

### Common Error Responses

```json
{
  "success": false,
  "message": "Invalid or expired API token"
}
```

```json
{
  "success": false,
  "message": "AdminAPI role required"
}
```

```json
{
  "success": false,
  "message": "Law firm is not active"
}
```

## Setup and Installation

### Quick Setup

Use the automated setup script:

```bash
npm run setup-api-tokens
```

This script will:
- Create the `api_tokens` database table
- Create an AdminAPI user if none exists
- Generate an initial API token
- Provide credentials and usage examples

### Manual Setup

1. **Run the migration script:**
   ```sql
   SOURCE migrations/003_create_api_tokens_table.sql;
   ```

2. **Create AdminAPI user** (if not exists):
   ```sql
   INSERT INTO users (email, password, role, status, lf_id, is_owner)
   VALUES ('<EMAIL>', 'hashed_password', '6', 1, 1, 0);
   ```

3. **Create API tokens** via the management endpoints

### Environment Variables

No additional environment variables are required. The system uses existing database and JWT configurations.

## Monitoring

- Monitor `last_used_at` timestamps for token activity
- Track token creation and revocation in application logs
- Set up alerts for unusual token usage patterns
- Regular audit of active tokens and their permissions
