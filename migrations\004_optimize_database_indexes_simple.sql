-- Migration: Database Optimization - Add Indexes for Better Performance (Simple Version)
-- Date: 2025-01-26
-- Description: Creates indexes on frequently queried columns to optimize getTemplate, getQuestionnaire, and getCases functions
-- Note: Compatible with all MySQL versions (no IF NOT EXISTS syntax)

-- ============================================================================
-- CRITICAL PERFORMANCE INDEXES
-- ============================================================================

-- GROUPS TABLE - Foreign key index for template relationships (most important)
CREATE INDEX idx_groups_tem_id ON `groups`(tem_id);

-- QUESTIONS TABLE - Foreign key index for group relationships (critical for N+1 elimination)
CREATE INDEX idx_questions_gr_id ON questions(gr_id);

-- ANSWER TABLE - Foreign key index for question relationships
CREATE INDEX idx_answer_ques_id ON answer(ques_id);

-- ANSWERS TABLE - Foreign key index for answer relationships
CREATE INDEX idx_answers_ques_id ON answers(ques_id);

-- QUESTIONAIRES TABLE - Law firm filtering (multi-tenant)
CREATE INDEX idx_questionaires_lf_id ON questionaires(lf_id);

-- QUESTIONAIRES TABLE - Template relationship
CREATE INDEX idx_questionaires_tem_id ON questionaires(tem_id);

-- CASES TABLE - Law firm filtering (critical for getCases)
CREATE INDEX idx_cases_lf_id ON cases(lf_id);

-- CASES TABLE - Combined index for law firm cases with ordering
CREATE INDEX idx_cases_lf_created ON cases(lf_id, created_at DESC);

-- ============================================================================
-- ADDITIONAL PERFORMANCE INDEXES
-- ============================================================================

-- Templates status filtering
CREATE INDEX idx_templates_status ON templates(status);

-- Questions parent_answer relationships
CREATE INDEX idx_questions_parent_answer ON questions(parent_answer);

-- Questionaires status filtering
CREATE INDEX idx_questionaires_status ON questionaires(status);

-- Cases user filtering
CREATE INDEX idx_cases_user_id ON cases(user_id);

-- Cases status filtering
CREATE INDEX idx_cases_status ON cases(status);

-- Cases questionnaire relationship
CREATE INDEX idx_cases_qtn_id ON cases(qtn_id);

-- Users law firm relationship
CREATE INDEX idx_users_lf_id ON users(lf_id);

-- User_info foreign key
CREATE INDEX idx_user_info_user_id ON user_info(user_id);

-- Conditions template relationship
CREATE INDEX idx_conditions_template_id ON `conditions`(template_id);

-- ============================================================================
-- COMPOSITE INDEXES FOR COMPLEX QUERIES
-- ============================================================================

-- Questionaires: law firm + status + ordering
CREATE INDEX idx_questionaires_lf_status_created ON questionaires(lf_id, status, created_at DESC);

-- Cases: law firm + status + ordering
CREATE INDEX idx_cases_lf_status_created ON cases(lf_id, status, created_at DESC);

-- Cases: law firm + user + ordering
CREATE INDEX idx_cases_lf_user_created ON cases(lf_id, user_id, created_at DESC);

-- Groups: template + ordering
CREATE INDEX idx_groups_tem_id_id ON `groups`(tem_id, id);

-- Questions: group + ordering
CREATE INDEX idx_questions_gr_id_id ON questions(gr_id, id);

-- ============================================================================
-- VERIFICATION QUERIES
-- ============================================================================

-- Check that indexes were created successfully
SHOW INDEX FROM `groups` WHERE Key_name = 'idx_groups_tem_id';
SHOW INDEX FROM questions WHERE Key_name = 'idx_questions_gr_id';
SHOW INDEX FROM answer WHERE Key_name = 'idx_answer_ques_id';
SHOW INDEX FROM answers WHERE Key_name = 'idx_answers_ques_id';
SHOW INDEX FROM questionaires WHERE Key_name = 'idx_questionaires_lf_id';
SHOW INDEX FROM cases WHERE Key_name = 'idx_cases_lf_id';

-- ============================================================================
-- PERFORMANCE TESTING QUERIES
-- ============================================================================

-- Test template query performance
-- EXPLAIN SELECT t.*, g.*, q.*, a.*, ans.* 
-- FROM templates t 
-- LEFT JOIN `groups` g ON g.tem_id = t.id 
-- LEFT JOIN questions q ON q.gr_id = g.id 
-- LEFT JOIN answer a ON a.ques_id = q.id 
-- LEFT JOIN answers ans ON ans.ques_id = a.ans_id 
-- WHERE t.id = 1;

-- Test questionnaire query performance
-- EXPLAIN SELECT q.*, l.lf_org_name, t.tem_name 
-- FROM questionaires q 
-- JOIN law_firm l ON l.lf_id = q.lf_id 
-- JOIN templates t ON t.id = q.tem_id 
-- WHERE q.lf_id = 1;

-- Test cases query performance
-- EXPLAIN SELECT c.*, u.email, ui.first_name, ui.last_name 
-- FROM cases c 
-- JOIN users u ON c.user_id = u.user_id 
-- JOIN user_info ui ON u.user_id = ui.user_id 
-- WHERE c.lf_id = 1 
-- ORDER BY c.created_at DESC 
-- LIMIT 10;

-- ============================================================================
-- NOTES
-- ============================================================================

-- 1. Run this script on your database to create the performance indexes
-- 2. Monitor query performance before and after using EXPLAIN
-- 3. Check slow query log for improvements
-- 4. Some indexes may already exist - MySQL will show warnings but continue
-- 5. Primary key indexes (id columns) are automatically created by MySQL

-- ============================================================================
-- ROLLBACK (if needed)
-- ============================================================================

-- To remove these indexes, uncomment and run:
/*
DROP INDEX idx_groups_tem_id ON `groups`;
DROP INDEX idx_questions_gr_id ON questions;
DROP INDEX idx_answer_ques_id ON answer;
DROP INDEX idx_answers_ques_id ON answers;
DROP INDEX idx_questionaires_lf_id ON questionaires;
DROP INDEX idx_questionaires_tem_id ON questionaires;
DROP INDEX idx_cases_lf_id ON cases;
DROP INDEX idx_cases_lf_created ON cases;
DROP INDEX idx_templates_status ON templates;
DROP INDEX idx_questions_parent_answer ON questions;
DROP INDEX idx_questionaires_status ON questionaires;
DROP INDEX idx_cases_user_id ON cases;
DROP INDEX idx_cases_status ON cases;
DROP INDEX idx_cases_qtn_id ON cases;
DROP INDEX idx_users_lf_id ON users;
DROP INDEX idx_user_info_user_id ON user_info;
DROP INDEX idx_conditions_template_id ON `conditions`;
DROP INDEX idx_questionaires_lf_status_created ON questionaires;
DROP INDEX idx_cases_lf_status_created ON cases;
DROP INDEX idx_cases_lf_user_created ON cases;
DROP INDEX idx_groups_tem_id_id ON `groups`;
DROP INDEX idx_questions_gr_id_id ON questions;
*/
