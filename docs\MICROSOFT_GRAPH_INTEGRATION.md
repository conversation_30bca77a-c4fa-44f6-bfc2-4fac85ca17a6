# Microsoft Graph API Integration

This document describes the Microsoft Graph API integration for multi-tenant email sending in the Propero system.

## Overview

The system now supports sending emails through Microsoft Graph API alongside the existing SendGrid integration. This allows law firms to use their own Microsoft Entra ID (Azure AD) credentials to send emails on behalf of their users.

## Features

- **Multi-tenant Support**: Each law firm can configure their own Microsoft Graph credentials
- **Automatic Provider Selection**: System automatically chooses between Graph API and SendGrid based on configuration
- **Fallback Mechanism**: If Graph API fails, the system falls back to SendGrid
- **Secure Credential Storage**: Client secrets are encrypted in the database
- **Role-based Access Control**: Only admins and law firm super admins can manage credentials
- **Connection Testing**: Built-in functionality to test Graph API connections

## Architecture

### Components

1. **GraphMailService**: Handles Microsoft Graph API authentication and email sending
2. **GraphCredentialsService**: Manages CRUD operations for Graph credentials
3. **MailProviderService**: Factory pattern to choose between SendGrid and Graph API
4. **Enhanced Mail Functions**: Updated mail functions that support both providers

### Database Schema

```sql
CREATE TABLE graph_credentials (
    id INT AUTO_INCREMENT PRIMARY KEY,
    lf_id INT NOT NULL,
    tenant_id VARCHAR(255) NOT NULL,
    client_id VARCHAR(255) NOT NULL,
    client_secret TEXT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    FOREIGN KEY (lf_id) REFERENCES law_firm(lf_id) ON DELETE CASCADE,
    UNIQUE KEY unique_lf_graph (lf_id)
);
```

## Setup Instructions

### 1. Microsoft Entra ID Configuration

Each law firm needs to configure their Microsoft Entra ID application:

1. **Register Application**:
   - Go to Azure Portal > Entra ID > App registrations
   - Click "New registration"
   - Name: "Propero Email Integration"
   - Supported account types: "Accounts in this organizational directory only"

2. **Configure API Permissions**:
   - Add Microsoft Graph permissions:
     - `Mail.Send` (Application permission)
     - `User.Read` (Application permission)
   - Grant admin consent for the organization

3. **Create Client Secret**:
   - Go to "Certificates & secrets"
   - Click "New client secret"
   - Copy the secret value (you won't see it again)

4. **Note Required Information**:
   - Tenant ID (Directory ID)
   - Application (Client) ID
   - Client Secret

### 2. System Configuration

#### Add Graph Credentials via API

```bash
POST /private/graph-credentials
Authorization: Bearer <admin_token>
Content-Type: application/json

{
  "lf_id": 123,
  "tenant_id": "c294f073-4dd8-42a8-ac2c-028a2c9d9983",
  "client_id": "c06acc85-fd60-4f49-b87e-10e2ef62222b",
  "client_secret": "****************************************"
}
```

#### Test Connection

```bash
POST /private/graph-credentials/123/test
Authorization: Bearer <admin_token>
```

## API Endpoints

### Graph Credentials Management

- `POST /private/graph-credentials` - Add credentials
- `GET /private/graph-credentials/{lf_id}` - Get credentials (without secret)
- `PUT /private/graph-credentials/{lf_id}` - Update credentials
- `DELETE /private/graph-credentials/{lf_id}` - Delete credentials
- `GET /private/graph-credentials/all` - Get all credentials (Admin only)
- `POST /private/graph-credentials/{lf_id}/test` - Test connection
- `GET /private/graph-credentials/{lf_id}/provider-info` - Get provider info

### Enhanced User Management

- `POST /private/user/getLawfirmDetailsWithGraph` - Get law firm details with Graph status

## Usage Examples

### Sending Email with Enhanced Functions

```javascript
import { mailService } from "#services";

// Send temp password email with Graph API support
const result = await mailService.sendEnhancedTempPassword(
  "<EMAIL>",
  "tempPassword123",
  "John",
  123, // law firm ID
  "<EMAIL>" // user ID for Graph API
);

// Send case email with Graph API support
const mailData = {
  email: "<EMAIL>",
  link: "https://propero.co.uk/case/123",
  first_name: "Jane",
  lf_id: 123,
  user_id: "<EMAIL>",
  // ... other mail data
};

const result = await mailService.sendEnhancedCaseMail(mailData);
```

### Direct Mail Provider Usage

```javascript
import { mailProviderService } from "#services";

const emailData = {
  to: "<EMAIL>",
  subject: "Test Email",
  html: "<h1>Hello World</h1>",
  from: { name: "Propero", email: "<EMAIL>" }
};

// Send with automatic provider selection
const result = await mailProviderService.sendMail(emailData, 123, "<EMAIL>");

// Send with monitoring and retry options
const result = await mailProviderService.sendMailWithMonitoring(
  emailData, 
  123, 
  "<EMAIL>",
  { retryCount: 3, fallbackToSendGrid: true }
);
```

## Error Handling

The system implements comprehensive error handling:

1. **Retry Logic**: Automatic retries with exponential backoff
2. **Fallback Mechanism**: Falls back to SendGrid if Graph API fails
3. **Detailed Logging**: Comprehensive logging for debugging
4. **Monitoring**: Built-in monitoring and metrics collection

## Security Considerations

1. **Strong Encryption**: Client secrets are encrypted using AES-256-CBC with:
   - Unique random IV for each encryption operation
   - Proper key derivation and validation
   - Secure `createCipheriv()` implementation (not deprecated `createCipher()`)
2. **Access Control**: Role-based access to credential management
3. **Token Caching**: Access tokens are cached securely with proper expiration
4. **Audit Trail**: All credential changes are logged with user information
5. **Environment Security**: Encryption key must be set via environment variable in production

## Troubleshooting

### Common Issues

1. **Authentication Errors**:
   - Verify tenant ID, client ID, and client secret
   - Ensure admin consent is granted for API permissions
   - Check if application is enabled

2. **Permission Errors**:
   - Verify `Mail.Send` permission is granted
   - Ensure the user exists in the tenant
   - Check if the user has a mailbox

3. **Fallback Not Working**:
   - Verify SendGrid API key is configured
   - Check if fallback is enabled in options

### Testing

Use the built-in test endpoints to verify configuration:

```bash
# Test Graph API connection
POST /private/graph-credentials/123/test

# Get provider information
GET /private/graph-credentials/123/provider-info
```

## Environment Variables

Add these environment variables for enhanced functionality:

```env
# Graph API encryption key (REQUIRED in production, minimum 32 characters)
GRAPH_ENCRYPTION_KEY=your-secure-32-character-encryption-key-here

# Frontend URL for email links
FRONTEND_URL=https://your-frontend-url.com

# Image URL for email assets
IMAGE_URL=https://your-image-url.com
```

### Generating a Secure Encryption Key

Generate a secure 32+ character encryption key:

```bash
# Using OpenSSL
openssl rand -base64 32

# Using Node.js
node -e "console.log(require('crypto').randomBytes(32).toString('base64'))"

# Using Python
python -c "import secrets; print(secrets.token_urlsafe(32))"
```

## Migration Guide

### From SendGrid Only to Hybrid

1. **Install Dependencies**: Already included in package.json
2. **Run Database Migration**: Execute the SQL schema creation
3. **Configure Graph Credentials**: Use the API endpoints to add credentials
4. **Update Mail Functions**: Replace existing mail functions with enhanced versions
5. **Test Integration**: Use test endpoints to verify functionality

### Backward Compatibility

The integration maintains full backward compatibility:
- Existing SendGrid functionality continues to work
- No changes required to existing mail function calls
- Enhanced functions are opt-in

## Performance Considerations

- **Token Caching**: Access tokens are cached to reduce API calls
- **Connection Pooling**: Reuses HTTP connections where possible
- **Async Processing**: All email operations are asynchronous
- **Retry Logic**: Intelligent retry with exponential backoff

## Monitoring and Metrics

The system provides detailed monitoring:
- Email send success/failure rates
- Provider usage statistics
- Response times and performance metrics
- Error tracking and alerting
